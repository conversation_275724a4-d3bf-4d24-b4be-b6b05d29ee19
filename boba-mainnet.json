
*/20 * * * * . $HOME/.bashrc; cd /mnt/indexer && npm run cron:PopulateGenesisParticipantsAnalysis 2>&1; npm run cron:PopulateGenesisParticipantsAggregation 2>&1 | /usr/bin/systemd-cat -t genesisParticipantsAnalysisAndAgg


 git branch -d api-param-update base-integration bot-apis degen-backend dune-buyback-api eth-genesis-integration featured fixes game-sdk genesis-integration genesis-token-details hedera-ecosystem lqa-confidence lqa-v2 main mindshare-test near-ai nlq-improvements only-data-changes production reown-appkit-login-check selection-logic-fix signal solana-feature solana-terminal-integration staging-env trending-api virtuals-solana-tokens
