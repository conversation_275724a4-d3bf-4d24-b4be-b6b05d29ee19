{"name": "Ingestor", "version": "0.0.1", "description": "This repository pulls dapp events and transactions to different data stores.", "main": "index.js", "scripts": {"ingestor": "node dist/bin/ingestor-start.js", "balance-tracker": "node dist/bin/balance-tracker.ts", "lint:ts": "eslint src -c .eslintrc.json --ext ts", "test": "echo \"Error: no test specified\" && exit 1", "build": "rm -rf dist && tsc && cp src/config/IngestorConfig.schema.json dist/config/ && cp src/config/whitelistToken.json dist/config/ && cp src/config/*.json dist/config/", "dev:ingestor": "npm run build && npm run ingestor", "cron:addNewTokens": "node dist/executables/crons/AddNewToken.js", "cron:updateTokenPrices": "node dist/executables/crons/UpdateTokenPrices.js", "cron:updateTokenPricesHistorical": "node dist/executables/crons/UpdateTokenPricesHistorical.js", "cron:populatePolygonContracts": "node dist/executables/crons/PopulatePolygonContracts.js", "cron:PopulateTokenCategories": "node dist/executables/crons/PopulateTokenCategories.js", "onetimer:populateDarkForestLeaderBoard": "node dist/executables/oneTimers/PopulateDarkForestLeaderBoard.js", "onetimer:populateCohortData": "node dist/executables/oneTimers/PopulateCohortData.js", "onetimer:sendAlertForFailedServices": "node dist/executables/oneTimers/SendAlertForFailedServices.js", "onetimer:DappSchemaOptimizerCLI": "node dist/executables/oneTimers/DappSchemaOptimizerCLI.js", "onetimer:MigrateDataFromPostgresToClickhouse": "node dist/executables/oneTimers/DataMigrationToCh.js", "onetimer:PopulateToFieldCeloNetwork": "node dist/executables/oneTimers/PopulateCeloToField.js", "onetimer:MigratePriceOracleToCH": "node dist/executables/oneTimers/MigratePriceOracleToCH.js", "onetimer:MigrateData": "node dist/executables/oneTimers/MigrateData.js", "onetimer:PopulateSolanaTokenAddress": "node dist/executables/oneTimers/PopulateSolanaTokenAddress.js", "onetimer:PopulatePrototypeTokensTempForAnalysis": "node dist/executables/crons/PopulatePrototypeTokens.js", "onetimer:PopulateMissingHistoricalData": "node dist/executables/oneTimers/PopulateMissingHistoricalData.js", "onetimer:PopulateDevStakingAndVestingData": "node dist/executables/oneTimers/PopulateDevStakingAndVestingData.js", "onetimer:PopulateTokenCategoryAndAddress": "node dist/executables/oneTimers/PopulateTokenCategoryAndAddress.js", "onetimer:UpdateBaseAddressToCH": "node dist/executables/oneTimers/UpdateBaseAddressToCH.js", "onetimer:ValidateBaseAddressMigration": "node dist/executables/oneTimers/ValidateBaseAddressMigration.js", "cron:populateMaticValidatorPendingRewards": "node dist/executables/crons/PopulatePendingRewardsForMaticValidator.js", "cron:PopulateBitcannaData": "node dist/executables/crons/PopulateBitcannaData.js", "cron:PopulateCasperData": "node dist/executables/crons/PopulateCasperData.js", "cron:PopulateProviderPerformance": "node dist/executables/crons/PopulateProviderPerformance.js", "cron:ProcessSQLTransformation": "node dist/executables/crons/ProcessSQLTransformation.js", "cron:PopulateBitcannaMainnetNetworkData": "node dist/executables/crons/PopulateBitcannaMainnetNetworkData.js", "cron:populateTransformationForUserRetention": "node dist/executables/crons/PopulateTransformationForUserRetention.js", "cron:PopulateMoonbeamNetworksData": "node dist/executables/crons/PopulateMoonbeamNetworkData.js", "cron:PopulateApiData": "node dist/executables/crons/PopulateApiData.js", "cron:PopulateHistoricalTokenPrices": "node dist/executables/crons/PopulateHistoricalTokenPrices.js", "cron:PopulateProofOfReserveData": "node dist/executables/crons/PopulateProofOfReserveData.js", "cron:PopulateContractMethodData": "node dist/executables/crons/PopulateContractMethodData.js", "cron:PopulatePeaqNetworkData": "node dist/executables/crons/PopulatePeaqNetworkData.js", "cron:PopulateZetaNetworkData": "node dist/executables/crons/PopulateZetaNetworkData.js", "cron:OffChainProjectIngestor": "node dist/executables/crons/OffChainProjectIngestor.js", "cron:PopulateCeloNetworkData": "node dist/executables/crons/PopulateCeloNetworkData.js", "cron:PopulateCeloContracts": "node dist/executables/crons/PopulateCeloTimestamp.js", "cron:PopulateNordekNetworkData": "node dist/executables/crons/PopulateNordekNetworkData.js", "cron:DappSchemaOptimizer": "node dist/executables/crons/DappSchemaOptimizer.js", "cron:GraphQLIngestor": "node dist/executables/crons/GraphQLIngestor.js", "cron:PopulateBerachainNetworkData": "node dist/executables/crons/PopulateBerachainNetworkData.js", "cron:PopulateMantaNetworkData": "node dist/executables/crons/PopulateMantaNetworkData.js", "cron:PopulateStellarNetworkData": "node dist/executables/crons/PopulateStellarNetworkData.js", "cron:NetworkHealthChecker": "node dist/executables/crons/NetworkHealthChecker.js", "cron:PopulateAstarNetworkData": "node dist/executables/crons/PopulateAstarNetworkData.js", "cron:ApiKeyAccessWorker": "node dist/executables/crons/ApiKeyAccessWorker.js", "cron:PopulateHaqqNetworkData": "node dist/executables/crons/PopulateHaqqNetworkData.js", "cron:PopulateMultiversXNetworkData": "node dist/executables/crons/PopulateMultiversXData.js", "cron:IndexerProjectAdapter": "node dist/executables/crons/IndexerProjectAdapter.js", "cron:PopulateVaraNetworkData": "node dist/executables/crons/PopulateVaraNetworkData.js", "cron:PopulateCrossFiNetworkData": "node dist/executables/crons/PopulateCrossFiNetworkData.js", "cron:PopulateAVSMetadata": "node dist/executables/crons/PopulateAVSMetadata.js", "cron:PopulateEigenStrategiesTVL": "node dist/executables/crons/PopulateEigenStrategiesTVL.js", "cron:PopulateOrbitNetworksData": "node dist/executables/crons/PopulateOrbitNetworksData.js", "cron:PopulateMovementMainnetNetworkData": "node dist/executables/crons/PopulatePortoNetworkData.js", "cron:PopulateTonMiniAppsData": "node dist/executables/crons/PopulateTonMiniAppsData.js", "cron:PopulateArbitrumNetworkData": "node dist/executables/crons/PopulateArbitrumNetworkData.js", "cron:PopulateVirtualTokensData": "node dist/executables/crons/PopulateVirtualTokensData.js", "cron:PopulateTopTokenHolders": "node dist/executables/crons/PopulateTopTokenHolders.js", "cron:UpdateAndPopulateVirtualTokensMarketData": "node dist/executables/crons/UpdateAndPopulateVirtualTokensMarketData.js", "cron:PopulateVirtualTokensFirstBuyers": "node dist/executables/crons/PopulateFirstBuyersCron.js", "cron:PopulateDevBundleData": "node dist/executables/crons/PopulateDevBundleData.js", "cron:PopulateTokenStatistics": "node dist/executables/crons/PopulateTokenStatisticsCron.js", "cron:PopulateDevBundleTransactions": "node dist/executables/crons/PopulateDevBundleTransactions.js", "cron:PopulateDevWalletFundingTransactions": "node dist/executables/crons/PopulateDevWalletFundingTransactions.js", "cron:PopulateAgentGraduationAddressMap": "node dist/executables/crons/PopulateAgentGraduationAddressMap.js", "cron:PopulateGenesisAgentsMetadataFromVirtualsCH": "node dist/executables/crons/PopulateGenesisAgentsMetadataFromVirtualsCHCron.js", "cron:PopulateGenesisParticipantsAnalysis": "node dist/executables/crons/PopulateGenesisParticipantsAnalysis.js", "cron:PopulateGenesisParticipantsAggregation": "node dist/executables/crons/PopulateGenesisParticipantsAggregation.js", "cron:PopulateACPAgentsMetadataCH": "node dist/executables/crons/PopulateACPAgentsMetadataCHCron.js"}, "repository": {"type": "git", "url": "**************:dapplooker/indexer.git"}, "author": "<EMAIL>", "license": "ISC", "bugs": {"url": "https://github.com/dapplooker/indexer/issues"}, "homepage": "https://github.com/dapplooker/indexer#readme", "dependencies": {"@clickhouse/client": "0.2.9", "@duneanalytics/client-sdk": "0.2.4", "@polkadot/api": "9.3.3", "apollo-cache-inmemory": "1.6.6", "apollo-client": "2.6.10", "apollo-link-http": "1.5.17", "apollo-link-ws": "1.0.20", "async-lock": "1.4.1", "aws-sdk": "^2.1348.0", "axios": "0.26.0", "bech32": "2.0.0", "bignumber.js": "9.0.2", "cache": "file:src/externalPackage/cache", "commander": "6.2.1", "graphql": "16.3.0", "graphql-tag": "2.12.6", "html-to-text": "9.0.5", "ipfs-http-client": "55.0.0", "jsonschema": "1.4.0", "moment": "2.29.1", "moonbeam-types-bundle": "2.0.3", "mustache": "4.2.0", "node-fetch": "2.6.1", "papaparse": "5.3.2", "pg": "8.0.3", "pluralize": "8.0.0", "sequelize": "6.16.2", "subscriptions-transport-ws": "0.11.0", "typescript": "5.4.5", "web3": "1.7.0", "winston": "3.6.0", "ws": "8.5.0"}, "devDependencies": {"@polkadot/util": "8.4.1", "@polkadot/util-crypto": "8.4.1", "@types/async-lock": "1.4.2", "@types/bluebird": "3.5.36", "@types/chai": "4.3.0", "@types/chai-as-promised": "7.1.1", "@types/circular-json": "0.4.0", "@types/html-to-text": "9.0.0", "@types/mocha": "9.1.0", "@types/mustache": "4.1.2", "@types/node": "20.12.12", "@types/node-fetch": "2.6.1", "@types/pluralize": "0.0.29", "@types/request": "2.48.10", "@types/sinon": "10.0.11", "@types/validator": "13.7.1", "@types/ws": "8.5.1", "@typescript-eslint/eslint-plugin": "5.12.1", "@typescript-eslint/parser": "5.12.1", "@typescript-eslint/typescript-estree": "5.12.1", "assemblyscript": "github:AssemblyScript/assemblyscript", "chai": "4.3.6", "chai-as-promised": "7.1.1", "eslint": "8.10.0", "eslint-config-airbnb-base": "15.0.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-json": "3.1.0", "mocha": "9.2.1", "sinon": "13.0.1", "ts-node": "10.5.0"}}