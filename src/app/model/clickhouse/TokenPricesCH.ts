import Constant from '../../../config/Constant';
import Logger from '../../../lib/Logger';
import { ClickHouseConfig, CurrentTokenModelAttributes, FormattedTokenPrices } from '../../../lib/Types';
import CoingeckoConstants from '../../../lib/coingecko/CoingeckoConstants';
import ClickhouseClient from '../../../lib/dataStores/destination/ClickhouseClient';
import { ClickHouseDataTypes, ClickHouseIndexes } from '../../../lib/dataStores/destination/ClickHouseConstant';

export default class TokenPricesCH {
  private chConfig: ClickHouseConfig = {
    host: Constant.clickhouseHost,
    username: Constant.clickhouseUsername,
    password: Constant.clickhousePassword,
    database: Constant.clickhouseDatabasePriceOracle,
    max_open_connections: Constant.clickhouseMaxOpenConnection,
  };
  private clickhouse_client: ClickhouseClient;

  constructor() {
    const oThis = this;
    oThis.clickhouse_client = new ClickhouseClient(oThis.chConfig);
  }

  public async createTableIfNotExists() {
    try {
      const oThis = this;
      const client = oThis.clickhouse_client.getClient();
      const tableCreationQuery = await oThis.clickhouse_client.createTableQuery(
        oThis.chConfig.database,
        CoingeckoConstants.tokenPricesTableName,
        oThis.getSchema(),
        oThis.getIndexes()
      );
      Logger.info(`TokenPricesCH::getModelAndTokenPricesTableIfNotExist::Table creation query: ${tableCreationQuery}`);

      await client.command({
        query: tableCreationQuery
      });
      Logger.info(`TokenPricesCH::getModelAndTokenPricesTableIfNotExist::Table created Successfully in Clickhouse.`);
    } catch (e) {
      Logger.error(
        `TokenPricesCH::createTableIfNotExists::Error creating table. Exception ${JSON.stringify(
          e
        )}`
      );
    }
  }

  formattedTokenPrices(tokenPrice: CurrentTokenModelAttributes): any {
    return {
      token_id: tokenPrice.tokenId,
      token_name: tokenPrice.tokenSymbol,
      token_symbol: tokenPrice.tokenName,
      usd_price: tokenPrice.usdPrice,
      ethereum_address: tokenPrice.ethereumAddress,
      xdai_address: tokenPrice.xdaiAddress,
      polygon_address: tokenPrice.polygonAddress,
      fantom_address: tokenPrice.fantomAddress,
      binance_chain_address: tokenPrice.binanceChainAddress,
      arbitrum_one_address: tokenPrice.arbitrumOneAddress,
      moonriver_address: tokenPrice.moonriverAddress,
      avalanche_address: tokenPrice.avalancheAddress,
      optimism_address: tokenPrice.optimismAddress,
      near_address: tokenPrice.nearAddress,
      moonbeam_address: tokenPrice.moonbeamAddress,
      celo_address: tokenPrice.celoAddress,
      total_volume: tokenPrice.totalVolume,
      total_supply: tokenPrice.totalSupply,
      circulating_supply: tokenPrice.circulatingSupply,
      market_cap: tokenPrice.marketCap,
      time: tokenPrice.time,
      decimal: tokenPrice.decimal,
    }
  }


  public async update(where: any, values: any) {
    const oThis = this;
    const client = oThis.clickhouse_client.getClient();

    const whereClause = Object.entries(where)
      .map(([key, value]) => `${key} = ${oThis.handleUndefined(value)}`)
      .join(' AND ');

    const setClause = oThis.createUpdatePart(values);

    const query = `
            ALTER TABLE ${oThis.chConfig.database}.${CoingeckoConstants.tokenPricesTableName}
            UPDATE ${setClause}
            WHERE ${whereClause}
        `;

    try {
      Logger.debug(`TokenPricesCH::update:: Running query on clickhouse: ${query}`);
      await client.query({ query: query, format: "JSONEachRow" });
      Logger.info(`TokenPricesCH::update:: Successfully updated ${CoingeckoConstants.tokenPricesTableName} rows`);
    } catch (error) {
      Logger.error(`TokenPricesCH::update:: Error updating records: ${error}`);
      throw error;
    }
  }

  private createUpdatePart(tableObject) {
    const oThis = this;
    const fieldsToUpdate = Object.keys(tableObject);
    const updatePart = fieldsToUpdate.map(field => {
      const value = oThis.handleUndefined(tableObject[field]);
      return `${field}=${value}`;
    }).join(', ');
    return updatePart;
  }

  private handleUndefined(value) {
    if (value === undefined) {
      return 'NULL';
    } else {
      return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
    }
  }

  public async getAllTokens(): Promise<Record<string, FormattedTokenPrices>> {
    const oThis = this;
    const client = oThis.clickhouse_client.getClient();
    const query = `
      SELECT 
        token_id,
        token_symbol,
        token_name,
        ethereum_address,
        polygon_address,
        fantom_address,
        xdai_address,
        binance_chain_address,
        arbitrum_one_address,
        moonriver_address,
        avalanche_address,
        optimism_address,
        near_address,
        moonbeam_address,
        celo_address,
        decimal
      FROM ${CoingeckoConstants.tokenPricesTableName}
    `;
    Logger.debug(`TokenPricesCH::updateDatabase:: Running query on clickhouse: ${query}`);
    const result = await client.query({ query: query });
    const resultJson: any = await result.json();
    const allTokensResult = resultJson.data;

    const tokenPricesCH: Record<string, any> = {};
    for (let i = 0; i < allTokensResult.length; i += 1) {
      const c = allTokensResult[i];
      tokenPricesCH[c.token_id] = {
        token_id: c.token_id,
        token_symbol: c.token_symbol,
        token_name: c.token_name,
        ...(c.ethereum_address ? { ethereum_address: c.ethereum_address } : undefined),
        ...(c.fantom_address ? { fantom_address: c.fantom_address } : undefined),
        ...(c.xdai_address ? { xdai_address: c.xdai_address } : undefined),
        ...(c.polygon_address ? { polygon_address: c.polygon_address } : undefined),
        ...(c.binance_chain_address ? { binance_chain_address: c.binance_chain_address } : undefined),
        ...(c.arbitrum_one_address ? { arbitrum_one_address: c.arbitrum_one_address } : undefined),
        ...(c.moonriver_address ? { moonriver_address: c.moonriver_address } : undefined),
        ...(c.avalanche_address ? { avalanche_address: c.avalanche_address } : undefined),
        ...(c.optimism_address ? { optimism_address: c.optimism_address } : undefined),
        ...(c.near_address ? { near_address: c.near_address } : undefined),
        ...(c.moonbeam_address ? { moonbeam_address: c.moonbeam_address } : undefined),
        ...(c.celo_address ? { celo_address: c.celo_address } : undefined),
        decimal: c.decimal,
      };
    }
    return tokenPricesCH;
  }

  public async bulkCreate(values: any[]) {
    const oThis = this;
    const client = oThis.clickhouse_client.getClient();
    const tableName = CoingeckoConstants.tokenPricesTableName;
    try {
      await client.insert({
        table: tableName,
        values: values,
        format: "JSONEachRow"
      });
      Logger.info(`TokenPricesCH::bulkCreate::Successfully inserted ${values.length} records`);
    } catch (error) {
      Logger.error(`TokenPricesCH::bulkCreate::Error inserting records, Error: ${JSON.stringify(error)}`);
      throw error;
    }
  }

  private getSchema() {
    return {
      token_id: {
        type: ClickHouseDataTypes.STRING,
        primaryKey: true,
      },
      token_symbol: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      ethereum_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      polygon_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      fantom_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      xdai_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      binance_chain_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      arbitrum_one_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      moonriver_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      avalanche_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      optimism_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      near_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      moonbeam_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      celo_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      base_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      solana_address: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      token_name: {
        type: ClickHouseDataTypes.STRING,
        allowNull: true,
      },
      total_supply: {
        type: ClickHouseDataTypes.Float64,
        allowNull: true,
      },
      circulating_supply: {
        type: ClickHouseDataTypes.Float64,
        allowNull: true,
      },
      usd_price: {
        type: ClickHouseDataTypes.Float64,
        allowNull: true,
      },
      total_volume: {
        type: ClickHouseDataTypes.Float64,
        allowNull: true,
      },
      market_cap: {
        type: ClickHouseDataTypes.Float64,
        allowNull: true,
      },
      time: {
        type: ClickHouseDataTypes.DateTime,
        allowNull: true,
      },
      decimal: {
        type: ClickHouseDataTypes.UInt8,
        allowNull: true,
      }
    };
  }

  private getIndexes(): ClickHouseIndexes[] {
    return [
      {
        fields: ["token_id"],
        type: 'minmax',
        name: "idx_token_prices_token_id"
      },
      {
        fields: ["time"],
        type: 'minmax',
        name: "idx_token_prices_time"
      }
    ];
  }
}
