import Constant from '../../../config/Constant';
import ClickhouseClient from '../../../lib/dataStores/destination/ClickhouseClient';
import { ClickHouseDataTypes, ClickHouseIndexes } from '../../../lib/dataStores/destination/ClickHouseConstant';
import Logger from '../../../lib/Logger';
import { ClickHouseConfig } from '../../../lib/Types';

export default class ACPAgentsMetadataCH {
    private chConfig: ClickHouseConfig = {
        host: Constant.clickhouseHost,
        username: Constant.clickhouseUsername,
        password: Constant.clickhousePassword,
        database: Constant.clickhouseDatabaseGenesisInsights,
        max_open_connections: Constant.clickhouseMaxOpenConnection,
    };
    private clickhouse_client: ClickhouseClient;

    constructor() {
        const oThis = this;
        oThis.clickhouse_client = new ClickhouseClient(oThis.chConfig);
    }

    public async createTableIfNotExists() {
        try {
            const oThis = this;
            const client = oThis.clickhouse_client.getClient();
            const tableCreationQuery = await oThis.clickhouse_client.createTableQuery(
                oThis.chConfig.database,
                Constant.acpAgentsMetadataConfig.sourceTable,
                oThis.getSchema(),
                oThis.getIndexes()
            );
            Logger.info(`ACPAgentsMetadataCH::createTableIfNotExists::Table creation query: ${tableCreationQuery}`);

            await client.command({
                query: tableCreationQuery
            });
            Logger.info(`ACPAgentsMetadataCH::createTableIfNotExists::Table created Successfully in Clickhouse.`);
        } catch (e) {
            Logger.error(
                `ACPAgentsMetadataCH::createTableIfNotExists::Error creating table. Exception ${JSON.stringify(
                    e
                )}`
            );
            throw e;
        }
    }

    public async update(where: any, values: any) {
        const oThis = this;
        const client = oThis.clickhouse_client.getClient();

        const whereClause = Object.entries(where)
            .map(([key, value]) => {
                if (Array.isArray(value)) {
                    // Handle arrays with IN operator
                    return `${key} IN (${value.join(',')})`;
                } else {
                    return `${key} = ${oThis.handleUndefined(value)}`;
                }
            })
            .join(' AND ');

        const setClause = oThis.createUpdatePart(values);

        const query = `
            ALTER TABLE ${oThis.chConfig.database}.${Constant.acpAgentsMetadataConfig.sourceTable}
            UPDATE ${setClause}
            WHERE ${whereClause}
        `;

        try {
            Logger.debug(`ACPAgentsMetadataCH::update:: Running query on clickhouse: ${query}`);
            await client.query({ query: query, format: "JSONEachRow" });
            Logger.info(`ACPAgentsMetadataCH::update:: Successfully updated ${Constant.acpAgentsMetadataConfig.sourceTable} rows`);
        } catch (error) {
            Logger.error(`ACPAgentsMetadataCH::update:: Error updating records: ${error}`);
            throw error;
        }
    }

    private createUpdatePart(tableObject) {
        const oThis = this;
        const fieldsToUpdate = Object.keys(tableObject);
        const updatePart = fieldsToUpdate.map(field => {
            const value = oThis.handleUndefined(tableObject[field]);
            return `${field}=${value}`;
        }).join(', ');
        return updatePart;
    }

    private handleUndefined(value) {
        if (value === undefined) {
            return 'NULL';
        } else {
            return typeof value === 'string' ? `'${value.replace(/'/g, "''")}'` : value;
        }
    }

    public async bulkCreate(values: any[]) {
        const oThis = this;
        const client = oThis.clickhouse_client.getClient();
        const tableName = Constant.acpAgentsMetadataConfig.sourceTable;
        try {
            await client.insert({
                table: tableName,
                values: values,
                format: "JSONEachRow"
            });
            Logger.info(`ACPAgentsMetadataCH::bulkCreate::Successfully inserted ${values.length} records`);
        } catch (error) {
            Logger.error(`ACPAgentsMetadataCH::bulkCreate::Error inserting records, Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async getAllExistingAgents(): Promise<any[]> {
        const oThis = this;
        const client = oThis.clickhouse_client.getClient();
        try {
            const query = `SELECT * FROM ${oThis.chConfig.database}.${Constant.acpAgentsMetadataConfig.sourceTable}`;
            Logger.info(`ACPAgentsMetadataCH::getAllExistingAgents::Query: ${query}`);
            const result = await client.query({ query: query, format: "JSONEachRow" });
            const rows = await result.json() as any[];
            Logger.debug(`ACPAgentsMetadataCH::getAllExistingAgents::Found ${rows.length} existing records`);
            return rows;
        } catch (error) {
            Logger.error(`ACPAgentsMetadataCH::getAllExistingAgents::Error fetching records, Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private getSchema() {
        return {
            id: {
                type: ClickHouseDataTypes.Int64,
                primaryKey: true,
                allowNull: false,
            },
            document_id: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            name: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            description: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            wallet_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            is_virtual_agent: {
                type: ClickHouseDataTypes.Boolean,
                allowNull: true
            },
            profile_pic: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true
            },
            category: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true
            },
            token_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true
            },
            owner_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true
            },
            cluster: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true
            },
            twitter_handle: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true
            },
            symbol: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            virtual_agent_id: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true
            },
            created_at: {
                type: ClickHouseDataTypes.DateTime,
                allowNull: true
            },
            updated_at: {
                type: ClickHouseDataTypes.DateTime,
                allowNull: true
            },
            published_at: {
                type: ClickHouseDataTypes.DateTime,
                allowNull: true
            },
            role: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true
            },
            successful_job_count: {
                type: ClickHouseDataTypes.Int32,
                allowNull: true
            },
            success_rate: {
                type: ClickHouseDataTypes.Float64,
                allowNull: true
            },
            unique_buyer_count: {
                type: ClickHouseDataTypes.Int32,
                allowNull: true
            },
            last_active_at: {
                type: ClickHouseDataTypes.DateTime,
                allowNull: true
            },
            is_self_custody_wallet: {
                type: ClickHouseDataTypes.Boolean,
                allowNull: true
            },
            processing_time: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true
            },
            has_graduated: {
                type: ClickHouseDataTypes.Boolean,
                allowNull: true
            },
            wallet_balance: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true
            }
        };
    }

    private getIndexes(): ClickHouseIndexes[] {
        return [
            {
                fields: ["token_address"],
                type: 'minmax',
                name: "idx_acp_agents_metadata_token_address"
            }
        ];
    }
}
