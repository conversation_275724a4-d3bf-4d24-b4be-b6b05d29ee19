import { DataTypes, InitOptions, Model, ModelIndexesOptions, WhereOptions } from "sequelize";
import Constant from "../../config/Constant";
import Logger from "../../lib/Logger";
import { GenesisParticipantsAggregationModelAttributes } from "../../lib/Types";

class SequelizeModel extends Model { }

export default class GenesisParticipantsAggregation {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        SequelizeModel.init(oThis.getSchema(), {
            ...initOptions,
            modelName: "GenesisParticipantsAggregation",
            tableName: "genesis_participants_aggregation",
            indexes: oThis.getIndexes(),
            schema: Constant.virtualAgentInsightsSchemaName,
            timestamps: false
        });
    }

    public async createTableIfNotExists() {
        try {
            await SequelizeModel.sync({ force: false });
        } catch (e) {
            Logger.error(`GenesisParticipantsAggregation::createTableIfNotExists::Error creating table. Exception ${JSON.stringify(e)}`);
        }
    }

    public async create(values: GenesisParticipantsAggregationModelAttributes) {
        try {
            await SequelizeModel.create(values);
        } catch (e) {
            Logger.error(`GenesisParticipantsAggregation::create::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(e);
        }
    }

    public async upsert(values: GenesisParticipantsAggregationModelAttributes) {
        const upsertResult: any = await SequelizeModel.upsert(values, { returning: true });
        if (upsertResult.length === 0 || upsertResult[0] < 1) {
            throw new Error("Upsert operation failed in genesis participant aggregation");
        }
    }

    public async update(where: WhereOptions, values: any) {
        try {
            const updateResult: any = await SequelizeModel.update(values, {
                where
            });
            Logger.debug(`GenesisParticipantsAggregation::update::Update operation successful in genesis participants aggregation: ${updateResult}`);
        } catch (e) {
            const error = {
                message: `Update operation failed in genesis participants aggregation: ${e.message}`,
                stack: e.stack,
                updateParams: { values: values, where: where }
            };
            throw new Error(JSON.stringify(error));
        }
    }

    public async bulkCreate(values: GenesisParticipantsAggregationModelAttributes[], options: any) {
        const result = await SequelizeModel.bulkCreate(values, { ignoreDuplicates: true, ...options });
        if (result.length !== values.length) {
            Logger.error(`Failed to insert records in genesis participants aggregation ${JSON.stringify(values)}`);
            throw new Error(`Failed bulk insert operation for genesis participants aggregation`);
        }
    }

    public async bulkUpdateOnDuplicate(values: GenesisParticipantsAggregationModelAttributes[], options?: any) {
        const result = await SequelizeModel.bulkCreate(values, {
            updateOnDuplicate: [
                "totalParticipants",
                "soldAll",
                "soldPartially",
                "holdingInitialAllocation",
                "boughtMore",
                "stakedAmount",
                "noInitialAllocation",
                "initialSupply",
                "currentSupply",
                "totalStakedAmount",
                "top25ParticipantsHolding",
                "updatedAt"
            ],
            ...options
        });
        if (result.length !== values.length) {
            Logger.error(`Failed to bulk update on duplicate records in genesis participants aggregation ${JSON.stringify(values)}`);
            throw new Error(`Failed bulk update on duplicate operation for genesis participants aggregation`);
        }
    }

    private getSchema() {
        return {
            tokenAddress: {
                type: DataTypes.STRING(42),
                allowNull: false,
                primaryKey: true
            },
            genesisId: {
                type: DataTypes.INTEGER,
                allowNull: false,
                unique: true
            },
            chain: {
                type: DataTypes.SMALLINT,
                allowNull: false
            },
            totalParticipants: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            soldAll: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            soldPartially: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            holdingInitialAllocation: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            boughtMore: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            stakedAmount: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            noInitialAllocation: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            initialSupply: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            currentSupply: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            totalStakedAmount: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            top25ParticipantsHolding: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [];
    }
}
