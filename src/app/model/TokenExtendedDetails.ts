import { DataTypes, InitOptions, Model, ModelIndexesOptions, Op, Sequelize, WhereOptions } from "sequelize";
import Constant from '../../config/Constant';
import Logger from "../../lib/Logger";
import VirtualsConstants from "../../lib/globalConstant/VirtualsConstants";
class SequelizeModel extends Model { }

export default class TokenExtendedDetails {

    public constructor(initOptions: InitOptions) {
        const oThis = this;
        SequelizeModel.init(oThis.getSchema(), {
            ...initOptions,
            modelName: "TokenExtendedDetails",
            tableName: "token_extended_details",
            indexes: oThis.getIndexes(),
            schema: Constant.pricingDbSchemaName,
            timestamps: false,
        });
    }

    public async createTableIfNotExists() {
        try {
            await SequelizeModel.sync({ force: false });
            Logger.info(`Token Extended Details Table Created Successfully.`)
        } catch (e) {
            Logger.error(`TokenExtendedDetails::createTableIfNotExists::Error creating table. Exception ${JSON.stringify(e)}`);
        }
    }

    public async create(values: any) {
        try {
            await SequelizeModel.create(values);
        } catch (e) {
            Logger.error(`TokenExtendedDetails::create::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(e);
        }
    }

    public async upsert(values: any) {
        const upsertResult: any = await SequelizeModel.upsert(values, { returning: true });
        if (upsertResult.length === 0 || upsertResult[0] < 1) {
            throw new Error("Upsert operation failed in TokenExtendedDetails table");
        }
    }

    public async update(where: WhereOptions, values: any) {
        try {
            const updateResult: any = await SequelizeModel.update(values, {
                where,
            });
        } catch (e) {
            const error = {
                message: `Update operation failed in TokenExtendedDetails table: ${e.message}`,
                stack: e.stack,
                updateParams: { values: values, where: where }
            };
            throw new Error(JSON.stringify(error));
        }
    }

    public async bulkCreate(values: any[]) {
        Logger.debug(`Bulk creating in tokenExtendedDetails::values: ${JSON.stringify(values)}`)
        const result = await SequelizeModel.bulkCreate(values, { ignoreDuplicates: true });
        if (result.length !== values.length) {
            Logger.error(`Failed to insert records in TokenExtendedDetails table ${JSON.stringify(values)}`);
            throw new Error(`Failed bulk insert operation for TokenExtendedDetails`);
        }
    }

    public async getLatestVirtualId(): Promise<any> {
        const result: any = await SequelizeModel.findOne({
            attributes: [
                [Sequelize.fn('MAX', Sequelize.cast(Sequelize.json('extra_data.virtual_id'), 'BIGINT')), 'max_virtual_id'],
            ],
            raw: true,
        });
        return result?.max_virtual_id || 0;
    }

    public async getLastProcessedTimestamp(statusType: number, dateColumn: string): Promise<any> {
        const lastRecord: any = await SequelizeModel.findOne({
            where: {
                status: statusType
            },
            order: [
                [dateColumn, 'desc'],
            ],
            raw: true
        });
        Logger.debug(`TokenExtendedDetails::getLastCreatedAt::lastRecord: ${JSON.stringify(lastRecord)}`);
        return lastRecord ? Math.floor(new Date(lastRecord?.[dateColumn]).getTime() / 1000) : null;
    }

    public async getAllTokensByDataPopulationSource(dataPopulationSource: number, chain?: number): Promise<any> {
        const oThis = this;
        const res = await SequelizeModel.findAll({
            where: {
                dataPopulationSource: dataPopulationSource,
                status: {
                    [Op.in]: [VirtualsConstants.status.SENTIENT, VirtualsConstants.status.GENESIS]
                },
                ...(chain ? { chain: chain } : {})
            },
            order: [
                ['createdAt', 'DESC'],
            ],
            raw: true
        })
        return res;
    }

    public async getTokenIdByAddress(tokenAddress: string): Promise<string | null> {
        const res: any = await SequelizeModel.findOne({
            attributes: ['tokenId'],
            where: {
                baseTokenAddress: {
                    [Op.iLike]: tokenAddress,
                },
            },
            raw: true,
        });
        return res?.tokenId || null;
    }

    public async getPaginatedData(limit: number, offset: number): Promise<any> {
        const oThis = this;
        const res: any = SequelizeModel.findAll({
            limit: limit,
            offset: offset,
            raw: true
        })
        return res;
    }

    public async getByStatus(status: number): Promise<any> {
        const oThis = this;
        const res = await SequelizeModel.findAll({
            attributes: ['tokenId', 'tempTokenId', 'baseTokenAddress'],
            where: {
                status: status
            },
            raw: true
        });
        return res;
    }

    private getSchema() {
        return {
            tokenId: {
                type: DataTypes.STRING,
                allowNull: false,
                primaryKey: true
            },
            baseTokenAddress: {
                type: DataTypes.STRING,
                allowNull: false
            },
            description: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            extraData: {
                type: DataTypes.JSON,
                allowNull: true
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
            },
            devWalletAddress: {
                type: DataTypes.STRING,
                allowNull: true
            },
            socials: {
                type: DataTypes.JSON,
                allowNull: true
            },
            imageUrl: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            dataPopulationSource: {
                type: DataTypes.SMALLINT,
                allowNull: false
            },
            coinGeckoCoinId: {
                type: DataTypes.STRING,
                allowNull: true
            },
            tempTokenId: {
                type: DataTypes.STRING,
                allowNull: true
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
            },
            status: {
                type: DataTypes.SMALLINT,
                allowNull: false
            },
            chain: {
                type: DataTypes.SMALLINT,
                allowNull: true
            },
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: true,
                fields: ['created_at', 'base_token_address']
            }
        ]
    }
}
