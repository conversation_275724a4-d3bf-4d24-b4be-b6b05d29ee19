import { DataTypes, InitOptions, Model, ModelIndexesOptions, QueryTypes, Sequelize, WhereOptions } from "sequelize";
import Constant from "../../config/Constant";
import Logger from "../../lib/Logger";
import { GenesisParticipantsAnalysisModelAttributes } from "../../lib/Types";

class SequelizeModel extends Model { }

export default class GenesisParticipantsAnalysis {
    public constructor(initOptions: InitOptions) {
        const oThis = this;
        SequelizeModel.init(oThis.getSchema(), {
            ...initOptions,
            modelName: "GenesisParticipantsAnalysis",
            tableName: "genesis_participants_analysis",
            indexes: oThis.getIndexes(),
            schema: Constant.virtualAgentInsightsSchemaName,
            timestamps: false
        });
    }

    public async createTableIfNotExists() {
        try {
            await SequelizeModel.sync({ force: false });
        } catch (e) {
            Logger.error(`GenesisParticipantsAnalysis::createTableIfNotExists::Error creating table. Exception ${JSON.stringify(e)}`);
        }
    }

    public async create(values: GenesisParticipantsAnalysisModelAttributes) {
        try {
            await SequelizeModel.create(values);
        } catch (e) {
            Logger.error(`GenesisParticipantsAnalysis::create::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(e);
        }
    }

    public async upsert(values: GenesisParticipantsAnalysisModelAttributes) {
        const upsertResult: any = await SequelizeModel.upsert(values, { returning: true });
        if (upsertResult.length === 0 || upsertResult[0] < 1) {
            throw new Error("Upsert operation failed in genesis participant analysis");
        }
    }

    public async update(where: WhereOptions, values: any) {
        try {
            const updateResult: any = await SequelizeModel.update(values, {
                where
            });
            Logger.debug(`GenesisParticipantsAnalysis::update::Update operation successful in genesis participants analysis: ${updateResult}`);
        } catch (e) {
            const error = {
                message: `Update operation failed in genesis participants analysis: ${e.message}`,
                stack: e.stack,
                updateParams: { values: values, where: where }
            };
            throw new Error(JSON.stringify(error));
        }
    }

    public async bulkCreate(values: GenesisParticipantsAnalysisModelAttributes[], options: any) {
        const result = await SequelizeModel.bulkCreate(values, { ignoreDuplicates: true, ...options });
        if (result.length !== values.length) {
            Logger.error(`Failed to insert records in genesis participants analysis ${JSON.stringify(values)}`);
            throw new Error(`Failed bulk insert operation for genesis participants analysis`);
        }
    }

    public async getUserTokenPairs(batchSize: number, offset: number, chain: number) {
        try {
            const userTokenPairs = await SequelizeModel.findAll({
                attributes: ["userAddress", "tokenAddress"],
                where: {
                    chain: chain
                },
                limit: batchSize,
                offset: offset,
                order: [["userAddress", "ASC"], ["tokenAddress", "ASC"]]
            });
            return userTokenPairs.map((pair: any) => ({
                userAddress: pair.userAddress,
                tokenAddress: pair.tokenAddress
            }));
        } catch (e) {
            Logger.error(`GenesisParticipantsAnalysis::getUserTokenPairs::Error getting user token pairs. Exception ${JSON.stringify(e)}`);
            throw e;
        }
    }

    public async getUniqueGenesisIds() {
        try {
            const genesisIds = await SequelizeModel.findAll({
                attributes: ["genesisId"],
                group: ["genesisId"]
            });
            return genesisIds.map((genesisId: any) => genesisId.genesisId);
        } catch (e) {
            Logger.error(`GenesisParticipantsAnalysis::getUniqueGenesisIds::Error getting unique genesis ids. Exception ${JSON.stringify(e)}`);
            throw e;
        }
    }

    public async runQuery(query: string) {
        try {
            const result = await SequelizeModel.sequelize.query(query, { type: QueryTypes.SELECT });
            return result;
        } catch (e) {
            Logger.error(`GenesisParticipantsAggregation::runQuery::Error running query. Exception ${JSON.stringify(e)}`);
            throw e;
        }
    }

    public getSequelize(): Sequelize {
        return SequelizeModel.sequelize;
    }

    private getSchema() {
        return {
            userAddress: {
                type: DataTypes.STRING(42),
                allowNull: false,
                primaryKey: true
            },
            tokenAddress: {
                type: DataTypes.STRING(42),
                allowNull: false,
                primaryKey: true
            },
            genesisId: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            pointsCommitted: {
                type: DataTypes.DECIMAL(30, 0),
                allowNull: true
            },
            virtualsCommitted: {
                type: DataTypes.DECIMAL(10, 5),
                allowNull: true
            },
            virtualsRefunded: {
                type: DataTypes.DECIMAL(10, 5),
                allowNull: true
            },
            initialTokenAllocation: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            initialAllocationClaimedAt: {
                type: DataTypes.DATE,
                allowNull: true
            },
            currentTokenBalance: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            stakedTokenAmount: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            firstStakedAt: {
                type: DataTypes.DATE,
                allowNull: true
            },
            chain: {
                type: DataTypes.SMALLINT,
                allowNull: false
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: false,
                fields: ["genesis_id"]
            }
        ];
    }
}
