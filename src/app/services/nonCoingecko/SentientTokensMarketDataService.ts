import Logger from "../../../lib/Logger";
import ResponseHelper from "../../../lib/ResponseHelper";
import { ErrorResponse, SuccessResponse } from "../../../lib/Types";
import NetworkBase from "../../../lib/NetworkBase";
import moment from "moment-timezone";
import Container from "../../../lib/Container";
import ExtendedTokensHistoricalData from "../../model/ExtendedTokensHistoricalData";
import TokenExtendedDetails from "../../model/TokenExtendedDetails";
import CoingeckoConstants from "../../../lib/coingecko/CoingeckoConstants";
import VirtualWrapper from "../../../lib/VirtualWrapper/VirtualWrapper";
import CoingeckoWrapper from "../../../lib/coingecko/requests/Wrapper";
import TokenPrices from "../../model/TokenPrices";
import HistoricalTokenPrices from "../../model/HistoricalTokenPrices";
import DateTimeUtil from "../../../lib/DateTime";
import TokenCategoryMap from "../../model/TokenCategoryMap";
import { Op } from "sequelize";
import { DexScreenerHelper } from "../../../lib/helpers/DexScreenerHelper";
import VirtualsConstants from "../../../lib/globalConstant/VirtualsConstants";

/**
 * Service Notes:
 * - Populate and update non-CoinGecko sentient tokens historical, extended historical and token prices table data.
 * - Populate CoinGecko supported tokens historical data ONLY in extended historical data table.
 *   (For CoinGecko supported tokens we have dedicated crons to maintain historical and update current prices)
 */
export default class SentientTokensMarketDataService extends NetworkBase {

    private extendedTokensHistoricalDataModel: ExtendedTokensHistoricalData;

    private tokenExtendedDetailsModel: TokenExtendedDetails;

    private tokenPricesModel: TokenPrices;

    private historicalTokenPricesModel: HistoricalTokenPrices;

    private tokenCategoryMapModel: TokenCategoryMap;

    private currentDateTime: string;

    public constructor(params: {}) {
        super(params);
        const oThis = this;
        oThis.currentDateTime = moment().toISOString();
        oThis.extendedTokensHistoricalDataModel = Container.get().models.extendedTokensHistoricalData;
        oThis.tokenExtendedDetailsModel = Container.get().models.tokenExtendedDetails;
        oThis.tokenPricesModel = Container.get().models.tokenPrices;
        oThis.historicalTokenPricesModel = Container.get().models.historicalTokenPrices;
        oThis.tokenCategoryMapModel = Container.get().models.tokenCategoryMap;
    }

    public async perform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;

        await oThis.extendedTokensHistoricalDataModel.createTableIfNotExists();

        await oThis.processNonCoingeckoSourceSentientTokens(VirtualsConstants.chain.BASE);

        await oThis.processNonCoingeckoSourceSentientTokens(VirtualsConstants.chain.ETHEREUM);

        await oThis.processCoingeckoSourceSentientTokens();

        return oThis.prepareResponse();
    }

    /**
     * Fetch market data for non-CoinGecko supported tokens from GeckoTerminal & DexScanner.
     * Update/Populate in token_prices, historical_token_prices and extended_tokens_historical_data tables.
     */
    private async processNonCoingeckoSourceSentientTokens(chain: number) {
        const oThis = this;
        try {
            Logger.debug(`SentientTokensMarketDataService::processNonCoingeckoSentientTokens::Fetching geckoterminal supported sentient tokens from token extended details table...`);
            let extendedTokens: any[] = await oThis.tokenExtendedDetailsModel.getAllTokensByDataPopulationSource(CoingeckoConstants.dataSources[CoingeckoConstants.geckoTerminal], chain);
            Logger.info(`SentientTokensMarketDataService::processNonCoingeckoSentientTokens::Fetched geckoterminal supported sentient tokens: ${extendedTokens.length}`)
            const batchSize = CoingeckoConstants.geckoTerminalBatchSize;
            for (let i = 0; i < extendedTokens.length; i += batchSize) {
                const currentBatch = extendedTokens.slice(i, i + batchSize);
                let tokenAddressSet = new Set<string>();
                let tokenAddressToIdMap = new Map<string, string>();

                for (let i = 0; i < currentBatch.length; i++) {
                    const token = currentBatch[i];
                    const tokenAddress = token.baseTokenAddress?.toLowerCase();
                    tokenAddressSet.add(tokenAddress);
                    tokenAddressToIdMap.set(tokenAddress, token.tokenId);
                }

                let tokenAddresses: string = (Array.from(tokenAddressSet)).join(', ');

                const tokensAddressSetWithoutMcap = await oThis.fetchAndPopulateFromGeckoterminal(tokenAddresses, tokenAddressToIdMap, chain);

                await oThis.fetchAndPopulateFromDexScreener(Array.from(tokensAddressSetWithoutMcap), tokenAddressToIdMap, chain);
            }
        } catch (error) {
            if (error instanceof Error) {
                Logger.error(`SentientTokensMarketDataService::processNonCoingeckoSentientTokens::Error | Message: ${error.message} | Stack: ${error.stack}`);
            } else {
                Logger.error(`SentientTokensMarketDataService::processNonCoingeckoSentientTokens::Error | Unknown error: ${JSON.stringify(error)}`);
            }
        }
    }

    private async fetchAndPopulateFromGeckoterminal(tokenAddresses: string, tokenAddressToIdMap: Map<string, string>, chain: number): Promise<Set<string>> {
        const oThis = this;
        let cgTokenAddressToIdMap = new Map<string, string>();
        let tokensAddressSetWithoutMcap = new Set<string>();
        Logger.debug(`SentientTokensMarketDataService::fetchAndPopulateFromGeckoterminal::Fetching data from geckoterminal API...`)
        const network = VirtualsConstants.chainNameMap[chain];
        const response = await VirtualWrapper.fetchDataFromGeckoTerminal(network, tokenAddresses);
        for (let i = 0; i < response.length; i++) {
            let token = response[i];
            if (token.coingecko_coin_id) {
                cgTokenAddressToIdMap.set(token.address, token.coingecko_coin_id);
                await oThis.syncCoingeckoIdForListedTokens(token, tokenAddressToIdMap);
            } else {
                if (!token.market_cap_usd) {
                    tokensAddressSetWithoutMcap.add(token.address);
                }
            }
        }

        let formattedTokenPricesBatch = oThis.formatGeckoDataForTokenPrices(response, tokenAddressToIdMap, cgTokenAddressToIdMap);
        Logger.info(`SentientTokensMarketDataService::fetchAndPopulateFromGeckoterminal::Upserting batch in token prices table. BatchSize: ${formattedTokenPricesBatch.length}`);
        Logger.debug(`SentientTokensMarketDataService::fetchAndPopulateFromGeckoterminal::formattedTokenPricesBatch: ${JSON.stringify(formattedTokenPricesBatch)}`);
        await oThis.tokenPricesModel.bulkUpsert(formattedTokenPricesBatch);

        let formattedHistoricalPricesBatch = oThis.formatGeckoDataForHistoricalPrices(response, tokenAddressToIdMap, cgTokenAddressToIdMap);
        Logger.info(`SentientTokensMarketDataService::fetchAndPopulateFromGeckoterminal::Inserting batch in historical prices table. BatchSize: ${formattedHistoricalPricesBatch.length}`)
        Logger.debug(`SentientTokensMarketDataService::fetchAndPopulateFromGeckoterminal::formattedHistoricalPricesBatch: ${JSON.stringify(formattedHistoricalPricesBatch)}`);
        await oThis.ingestHistoricalPrices(formattedHistoricalPricesBatch);

        let formattedExtendedHistoricalDataBatch = oThis.formatGeckoDataForExtendedHistoricalData(response, tokenAddressToIdMap);
        Logger.info(`SentientTokensMarketDataService::fetchAndPopulateFromGeckoterminal::Inserting batch in extended historical data table. BatchSize: ${formattedExtendedHistoricalDataBatch.length}`)
        Logger.debug(`SentientTokensMarketDataService::fetchAndPopulateFromGeckoterminal::formattedExtendedHistoricalDataBatch: ${JSON.stringify(formattedExtendedHistoricalDataBatch)}`);
        await oThis.extendedTokensHistoricalDataModel.bulkCreate(formattedExtendedHistoricalDataBatch);

        return tokensAddressSetWithoutMcap;
    }

    private async syncCoingeckoIdForListedTokens(token: any, tokenAddressToIdMap: Map<string, string>) {
        const oThis = this;
        Logger.info(`SentientTokensMarketDataService::syncCoingeckoIdForListedTokens::Syncing coingecko available token id across all tables...`);
        try {
            let extendedTokenId = tokenAddressToIdMap.get(token.address.toLowerCase());
            Logger.info(`Coingecko Token Id: ${token.coingecko_coin_id} | Existing Token Id: ${extendedTokenId} | Token Address: ${token.address.toLowerCase()}`);

            const cgResponse = await new CoingeckoWrapper().getCoinData(token.coingecko_coin_id);
            Logger.info(`SentientTokensMarketDataService::syncCoingeckoIdForListedTokens::Updating in historical token prices table...`);
            await oThis.historicalTokenPricesModel.update(
                {
                    tokenId: extendedTokenId
                },
                {
                    ...(cgResponse?.symbol ? { tokenSymbol: cgResponse?.symbol } : {}),
                    tokenId: token.coingecko_coin_id
                }
            );
            Logger.info(`SentientTokensMarketDataService::syncCoingeckoIdForListedTokens::Updating in token prices table...`);
            await oThis.tokenPricesModel.updateVirtualTokenIds(
                {
                    tokenId: extendedTokenId
                },
                {
                    ...(cgResponse?.symbol ? { tokenSymbol: cgResponse?.symbol } : {}),
                    tokenId: token.coingecko_coin_id,
                }
            );

            Logger.info(`SentientTokensMarketDataService::syncCoingeckoIdForListedTokens::Updating in token category map table...`);
            await oThis.tokenCategoryMapModel.update(
                {
                    tokenId: extendedTokenId
                },
                {
                    tokenId: token.coingecko_coin_id
                }
            );

            Logger.info(`SentientTokensMarketDataService::syncCoingeckoIdForListedTokens::Updating in token extended details table...`);
            await oThis.tokenExtendedDetailsModel.update(
                {
                    baseTokenAddress: {
                        [Op.iLike]: token.address.toLowerCase()
                    }
                },
                {
                    dataPopulationSource: CoingeckoConstants.dataSources[CoingeckoConstants.coingecko],
                    coinGeckoCoinId: token.coingecko_coin_id,
                    tokenId: token.coingecko_coin_id
                }
            );
            Logger.info(`SentientTokensMarketDataService::syncCoingeckoIdForListedTokens::Updating in extended tokens historical data table...`);
            await oThis.extendedTokensHistoricalDataModel.update(
                {
                    tokenId: extendedTokenId
                },
                {
                    tokenId: token.coingecko_coin_id,
                    ...(cgResponse?.symbol ? { tokenSymbol: cgResponse?.symbol } : {}),
                }
            );
        } catch (error) {
            Logger.error(`SentientTokensMarketDataService::syncCoingeckoIdForListedTokens::Error sync coingecko token details for token address: ${token.address.toLowerCase()}, ${JSON.stringify(error)}`);
        }
    }

    private formatGeckoDataForTokenPrices(apiData: any[], tokenAddressToIdMap: Map<string, string>, coingeckoAddressToIdMap: Map<string, string>) {
        return apiData.map((token: any) => ({
            tokenId: coingeckoAddressToIdMap.get(token?.address) || tokenAddressToIdMap.get(token?.address),
            tokenSymbol: token?.symbol.toLowerCase(),
            baseAddress: token?.address,
            tokenName: token?.name,
            totalSupply: token?.total_supply || 0,
            usdPrice: token?.price_usd || 0,
            totalVolume: token?.volume_usd?.h24 || 0,
            marketCap: token?.market_cap_usd || 0,
            circulatingSupply: (parseFloat(token?.market_cap_usd) / parseFloat(token?.price_usd)) || 0,
            time: new Date().toISOString(),
            decimal: token?.decimals
        }));
    }

    private formatGeckoDataForHistoricalPrices(apiData: any[], tokenAddressToIdMap: Map<string, string>, coingeckoAddressToIdMap: Map<string, string>) {
        return apiData.map((token: any) => {
            return {
                tokenId: coingeckoAddressToIdMap.get(token?.address) || tokenAddressToIdMap.get(token?.address),
                tokenSymbol: token?.symbol.toLowerCase(),
                tokenName: token?.name,
                usdPrice: token?.price_usd || 0,
                totalVolume: token?.volume_usd?.h24 || 0,
                marketCap: token?.market_cap_usd || 0,
            };
        });
    }

    private async fetchAndPopulateFromDexScreener(tokenAddresses: string[], tokenAddressToIdMap: Map<string, string>, chain: number) {
        const oThis = this;
        Logger.info(`SentientTokensMarketDataService::fetchAndPopulateFromDexScreener::Fetching tokens data from dexscanner API... | Token BatchSize: ${tokenAddresses.length}`);
        try {
            const dexScreenerHelper = new DexScreenerHelper();
            const network = VirtualsConstants.chainNameMap[chain];
            const response = await dexScreenerHelper.getTokenMarketDataByAddresses(network, tokenAddresses);

            let formattedTokenPricesBatch = oThis.formatDexDataForTokenPrices(response, tokenAddressToIdMap);
            Logger.info(`SentientTokensMarketDataService::fetchAndPopulateFromDexScreener::Upserting batch in token prices table. BatchSize: ${formattedTokenPricesBatch.length}`);
            await oThis.tokenPricesModel.bulkUpsert(formattedTokenPricesBatch);

            let formattedHistoricalPricesBatch = oThis.formatDexDataForHistoricalPrices(response, tokenAddressToIdMap);
            Logger.info(`SentientTokensMarketDataService::fetchAndPopulateFromDexScreener::Inserting batch in historical prices table. BatchSize: ${formattedHistoricalPricesBatch.length}`)
            await oThis.ingestHistoricalPrices(formattedHistoricalPricesBatch);

            let formattedExtendedHistoricalDataBatch = oThis.formatDexDataForExtendedHistoricalData(response, tokenAddressToIdMap);
            Logger.info(`SentientTokensMarketDataService::fetchAndPopulateFromGeckoterminal::Inserting batch in extended historical data table. BatchSize: ${formattedExtendedHistoricalDataBatch.length}`)
            await oThis.extendedTokensHistoricalDataModel.bulkCreate(formattedExtendedHistoricalDataBatch);
        } catch (error: any) {
            Logger.error(`SentientTokensMarketDataService::fetchAndPopulateFromDexScreener::Error while processing data from dexscreener. Error: ${JSON.stringify(error)}`);
        }
    }

    private formatDexDataForTokenPrices(apiData: any[], tokenAddressToIdMap: Map<string, string>) {
        return apiData.flatMap((token: any) => {
            const tokenId = tokenAddressToIdMap.get(token?.baseToken?.address.toLowerCase());
            if (!tokenId) return []; // Skip if tokenId is not found
            return [{
                tokenId,
                tokenSymbol: token?.baseToken?.symbol.toLowerCase(),
                baseAddress: token?.baseToken?.address.toLowerCase(),
                tokenName: token?.baseToken?.name,
                usdPrice: token?.priceUsd || 0,
                totalVolume: token?.volume?.h24 || 0,
                marketCap: token?.marketCap || 0,
                circulatingSupply: (parseFloat(token?.marketCap) / parseFloat(token?.priceUsd)) || 0,
                time: new Date().toISOString(),
            }];
        });
    }

    private formatDexDataForHistoricalPrices(apiData: any[], tokenAddressToIdMap: Map<string, string>) {
        return apiData.map((token: any) => {
            return {
                tokenId: tokenAddressToIdMap.get(token?.baseToken?.address.toLowerCase()),
                tokenSymbol: token?.baseToken?.symbol.toLowerCase(),
                tokenName: token?.baseToken?.name,
                usdPrice: token?.priceUsd || 0,
                totalVolume: token?.volume?.h24 || 0,
                marketCap: token?.marketCap || 0,
            };
        });
    }

    private async ingestHistoricalPrices(tokensBatch: any[]) {
        const oThis = this;
        for (let i = 0; i < tokensBatch.length; i++) {
            let tokenData = tokensBatch[i];
            Logger.info(`SentientTokensMarketDataService::ingestHistoricalPrices::Checking if data exists for for tokenId: ${tokenData.tokenId} for current date.`);
            const lastInsertedDateTime = await oThis.historicalTokenPricesModel.getLastInsertedDateTimeByTokenId(tokenData.tokenId);
            const lastInsertedTimestamp = Math.floor(new Date(lastInsertedDateTime).getTime());
            const currentTimestamp = Date.now();

            if (DateTimeUtil.isSameDate(lastInsertedTimestamp, currentTimestamp)) {
                Logger.info(`SentientTokensMarketDataService::ingestHistoricalPrices::Data already exists for current date: ${lastInsertedDateTime}, updating latest values for token ${tokenData.tokenId}`)
                await this.historicalTokenPricesModel.update(
                    {
                        tokenId: tokenData.tokenId,
                        dateTime: lastInsertedDateTime
                    },
                    {
                        usdPrice: tokenData.usdPrice,
                        totalVolume: tokenData.totalVolume,
                        marketCap: tokenData.marketCap,
                        dateTime: new Date(currentTimestamp).toISOString(),
                    }
                );
            } else {
                Logger.info(`SentientTokensMarketDataService::ingestHistoricalPrices::No data available for current date, inserting new values for token ${tokenData.tokenId}`);
                await oThis.historicalTokenPricesModel.create(
                    {
                        dateTime: new Date(currentTimestamp).toISOString(),
                        tokenId: tokenData.tokenId,
                        tokenSymbol: tokenData.tokenSymbol,
                        tokenName: tokenData.tokenName,
                        usdPrice: tokenData.usdPrice,
                        totalVolume: tokenData.totalVolume,
                        marketCap: tokenData.marketCap,
                    }
                );
            }
        }
    }

    /**
     * Fetch market data for CoinGecko supported tokens from CoinGecko.
     * Populate ONLY in extended_tokens_historical_data table for 15m historical data.
     * (token_prices and historical_token_prices already maintaing by UpdateTokenPrices cron)
     */
    private async processCoingeckoSourceSentientTokens() {
        const oThis = this;
        try {
            Logger.info(`SentientTokensMarketDataService::processCoingeckoSourceSentientTokens::Fetching coingecko supported sentient tokens from token extended details table...`);
            let extendedTokens: any[] = await oThis.tokenExtendedDetailsModel.getAllTokensByDataPopulationSource(CoingeckoConstants.dataSources[CoingeckoConstants.coingecko]);
            Logger.info(`SentientTokensMarketDataService::processCoingeckoSourceSentientTokens::Fetched coingecko supported sentient tokens: ${extendedTokens.length}`)
            const batchSize = CoingeckoConstants.coingeckoBatchSize;
            for (let i = 0; i < extendedTokens.length; i += batchSize) {
                const currentBatch = extendedTokens.slice(i, i + batchSize);
                const currentBatchTokenIds = currentBatch.map(token => token.tokenId as string);
                if (i + batchSize >= extendedTokens.length) {
                    currentBatchTokenIds.push('virtual-protocol'); // Add in last iteration
                }
                Logger.debug(`SentientTokensMarketDataService::processCoingeckoSourceSentientTokens::Fetching market data from coingecko for current batch tokenIds: ${currentBatchTokenIds}`);
                const tokensMarketData = await new CoingeckoWrapper().getMarketDataByTokenIds(currentBatchTokenIds);
                Logger.debug(`SentientTokensMarketDataService::processCoingeckoSourceSentientTokens::Fetched market data from coingecko for current batch tokenIds: ${JSON.stringify(tokensMarketData)}`);

                const formattedExtendedHistoricalDataBatch = tokensMarketData.map((tmd: any) => {
                    return {
                        createdAt: oThis.currentDateTime,
                        tokenId: tmd.id,
                        symbol: tmd.symbol,
                        mcap: tmd.market_cap,
                        usdPrice: tmd.current_price
                    }
                });

                Logger.info(`SentientTokensMarketDataService::processCoingeckoSourceSentientTokens::Inserting batch in extended historical data table. BatchSize: ${formattedExtendedHistoricalDataBatch.length}`)
                await oThis.extendedTokensHistoricalDataModel.bulkCreate(formattedExtendedHistoricalDataBatch);
            }
        } catch (error) {
            if (error instanceof Error) {
                Logger.error(`SentientTokensMarketDataService::processCoingeckoSourceSentientTokens::Error | Message: ${error.message} | Stack: ${error.stack}`);
            } else {
                Logger.error(`SentientTokensMarketDataService::processCoingeckoSourceSentientTokens::Error | Unknown error: ${JSON.stringify(error)}`);
            }
        }
    }

    private formatGeckoDataForExtendedHistoricalData(apiData: any[], tokenAddressToIdMap: Map<string, string>) {
        const oThis = this;
        return apiData.map((token: any) => {
            return {
                createdAt: oThis.currentDateTime,
                tokenId: tokenAddressToIdMap.get(token?.address),
                symbol: token?.symbol.toLowerCase(),
                mcap: token?.market_cap_usd,
                usdPrice: token?.price_usd
            };
        });
    }

    private formatDexDataForExtendedHistoricalData(apiData: any[], tokenAddressToIdMap: Map<string, string>) {
        const oThis = this;
        return apiData.map((token: any) => {
            return {
                createdAt: oThis.currentDateTime,
                tokenId: tokenAddressToIdMap.get(token?.baseToken?.address.toLowerCase()),
                symbol: token?.baseToken?.symbol.toLowerCase(),
                mcap: token?.marketCap,
                usdPrice: token?.priceUsd
            };
        });
    }

    private prepareResponse(): SuccessResponse {
        const oThis = this;
        Logger.info(`SentientTokensMarketDataService::prepareResponse::Execution completed successfully`);
        return ResponseHelper.success({});
    }
}
