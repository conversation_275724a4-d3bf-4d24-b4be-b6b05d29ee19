import Container from "../../../../lib/Container";
import Logger from "../../../../lib/Logger";
import NetworkBase from "../../../../lib/NetworkBase";
import ResponseHelper from "../../../../lib/ResponseHelper";
import { ErrorResponse, GenesisParticipantsAggregationModelAttributes, SuccessResponse } from "../../../../lib/Types";
import GenesisParticipantsAggregation from "../../../model/GenesisParticipantsAggregation";
import GenesisParticipantsAnalysis from "../../../model/GenesisParticipantsAnalysis";

export default class GenesisParticipantsAggregationService extends NetworkBase {

    private genesisParticipantsAggregationModel: GenesisParticipantsAggregation;
    private genesisParticipantsAnalysisModel: GenesisParticipantsAnalysis;

    public constructor(params: any) {
        super(params);
        const oThis = this;
        oThis.genesisParticipantsAggregationModel = Container.get().models.genesisParticipantsAggregationModel;
        oThis.genesisParticipantsAnalysisModel = Container.get().models.genesisParticipantsAnalysisModel;
    }

    protected async perform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        Logger.info("GenesisParticipantsAggregationService::perform:: Starting Genesis Participants Aggregation service.");

        await oThis.genesisParticipantsAggregationModel.createTableIfNotExists();
        await oThis.populateGenesisParticipantsAggregationData();

        return oThis.prepareResponse();
    }

    private async populateGenesisParticipantsAggregationData(): Promise<void> {
        const oThis = this;
        const batchSize = 100;
        let dataToInsert: GenesisParticipantsAggregationModelAttributes[] = [];

        try {
            const genesisIds = await oThis.genesisParticipantsAnalysisModel.getUniqueGenesisIds();

            if (genesisIds.length === 0) {
                Logger.warn("GenesisParticipantsAggregationService::populateGenesisParticipantsAggregationData:: No genesis IDs found");
                return;
            }

            for (const genesisId of genesisIds) {
                Logger.info(`GenesisParticipantsAggregationService::populateGenesisParticipantsAggregationData:: Processing genesisId ${genesisId}`);
                const query = oThis.aggregationQuery(genesisId);
                const result = await oThis.genesisParticipantsAnalysisModel.runQuery(query);
                Logger.debug(`GenesisParticipantsAggregationService::populateGenesisParticipantsAggregationData:: Result for genesisId ${genesisId}: ${JSON.stringify(result)}`);

                const rawData = result[0];
                const transformedData = oThis.transformAggregatedData(rawData);
                dataToInsert.push(transformedData);

                if (dataToInsert.length >= batchSize) {
                    Logger.debug(`GenesisParticipantsAggregationService::populateGenesisParticipantsAggregationData:: Inserting ${dataToInsert.length} records: ${JSON.stringify(dataToInsert)}`);
                    await oThis.genesisParticipantsAggregationModel.bulkUpdateOnDuplicate(dataToInsert);
                    dataToInsert = [];
                }
            }

            if (dataToInsert.length > 0) {
                Logger.debug(`GenesisParticipantsAggregationService::populateGenesisParticipantsAggregationData:: Inserting ${dataToInsert.length} records: ${JSON.stringify(dataToInsert)}`);
                await oThis.genesisParticipantsAggregationModel.bulkUpdateOnDuplicate(dataToInsert);
            }
        } catch (error) {
            Logger.error(`GenesisParticipantsAggregationService::populateGenesisParticipantsAggregationData:: Error during processing: ${error.message}`);
            throw error;
        }
    }

    private transformAggregatedData(rawData: any): GenesisParticipantsAggregationModelAttributes {
        return {
            genesisId: Number(rawData.genesis_id),
            tokenAddress: rawData.token_address,
            chain: Number(rawData.chain),
            totalParticipants: Number(rawData.total_participants),
            soldAll: Number(rawData.sold_all),
            soldPartially: Number(rawData.sold_partially),
            holdingInitialAllocation: Number(rawData.holding_initial_allocation),
            boughtMore: Number(rawData.bought_more),
            stakedAmount: Number(rawData.staked_amount),
            noInitialAllocation: Number(rawData.no_initial_allocation),
            initialSupply: Number(rawData.initial_supply),
            currentSupply: Number(rawData.current_supply),
            totalStakedAmount: Number(rawData.total_staked_amount),
            top25ParticipantsHolding: Number(rawData.top25_participants_holding),
            updatedAt: new Date().toISOString()
        };
    }

    private aggregationQuery(genesisId: number) {
        return `
            SELECT
                genesis_id,
                token_address,
                chain,
                COUNT(*) as total_participants,

                -- Sold All: current + staked = 0
                SUM(CASE
                    WHEN COALESCE(current_token_balance, 0) + COALESCE(staked_token_amount, 0) = 0
                    THEN 1
                    ELSE 0
                END) as sold_all,

                -- Sold Part: current + staked < initial allocation (and > 0)
                SUM(CASE
                    WHEN COALESCE(current_token_balance, 0) + COALESCE(staked_token_amount, 0) > 0
                    AND COALESCE(current_token_balance, 0) + COALESCE(staked_token_amount, 0) < COALESCE(initial_token_allocation, 0)
                    THEN 1
                    ELSE 0
                END) as sold_partially,

                -- Holding: current + staked = initial allocation
                SUM(CASE
                    WHEN COALESCE(current_token_balance, 0) + COALESCE(staked_token_amount, 0) = COALESCE(initial_token_allocation, 0)
                    AND COALESCE(initial_token_allocation, 0) > 0
                    THEN 1
                    ELSE 0
                END) as holding_initial_allocation,

                -- Bought More: current + staked > initial allocation
                SUM(CASE
                    WHEN COALESCE(current_token_balance, 0) + COALESCE(staked_token_amount, 0) > COALESCE(initial_token_allocation, 0)
                    AND COALESCE(initial_token_allocation, 0) > 0
                    THEN 1
                    ELSE 0
                END) as bought_more,

                -- Staked: number of people who have staked (staked amount > 0)
                SUM(CASE
                    WHEN COALESCE(staked_token_amount, 0) > 0
                    THEN 1
                    ELSE 0
                END) as staked_amount,

                -- Additional metrics for validation
                SUM(CASE
                    WHEN initial_token_allocation IS NULL OR initial_token_allocation = 0
                    THEN 1
                    ELSE 0
                END) as no_initial_allocation,

                -- Supply and Amount Metrics
                SUM(COALESCE(initial_token_allocation, 0)) as initial_supply,
                SUM(COALESCE(current_token_balance, 0) + COALESCE(staked_token_amount, 0)) as current_supply,
                SUM(COALESCE(staked_token_amount, 0)) as total_staked_amount,

                -- Top 25 Participants Holding: current + staked amount of top 25 by initial allocation
                (SELECT SUM(COALESCE(current_token_balance, 0) + COALESCE(staked_token_amount, 0))
                 FROM (
                     SELECT current_token_balance, staked_token_amount
                     FROM virtuals_agent_insights.genesis_participants_analysis
                     WHERE genesis_id = ${genesisId}
                     ORDER BY COALESCE(initial_token_allocation, 0) DESC
                     LIMIT 25
                 ) top25
                ) as top25_participants_holding

            FROM virtuals_agent_insights.genesis_participants_analysis
            WHERE genesis_id = ${genesisId}
            GROUP BY genesis_id, token_address, chain
        `;
    }

    private prepareResponse(): SuccessResponse {
        Logger.info("GenesisParticipantsAggregationService::prepareResponse::Data sync completed, stopping service");
        return ResponseHelper.success({});
    }
}
