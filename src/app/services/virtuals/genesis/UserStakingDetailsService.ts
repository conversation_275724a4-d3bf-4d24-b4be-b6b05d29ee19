import Constant from "../../../../config/Constant";
import Logger from "../../../../lib/Logger";
import GenesisParticipantsAnalysisConstant from "../../../../lib/globalConstant/GenesisParticipantsAnalysisConstants";
import VirtualsConstants from "../../../../lib/globalConstant/VirtualsConstants";
import BaseGenesisDataService from "./BaseGenesisDataService";

export default class UserStakingDetailsService extends BaseGenesisDataService {

    private chainConfigs = [
        {
            name: 'Base',
            schemaId: Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
            chain: VirtualsConstants.chain.BASE,
            queryMethod: (userTokenPairs: { userAddress: string, tokenAddress: string }[], lastUpdatedAt: number) =>
                GenesisParticipantsAnalysisConstant.userStakingDetailsQuery(userTokenPairs, lastUpdatedAt)
        },
        {
            name: 'Ethereum',
            schemaId: Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS,
            chain: VirtualsConstants.chain.ETHEREUM,
            queryMethod: (userTokenPairs: { userAddress: string, tokenAddress: string }[], lastUpdatedAt: number) =>
                GenesisParticipantsAnalysisConstant.ethereumUserStakingDetailsQuery(userTokenPairs, lastUpdatedAt)
        }
    ];

    public async populateUserStakingDetailsData(): Promise<void> {
        Logger.info("UserStakingDetailsService::populateUserStakingDetailsData:: Starting User Staking Details Data service.");
        const oThis = this;

        for (const config of oThis.chainConfigs) {
            await oThis.processChainStakingDetailsData(config);
        }
    }

    private async processChainStakingDetailsData(config: any): Promise<void> {
        Logger.info(`UserStakingDetailsService::processChainStakingDetailsData:: Starting ${config.name} chain staking details data processing.`);
        const oThis = this;

        const lastProcessedTimestamp = await oThis.getUserStakingDetailsLastProcessedTimestamp(config.schemaId);
        await oThis.processUserStakingDetailsData(config, lastProcessedTimestamp);
    }

    private async getUserStakingDetailsLastProcessedTimestamp(schemaId: number): Promise<number> {
        const oThis = this;
        const schemaIds = [schemaId];

        const lastUpdatedTimestamps = await oThis.devBundleTrackerModel.getLastProcessedIds(
            Constant.DevBundleQueryTypes.USER_STAKING_DETAILS_TRACKER_FOR_PARTICIPANTS_DATA,
            schemaIds
        );

        return lastUpdatedTimestamps[schemaId] || 0;
    }

    private async processUserStakingDetailsData(config: any, lastUpdatedAt: number) {
        const oThis = this;
        let batchCount = 0;
        let offset = 0;
        let maxTimestamp = lastUpdatedAt;
        let hasMoreData = true;
        const batchSize = GenesisParticipantsAnalysisConstant.userTokenPairBatchSize;

        Logger.info(`UserStakingDetailsService::processUserStakingDetailsData:: Processing ${config.name} schema ${Constant.DevBundleSchemaNames[config.schemaId]} starting from timestamp ${lastUpdatedAt}`);

        try {
            while (hasMoreData) {
                batchCount++;

                const userTokenPairs = await oThis.genesisParticipantsAnalysisModel.getUserTokenPairs(batchSize, offset, config.chain);

                if (userTokenPairs.length === 0) {
                    hasMoreData = false;
                    Logger.info(`UserStakingDetailsService::processUserStakingDetailsData:: No more ${config.name} user-token pairs for schema ${Constant.DevBundleSchemaNames[config.schemaId]} after ${batchCount - 1} batches`);
                    break;
                }

                const batchResult = await oThis.processUserStakingDetailsBatch(config, userTokenPairs, lastUpdatedAt);

                if (!batchResult.hasData) {
                    Logger.info(`UserStakingDetailsService::processUserStakingDetailsData:: No ${config.name} staking details data for user-token batch ${batchCount} (${userTokenPairs.length} pairs) in schema ${Constant.DevBundleSchemaNames[config.schemaId]}`);
                } else {
                    Logger.info(`UserStakingDetailsService::processUserStakingDetailsData:: Batch ${batchCount} - Retrieved ${batchResult.stakingDetailRecords.length} ${config.name} staking detail records for ${userTokenPairs.length} user-token pairs in schema ${Constant.DevBundleSchemaNames[config.schemaId]}`);

                    if (batchResult.stakingDetailRecords.length > 0) {
                        maxTimestamp = Math.max(maxTimestamp, batchResult.maxTimestamp);

                        await oThis.saveUserStakingDetailsData(config.schemaId, maxTimestamp, batchResult.stakingDetailRecords);
                    }
                }

                offset += batchSize;
                hasMoreData = userTokenPairs.length >= batchSize;
            }

            Logger.info(`UserStakingDetailsService::processUserStakingDetailsData:: Completed processing ${config.name} staking details data for schema ${Constant.DevBundleSchemaNames[config.schemaId]} after ${batchCount} user-token pair batches`);
        } catch (error) {
            Logger.error(`UserStakingDetailsService::processUserStakingDetailsData:: Error processing ${config.name} staking details data for schema ${Constant.DevBundleSchemaNames[config.schemaId]} at batch ${batchCount}: ${error}`);
            throw error;
        }
    }

    private async processUserStakingDetailsBatch(config: any, userTokenPairs: { userAddress: string, tokenAddress: string }[], lastUpdatedAt: number) {
        const oThis = this;
        let maxTimestamp = lastUpdatedAt;

        const query = config.queryMethod(userTokenPairs, lastUpdatedAt);
        Logger.debug(`UserStakingDetailsService::processUserStakingDetailsBatch:: Executing ${config.name} query for ${userTokenPairs.length} user-token pairs with timestamp ${lastUpdatedAt}: ${query}`);

        const result = await oThis.client.query({ query, format: "JSONEachRow" });
        const data = await result.json();

        if (!data || data.length === 0) {
            return { hasData: false, stakingDetailRecords: [], maxTimestamp: null };
        }

        const stakingDetailRecords = [];
        for (const item of data) {
            const stakingDetailRecord = oThis.parseStakingDetailRecord(item);

            if (!stakingDetailRecord) {
                continue;
            }

            stakingDetailRecords.push(stakingDetailRecord.record);
            maxTimestamp = Math.max(maxTimestamp, stakingDetailRecord.timestamp);
        }

        return {
            hasData: stakingDetailRecords.length > 0,
            stakingDetailRecords,
            maxTimestamp
        };
    }

    private parseStakingDetailRecord(item: any) {
        const userAddress = item.userAddress;
        const tokenAddress = item.tokenAddress;
        const currentStakeAmount = item.currentStakeAmount / Math.pow(10, 18);
        const lastUpdatedAt = item.lastUpdatedAt;
        const firstStakeAt = item.firstStakeAt ? new Date(item.firstStakeAt * 1000) : null; // Convert from timestamp

        if (!userAddress || !tokenAddress || lastUpdatedAt === undefined) {
            Logger.warn(`UserStakingDetailsService::parseStakingDetailRecord:: Skipping invalid staking detail with userAddress: ${userAddress}, tokenAddress: ${tokenAddress}, lastUpdatedAt: ${lastUpdatedAt}`);
            Logger.debug(`UserStakingDetailsService::parseStakingDetailRecord:: Invalid staking detail record: ${JSON.stringify(item)}`);
            return null;
        }

        return {
            record: {
                userAddress,
                tokenAddress,
                stakedTokenAmount: currentStakeAmount,
                firstStakedAt: firstStakeAt
            },
            timestamp: lastUpdatedAt
        };
    }

    private async saveUserStakingDetailsData(schemaId: number, maxTimestamp: number, stakingDetailRecords: any[]) {
        const oThis = this;
        const currentTime = new Date();

        await oThis.executeInTransaction(
            async (transaction) => {
                for (const stakingDetailRecord of stakingDetailRecords) {
                    await oThis.genesisParticipantsAnalysisModel.update(
                        {
                            userAddress: stakingDetailRecord.userAddress,
                            tokenAddress: stakingDetailRecord.tokenAddress
                        },
                        {
                            stakedTokenAmount: stakingDetailRecord.stakedTokenAmount,
                            firstStakedAt: stakingDetailRecord.firstStakedAt,
                            updatedAt: currentTime,
                            transaction
                        }
                    );
                }

                await oThis.updateTrackerInTransaction(
                    schemaId,
                    Constant.DevBundleQueryTypes.USER_STAKING_DETAILS_TRACKER_FOR_PARTICIPANTS_DATA,
                    { lastRunId: maxTimestamp },
                    transaction
                );
            },
            "UserStakingDetailsService::saveUserStakingDetailsData"
        );

        Logger.info(`UserStakingDetailsService::saveUserStakingDetailsData:: Successfully updated ${stakingDetailRecords.length} records with staking details data and updated tracker (max timestamp: ${maxTimestamp})`);
    }
} 
