import Constant from "../../../../config/Constant";
import Logger from "../../../../lib/Logger";
import GenesisParticipantsAnalysisConstant from "../../../../lib/globalConstant/GenesisParticipantsAnalysisConstants";
import BaseGenesisDataService from "./BaseGenesisDataService";

export default class VirtualsRefundedDataService extends BaseGenesisDataService {

    public async populateVirtualsRefundedData(): Promise<void> {
        Logger.info("VirtualsRefundedDataService::populateVirtualsRefundedData:: Starting Virtuals Refunded Data service.");
        const oThis = this;
        const schemaId = Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS;

        const lastProcessedUuid = await oThis.getVirtualsRefundedDataLastProcessedUuid(schemaId);
        await oThis.processVirtualsRefundedData(schemaId, lastProcessedUuid);
    }

    private async getVirtualsRefundedDataLastProcessedUuid(schemaId: number): Promise<number> {
        const oThis = this;
        const schemaIds = [schemaId];

        const refundClaimedUuids = await oThis.devBundleTrackerModel.getLastProcessedIds(
            Constant.DevBundleQueryTypes.REFUND_CLAIMED_UUID_TRACKER_FOR_PARTICIPANTS_DATA,
            schemaIds
        );

        return refundClaimedUuids[schemaId] || 0;
    }

    private async processVirtualsRefundedData(schemaId: number, refundClaimedUuid: number) {
        const oThis = this;
        let hasMoreData = true;
        let offset = 0;
        let batchCount = 0;
        let maxRefundUuid = refundClaimedUuid;

        Logger.info(`VirtualsRefundedDataService::processVirtualsRefundedData:: Processing schema ${Constant.DevBundleSchemaNames[schemaId]} starting from refund UUID ${refundClaimedUuid}`);

        try {
            while (hasMoreData) {
                batchCount++;
                const batchResult = await oThis.processRefundBatch(refundClaimedUuid, offset);

                if (!batchResult.hasData) {
                    hasMoreData = false;
                    Logger.info(`VirtualsRefundedDataService::processVirtualsRefundedData:: No more refund data for schema ${Constant.DevBundleSchemaNames[schemaId]} after ${batchCount} batches`);
                    break;
                }

                Logger.info(`VirtualsRefundedDataService::processVirtualsRefundedData:: Batch ${batchCount} - Retrieved ${batchResult.refundRecords.length} valid refund records for schema ${Constant.DevBundleSchemaNames[schemaId]}`);

                if (batchResult.refundRecords.length > 0) {
                    maxRefundUuid = Math.max(maxRefundUuid, batchResult.maxRefundUuid);

                    await oThis.saveRefundData(schemaId, maxRefundUuid, batchResult.refundRecords);
                }

                offset += GenesisParticipantsAnalysisConstant.resultLimit;
                hasMoreData = batchResult.hasMoreData;
            }

            Logger.info(`VirtualsRefundedDataService::processVirtualsRefundedData:: Completed processing refund data for schema ${Constant.DevBundleSchemaNames[schemaId]} after ${batchCount} batches`);
        } catch (error) {
            Logger.error(`VirtualsRefundedDataService::processVirtualsRefundedData:: Error processing refund data for schema ${Constant.DevBundleSchemaNames[schemaId]} at batch ${batchCount}: ${error}`);
            throw error;
        }
    }

    private async processRefundBatch(refundClaimedUuid: number, offset: number = 0) {
        const oThis = this;

        const query = GenesisParticipantsAnalysisConstant.virtualsRefundedQuery(refundClaimedUuid, offset);
        Logger.debug(`VirtualsRefundedDataService::processRefundBatch:: Executing query with refund UUID ${refundClaimedUuid}, offset ${offset}: ${query}`);

        const result = await oThis.client.query({ query, format: "JSONEachRow" });
        const data = await result.json();

        if (!data || data.length === 0) {
            return { hasData: false, hasMoreData: false, refundRecords: [], maxRefundUuid: null };
        }

        const refundRecords = [];
        let maxRefundUuid = refundClaimedUuid;

        for (const item of data) {
            const refundRecord = oThis.parseRefundRecord(item);

            if (!refundRecord) {
                continue;
            }

            refundRecords.push(refundRecord.record);
            maxRefundUuid = Math.max(maxRefundUuid, refundRecord.uuid);
        }

        const hasMoreData = data.length >= GenesisParticipantsAnalysisConstant.resultLimit;

        return {
            hasData: true,
            hasMoreData,
            refundRecords,
            maxRefundUuid
        };
    }

    private parseRefundRecord(item: any) {
        const uuid = item.uuid;
        const userAddress = item.userAddress;
        const genesisId = item.genesisId;
        const refundAmount = item.refundAmount / Math.pow(10, 18);

        if (!userAddress || !genesisId || !uuid) {
            Logger.warn(`VirtualsRefundedDataService::parseRefundRecord:: Skipping invalid refund with userAddress: ${userAddress}, genesisId: ${genesisId}, uuid: ${uuid}`);
            Logger.debug(`VirtualsRefundedDataService::parseRefundRecord:: Invalid refund record: ${JSON.stringify(item)}`);
            return null;
        }

        return {
            record: {
                userAddress,
                genesisId,
                virtualsRefunded: refundAmount
            },
            uuid
        };
    }

    private async saveRefundData(schemaId: number, maxRefundUuid: number, refundRecords: any[]) {
        const oThis = this;
        const currentTime = new Date();

        await oThis.executeInTransaction(
            async (transaction) => {
                for (const refundRecord of refundRecords) {
                    await oThis.genesisParticipantsAnalysisModel.update(
                        {
                            userAddress: refundRecord.userAddress,
                            genesisId: refundRecord.genesisId
                        },
                        {
                            virtualsRefunded: refundRecord.virtualsRefunded,
                            updatedAt: currentTime,
                            transaction
                        }
                    );
                }

                await oThis.updateTrackerInTransaction(
                    schemaId,
                    Constant.DevBundleQueryTypes.REFUND_CLAIMED_UUID_TRACKER_FOR_PARTICIPANTS_DATA,
                    { lastRunId: maxRefundUuid },
                    transaction
                );
            },
            "VirtualsRefundedDataService::saveRefundData"
        );

        Logger.info(`VirtualsRefundedDataService::saveRefundData:: Successfully updated ${refundRecords.length} records with refund data and updated tracker (max UUID: ${maxRefundUuid})`);
    }
} 
