import Constant from "../../../../config/Constant";
import Container from "../../../../lib/Container";
import Logger from "../../../../lib/Logger";
import NetworkBase from "../../../../lib/NetworkBase";
import { ClickHouseConfig } from "../../../../lib/Types";
import ClickhouseClient from "../../../../lib/dataStores/destination/ClickhouseClient";
import DevBundleTrackerModel from "../../../model/DevBundleTrackerModel";
import GenesisParticipantsAnalysis from "../../../model/GenesisParticipantsAnalysis";

export default abstract class BaseGenesisDataService extends NetworkBase {
    protected chConfig: ClickHouseConfig = {
        host: Constant.clickhouseHost,
        username: Constant.clickhouseUsername,
        password: Constant.clickhousePassword,
        database: null,
        max_open_connections: Constant.clickhouseMaxOpenConnection
    };

    protected clickhouse_client: ClickhouseClient;
    protected client: any;
    protected genesisParticipantsAnalysisModel: GenesisParticipantsAnalysis;
    protected devBundleTrackerModel: DevBundleTrackerModel;

    public constructor(params: any) {
        super(params);
        const oThis = this;
        oThis.genesisParticipantsAnalysisModel = Container.get().models.genesisParticipantsAnalysisModel;
        oThis.devBundleTrackerModel = Container.get().models.devBundleTrackerModel;
        oThis.clickhouse_client = new ClickhouseClient(oThis.chConfig);
        oThis.client = oThis.clickhouse_client.getClient();
    }

    public async ensureTablesExist(): Promise<void> {
        const oThis = this;
        await oThis.genesisParticipantsAnalysisModel.createTableIfNotExists();
        await oThis.devBundleTrackerModel.createTableIfNotExists();
    }

    protected async updateTrackerInTransaction(
        schemaId: number,
        queryType: number,
        updateData: any,
        transaction: any
    ): Promise<void> {
        const oThis = this;
        const currentTime = new Date();

        await oThis.devBundleTrackerModel.updateTracker(
            schemaId,
            queryType,
            {
                lastRunAt: currentTime.toISOString(),
                ...updateData
            },
            transaction
        );
    }

    protected async executeInTransaction<T>(
        operation: (transaction: any) => Promise<T>,
        context: string
    ): Promise<T> {
        const oThis = this;
        const sequelize = oThis.genesisParticipantsAnalysisModel.getSequelize();
        const transaction = await sequelize.transaction();

        try {
            const result = await operation(transaction);
            await transaction.commit();
            return result;
        } catch (error) {
            await transaction.rollback();
            Logger.error(`${context}:: Transaction failed: ${error}`);
            throw error;
        }
    }
} 
