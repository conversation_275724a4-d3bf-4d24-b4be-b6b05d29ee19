import Logger from "../../../../lib/Logger";
import ResponseHelper from "../../../../lib/ResponseHelper";
import { ErrorResponse, SuccessResponse } from "../../../../lib/Types";
import BaseGenesisDataService from "./BaseGenesisDataService";
import ParticipantsDataService from "./ParticipantsDataService";
import VirtualsRefundedDataService from "./VirtualsRefundedDataService";
import InitialTokenAllocationDataService from "./InitialTokenAllocationDataService";
import UserTokenBalanceService from "./UserTokenBalanceService";
import UserStakingDetailsService from "./UserStakingDetailsService";

export default class GenesisParticipantsAnalysisService extends BaseGenesisDataService {
    private participantsDataService: ParticipantsDataService;
    private virtualsRefundedDataService: VirtualsRefundedDataService;
    private initialTokenAllocationDataService: InitialTokenAllocationDataService;
    private userTokenBalanceService: UserTokenBalanceService;
    private userStakingDetailsService: UserStakingDetailsService;

    public constructor(params: any) {
        super(params);
        const oThis = this;

        oThis.participantsDataService = new ParticipantsDataService(params);
        oThis.virtualsRefundedDataService = new VirtualsRefundedDataService(params);
        oThis.initialTokenAllocationDataService = new InitialTokenAllocationDataService(params);
        oThis.userTokenBalanceService = new UserTokenBalanceService(params);
        oThis.userStakingDetailsService = new UserStakingDetailsService(params);
    }

    protected async perform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        Logger.info("GenesisParticipantsAnalysisService::perform:: Starting Genesis Participants Analysis service.");

        await oThis.ensureTablesExist();

        await oThis.participantsDataService.populateGenesisParticipantsData();
        await oThis.virtualsRefundedDataService.populateVirtualsRefundedData();
        await oThis.initialTokenAllocationDataService.populateInitialTokenAllocationData();
        await oThis.userTokenBalanceService.populateUserTokenBalanceData();
        await oThis.userStakingDetailsService.populateUserStakingDetailsData();

        return oThis.prepareResponse();
    }

    private prepareResponse(): SuccessResponse {
        Logger.info("GenesisParticipantsAnalysisService::prepareResponse::Data sync completed, stopping service");
        return ResponseHelper.success({});
    }
}
