import Constant from "../../../../config/Constant";
import Logger from "../../../../lib/Logger";
import GenesisParticipantsAnalysisConstant from "../../../../lib/globalConstant/GenesisParticipantsAnalysisConstants";
import VirtualsConstants from "../../../../lib/globalConstant/VirtualsConstants";
import BaseGenesisDataService from "./BaseGenesisDataService";

export default class InitialTokenAllocationDataService extends BaseGenesisDataService {

    private chainConfigs = [
        {
            name: 'Base',
            schemaId: Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
            chain: VirtualsConstants.chain.BASE,
            queryMethod: (uuid: number, offset: number) =>
                GenesisParticipantsAnalysisConstant.initialTokenAllocationQuery(uuid, offset),
            idField: 'genesisId',
            hasTokenAddress: false,
            trackerId: Constant.DevBundleQueryTypes.INITIAL_TOKEN_ALLOCATION_UUID_TRACKER_FOR_PARTICIPANTS_DATA
        },
        {
            name: 'Base V2',
            schemaId: Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
            chain: VirtualsConstants.chain.BASE,
            queryMethod: (uuid: number, offset: number) =>
                GenesisParticipantsAnalysisConstant.v2InitialTokenAllocationQuery(uuid, offset),
            idField: 'tokenAddress',
            hasTokenAddress: true,
            trackerId: Constant.DevBundleQueryTypes.V2_INITIAL_TOKEN_ALLOCATION_UUID_TRACKER_FOR_PARTICIPANTS_DATA
        },
        {
            name: 'Ethereum',
            schemaId: Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS,
            chain: VirtualsConstants.chain.ETHEREUM,
            queryMethod: (uuid: number, offset: number) =>
                GenesisParticipantsAnalysisConstant.ethereumInitialTokenAllocationQuery(uuid, offset),
            idField: 'tokenAddress',
            hasTokenAddress: true,
            trackerId: Constant.DevBundleQueryTypes.INITIAL_TOKEN_ALLOCATION_UUID_TRACKER_FOR_PARTICIPANTS_DATA
        }
    ];

    public async populateInitialTokenAllocationData(): Promise<void> {
        Logger.info("InitialTokenAllocationDataService::populateInitialTokenAllocationData:: Starting Initial Token Allocation Data service.");
        const oThis = this;

        for (const config of oThis.chainConfigs) {
            await oThis.processChainInitialTokenAllocationData(config);
        }
    }

    private async processChainInitialTokenAllocationData(config: any): Promise<void> {
        Logger.info(`InitialTokenAllocationDataService::processChainInitialTokenAllocationData:: Starting ${config.name} chain initial token allocation data processing.`);
        const oThis = this;

        const lastProcessedUuid = await oThis.getInitialTokenAllocationDataLastProcessedUuid(config);
        await oThis.processInitialTokenAllocationData(config, lastProcessedUuid);
    }

    private async getInitialTokenAllocationDataLastProcessedUuid(config: any): Promise<number> {
        const oThis = this;
        const schemaIds = [config.schemaId];

        const initialTokenAllocationUuids = await oThis.devBundleTrackerModel.getLastProcessedIds(
            config.trackerId,
            schemaIds
        );

        return initialTokenAllocationUuids[config.schemaId] || 0;
    }

    private async processInitialTokenAllocationData(config: any, initialTokenAllocationUuid: number) {
        const oThis = this;
        let hasMoreData = true;
        let offset = 0;
        let batchCount = 0;
        let maxInitialTokenAllocationUuid = initialTokenAllocationUuid;

        Logger.info(`InitialTokenAllocationDataService::processInitialTokenAllocationData:: Processing ${config.name} schema ${Constant.DevBundleSchemaNames[config.schemaId]} starting from initial token allocation UUID ${initialTokenAllocationUuid}`);

        try {
            while (hasMoreData) {
                batchCount++;
                const batchResult = await oThis.processInitialTokenAllocationBatch(config, initialTokenAllocationUuid, offset);

                if (!batchResult.hasData) {
                    hasMoreData = false;
                    Logger.info(`InitialTokenAllocationDataService::processInitialTokenAllocationData:: No more ${config.name} initial token allocation data for schema ${Constant.DevBundleSchemaNames[config.schemaId]} after ${batchCount} batches`);
                    break;
                }

                Logger.info(`InitialTokenAllocationDataService::processInitialTokenAllocationData:: Batch ${batchCount} - Retrieved ${batchResult.initialTokenAllocationRecords.length} valid ${config.name} initial token allocation records for schema ${Constant.DevBundleSchemaNames[config.schemaId]}`);

                if (batchResult.initialTokenAllocationRecords.length > 0) {
                    maxInitialTokenAllocationUuid = Math.max(maxInitialTokenAllocationUuid, batchResult.maxInitialTokenAllocationUuid);

                    await oThis.saveInitialTokenAllocationData(config, maxInitialTokenAllocationUuid, batchResult.initialTokenAllocationRecords);
                }

                offset += GenesisParticipantsAnalysisConstant.resultLimit;
                hasMoreData = batchResult.hasMoreData;
            }

            Logger.info(`InitialTokenAllocationDataService::processInitialTokenAllocationData:: Completed processing ${config.name} initial token allocation data for schema ${Constant.DevBundleSchemaNames[config.schemaId]} after ${batchCount} batches`);
        } catch (error) {
            Logger.error(`InitialTokenAllocationDataService::processInitialTokenAllocationData:: Error processing ${config.name} initial token allocation data for schema ${Constant.DevBundleSchemaNames[config.schemaId]} at batch ${batchCount}: ${error}`);
            throw error;
        }
    }

    private async processInitialTokenAllocationBatch(config: any, initialTokenAllocationUuid: number, offset: number = 0) {
        const oThis = this;

        const query = config.queryMethod(initialTokenAllocationUuid, offset);
        Logger.debug(`InitialTokenAllocationDataService::processInitialTokenAllocationBatch:: Executing ${config.name} query with initial token allocation UUID ${initialTokenAllocationUuid}, offset ${offset}: ${query}`);

        const result = await oThis.client.query({ query, format: "JSONEachRow" });
        const data = await result.json();

        if (!data || data.length === 0) {
            return { hasData: false, hasMoreData: false, initialTokenAllocationRecords: [], maxInitialTokenAllocationUuid: null };
        }

        const initialTokenAllocationRecords = [];
        let maxInitialTokenAllocationUuid = initialTokenAllocationUuid;

        for (const item of data) {
            const initialTokenAllocationRecord = oThis.parseInitialTokenAllocationRecord(item, config);

            if (!initialTokenAllocationRecord) {
                continue;
            }

            initialTokenAllocationRecords.push(initialTokenAllocationRecord.record);
            maxInitialTokenAllocationUuid = Math.max(maxInitialTokenAllocationUuid, initialTokenAllocationRecord.uuid);
        }

        const hasMoreData = data.length >= GenesisParticipantsAnalysisConstant.resultLimit;

        return {
            hasData: true,
            hasMoreData,
            initialTokenAllocationRecords,
            maxInitialTokenAllocationUuid
        };
    }

    private parseInitialTokenAllocationRecord(item: any, config: any) {
        const uuid = item.uuid;
        const userAddress = item.userAddress;
        const idValue = item[config.idField]; // genesisId for Base, tokenAddress for Ethereum
        const claimedAmount = item.claimedAmount / Math.pow(10, 18);
        const claimedAt = item.claimedAt;

        if (!userAddress || !idValue || !uuid) {
            Logger.warn(`InitialTokenAllocationDataService::parseInitialTokenAllocationRecord:: Skipping invalid ${config.name} initial token allocation with userAddress: ${userAddress}, ${config.idField}: ${idValue}, uuid: ${uuid}`);
            Logger.debug(`InitialTokenAllocationDataService::parseInitialTokenAllocationRecord:: Invalid ${config.name} initial token allocation record: ${JSON.stringify(item)}`);
            return null;
        }

        const record: any = {
            userAddress,
            initialTokenAllocation: claimedAmount,
            initialAllocationClaimedAt: claimedAt
        };

        // For Base: use genesisId, For Ethereum: use tokenAddress
        if (config.hasTokenAddress) {
            record.tokenAddress = idValue;
        } else {
            record.genesisId = idValue;
        }

        return {
            record,
            uuid
        };
    }

    private async saveInitialTokenAllocationData(config: any, maxInitialTokenAllocationUuid: number, initialTokenAllocationRecords: any[]) {
        const oThis = this;
        const currentTime = new Date();

        await oThis.executeInTransaction(
            async (transaction) => {
                for (const initialTokenAllocationRecord of initialTokenAllocationRecords) {

                    const whereClause: any = {
                        userAddress: initialTokenAllocationRecord.userAddress
                    };

                    // For records with tokenAddress, use it; otherwise find by genesisId
                    if (initialTokenAllocationRecord.tokenAddress) {
                        whereClause.tokenAddress = initialTokenAllocationRecord.tokenAddress;
                    } else {
                        // For Base chain records, we need to find by genesisId and update the matching record
                        whereClause.genesisId = initialTokenAllocationRecord.genesisId;
                    }

                    await oThis.genesisParticipantsAnalysisModel.update(
                        whereClause,
                        {
                            initialTokenAllocation: initialTokenAllocationRecord.initialTokenAllocation,
                            initialAllocationClaimedAt: initialTokenAllocationRecord.initialAllocationClaimedAt,
                            updatedAt: currentTime,
                            transaction
                        }
                    );
                }

                await oThis.updateTrackerInTransaction(
                    config.schemaId,
                    config.trackerId,
                    { lastRunId: maxInitialTokenAllocationUuid },
                    transaction
                );
            },
            "InitialTokenAllocationDataService::saveInitialTokenAllocationData"
        );

        Logger.info(`InitialTokenAllocationDataService::saveInitialTokenAllocationData:: Successfully updated ${initialTokenAllocationRecords.length} records with initial token allocation data and updated tracker (max UUID: ${maxInitialTokenAllocationUuid})`);
    }
} 
