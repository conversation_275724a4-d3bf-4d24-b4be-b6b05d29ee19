import Constant from "../../../../config/Constant";
import Logger from "../../../../lib/Logger";
import GenesisParticipantsAnalysisConstant from "../../../../lib/globalConstant/GenesisParticipantsAnalysisConstants";
import VirtualsConstants from "../../../../lib/globalConstant/VirtualsConstants";
import BaseGenesisDataService from "./BaseGenesisDataService";

export default class GenesisParticipantsDataService extends BaseGenesisDataService {

    private chainConfigs = [
        {
            name: 'Base',
            schemaId: Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
            chain: VirtualsConstants.chain.BASE,
            queryMethod: (genesis: number, participant: number, offset: number) =>
                GenesisParticipantsAnalysisConstant.genesisParticipantsQuery(genesis, participant, offset),
            sequentialField: { genesis: 'genesisUpdatedBlock', participant: 'participantCreatedBlock' }
        },
        {
            name: 'Ethereum',
            schemaId: Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS,
            chain: VirtualsConstants.chain.ETHEREUM,
            queryMethod: (genesis: number, participant: number, offset: number) =>
                GenesisParticipantsAnalysisConstant.ethereumGenesisParticipantsQuery(genesis, participant, offset),
            sequentialField: { genesis: 'genesisCreatedBlock', participant: 'participantCreatedBlock' }
        }
    ];

    public async populateGenesisParticipantsData(): Promise<void> {
        Logger.info("GenesisParticipantsDataService::populateGenesisParticipantsData:: Starting Genesis Participants Data service.");
        const oThis = this;

        for (const config of oThis.chainConfigs) {
            await oThis.processChainParticipantsData(config);
        }
    }

    private async processChainParticipantsData(config: any): Promise<void> {
        Logger.info(`GenesisParticipantsDataService::processChainParticipantsData:: Starting ${config.name} chain participants data processing.`);
        const oThis = this;

        const lastProcessedBlocks = await oThis.getLastProcessedBlocks(config.schemaId);
        await oThis.processGenesisParticipantsData(config, lastProcessedBlocks.genesisBlock, lastProcessedBlocks.participantBlock);
    }

    private async getLastProcessedBlocks(schemaId: number): Promise<{ genesisBlock: number, participantBlock: number }> {
        const oThis = this;
        const schemaIds = [schemaId];

        const [genesisBlock, participantBlock] = await Promise.all([
            oThis.devBundleTrackerModel.getLastProcessedIds(
                Constant.DevBundleQueryTypes.GENESIS_CREATED_BLOCK_TRACKER_FOR_PARTICIPANTS_DATA,
                schemaIds
            ),
            oThis.devBundleTrackerModel.getLastProcessedIds(
                Constant.DevBundleQueryTypes.PARTICIPANT_CREATED_BLOCK_TRACKER_FOR_PARTICIPANTS_DATA,
                schemaIds
            )
        ]);

        return {
            genesisBlock: genesisBlock[schemaId] || 0,
            participantBlock: participantBlock[schemaId] || 0
        };
    }

    private async processGenesisParticipantsData(config: any, genesisBlock: number, participantBlock: number) {
        const oThis = this;
        let hasMoreData = true;
        let offset = 0;
        let batchCount = 0;
        let maxGenesisBlock = genesisBlock;
        let maxParticipantBlock = participantBlock;

        Logger.info(`GenesisParticipantsDataService::processGenesisParticipantsData:: Processing ${config.name} schema ${Constant.DevBundleSchemaNames[config.schemaId]} starting from genesis block ${genesisBlock} and participant block ${participantBlock}`);

        try {
            while (hasMoreData) {
                batchCount++;
                const batchResult = await oThis.processBatch(config, genesisBlock, participantBlock, offset);

                if (!batchResult.hasData) {
                    hasMoreData = false;
                    Logger.info(`GenesisParticipantsDataService::processGenesisParticipantsData:: No more ${config.name} data for schema ${Constant.DevBundleSchemaNames[config.schemaId]} after ${batchCount} batches`);
                    break;
                }

                Logger.info(`GenesisParticipantsDataService::processGenesisParticipantsData:: Batch ${batchCount} - Retrieved ${batchResult.participantRecords.length} valid ${config.name} records for schema ${Constant.DevBundleSchemaNames[config.schemaId]}`);

                if (batchResult.participantRecords.length > 0) {
                    maxGenesisBlock = Math.max(maxGenesisBlock, batchResult.maxGenesisBlock);
                    maxParticipantBlock = Math.max(maxParticipantBlock, batchResult.maxParticipantBlock);

                    await oThis.saveParticipantData(config.schemaId, maxGenesisBlock, maxParticipantBlock, batchResult.participantRecords);
                }

                offset += GenesisParticipantsAnalysisConstant.resultLimit;
                hasMoreData = batchResult.hasMoreData;
            }

            Logger.info(`GenesisParticipantsDataService::processGenesisParticipantsData:: Completed processing ${config.name} for schema ${Constant.DevBundleSchemaNames[config.schemaId]} after ${batchCount} batches`);
        } catch (error) {
            Logger.error(`GenesisParticipantsDataService::processGenesisParticipantsData:: Error processing ${config.name} schema ${Constant.DevBundleSchemaNames[config.schemaId]} at batch ${batchCount}: ${error}`);
            throw error;
        }
    }

    private async processBatch(config: any, genesisBlock: number, participantBlock: number, offset: number = 0) {
        const oThis = this;

        const query = config.queryMethod(genesisBlock, participantBlock, offset);
        Logger.debug(`GenesisParticipantsDataService::processBatch:: Executing ${config.name} query with genesis block ${genesisBlock}, participant block ${participantBlock}, offset ${offset}: ${query}`);

        const result = await oThis.client.query({ query, format: "JSONEachRow" });
        const data = await result.json();

        if (!data || data.length === 0) {
            return { hasData: false, hasMoreData: false, participantRecords: [], maxGenesisBlock: null, maxParticipantBlock: null };
        }

        const participantRecords = [];
        let maxGenesisBlock = genesisBlock;
        let maxParticipantBlock = participantBlock;

        for (const item of data) {
            const participantRecord = oThis.parseParticipantRecord(item, config);

            if (!participantRecord) {
                continue;
            }

            participantRecords.push(participantRecord.record);
            maxGenesisBlock = Math.max(maxGenesisBlock, participantRecord.genesisBlock);
            maxParticipantBlock = Math.max(maxParticipantBlock, participantRecord.participantBlock);
        }

        const hasMoreData = data.length >= GenesisParticipantsAnalysisConstant.resultLimit;

        return {
            hasData: true,
            hasMoreData,
            participantRecords,
            maxGenesisBlock,
            maxParticipantBlock
        };
    }

    private parseParticipantRecord(item: any, config: any) {
        const userAddress = item.userAddress;
        const tokenAddress = item.tokenAddress;
        const genesisId = item.genesisId;
        const pointsCommitted = item.pointsCommitted;
        const virtualsCommitted = item.virtualsCommitted / Math.pow(10, 18);
        const genesisBlock = item[config.sequentialField.genesis];
        const participantBlock = item[config.sequentialField.participant];
        const currentTime = new Date();

        if (!userAddress || !tokenAddress || !genesisId) {
            Logger.warn(`GenesisParticipantsDataService::parseParticipantRecord:: Skipping invalid ${config.name} participant with userAddress: ${userAddress}, tokenAddress: ${tokenAddress}, genesisId: ${genesisId}`);
            Logger.debug(`GenesisParticipantsDataService::parseParticipantRecord:: Invalid ${config.name} participant record: ${JSON.stringify(item)}`);
            return null;
        }

        return {
            record: {
                userAddress,
                tokenAddress,
                genesisId,
                pointsCommitted,
                virtualsCommitted,
                chain: config.chain,
                updatedAt: currentTime
            },
            genesisBlock,
            participantBlock
        };
    }

    private async saveParticipantData(schemaId: number, maxGenesisBlock: number, maxParticipantBlock: number, participantRecords: any[]) {
        const oThis = this;

        await oThis.executeInTransaction(
            async (transaction) => {
                await oThis.genesisParticipantsAnalysisModel.bulkCreate(participantRecords, { transaction });

                await Promise.all([
                    oThis.updateTrackerInTransaction(
                        schemaId,
                        Constant.DevBundleQueryTypes.GENESIS_CREATED_BLOCK_TRACKER_FOR_PARTICIPANTS_DATA,
                        { lastRunId: maxGenesisBlock },
                        transaction
                    ),
                    oThis.updateTrackerInTransaction(
                        schemaId,
                        Constant.DevBundleQueryTypes.PARTICIPANT_CREATED_BLOCK_TRACKER_FOR_PARTICIPANTS_DATA,
                        { lastRunId: maxParticipantBlock },
                        transaction
                    )
                ]);
            },
            "GenesisParticipantsDataService::saveParticipantData"
        );

        Logger.info(`GenesisParticipantsDataService::saveParticipantData:: Successfully saved ${participantRecords.length} participant records and updated trackers (genesis: ${maxGenesisBlock}, participant: ${maxParticipantBlock})`);
    }
}
