import Constant from "../../../../config/Constant";
import Logger from "../../../../lib/Logger";
import GenesisParticipantsAnalysisConstant from "../../../../lib/globalConstant/GenesisParticipantsAnalysisConstants";
import VirtualsConstants from "../../../../lib/globalConstant/VirtualsConstants";
import BaseGenesisDataService from "./BaseGenesisDataService";

export default class UserTokenBalanceService extends BaseGenesisDataService {

    private chainConfigs = [
        {
            name: 'Base',
            schemaId: Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
            chain: VirtualsConstants.chain.BASE,
            queryMethod: (userTokenPairs: { userAddress: string, tokenAddress: string }[], lastUpdatedAt: number) =>
                GenesisParticipantsAnalysisConstant.userTokenBalanceQuery(userTokenPairs, lastUpdatedAt)
        },
        {
            name: 'Ethereum',
            schemaId: Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS,
            chain: VirtualsConstants.chain.ETHEREUM,
            queryMethod: (userTokenPairs: { userAddress: string, tokenAddress: string }[], lastUpdatedAt: number) =>
                GenesisParticipantsAnalysisConstant.ethereumUserTokenBalanceQuery(userTokenPairs, lastUpdatedAt)
        }
    ];

    public async populateUserTokenBalanceData(): Promise<void> {
        Logger.info("UserTokenBalanceService::populateUserTokenBalanceData:: Starting User Token Balance Data service.");
        const oThis = this;

        for (const config of oThis.chainConfigs) {
            await oThis.processChainTokenBalanceData(config);
        }
    }

    private async processChainTokenBalanceData(config: any): Promise<void> {
        Logger.info(`UserTokenBalanceService::processChainTokenBalanceData:: Starting ${config.name} chain token balance data processing.`);
        const oThis = this;

        const lastProcessedTimestamp = await oThis.getUserTokenBalanceLastProcessedTimestamp(config.schemaId);
        await oThis.processUserTokenBalanceData(config, lastProcessedTimestamp);
    }

    private async getUserTokenBalanceLastProcessedTimestamp(schemaId: number): Promise<number> {
        const oThis = this;
        const schemaIds = [schemaId];

        const lastUpdatedTimestamps = await oThis.devBundleTrackerModel.getLastProcessedIds(
            Constant.DevBundleQueryTypes.USER_TOKEN_BALANCE_TRACKER_FOR_PARTICIPANTS_DATA,
            schemaIds
        );

        return lastUpdatedTimestamps[schemaId] || 0;
    }

    private async processUserTokenBalanceData(config: any, lastUpdatedAt: number) {
        const oThis = this;
        let batchCount = 0;
        let offset = 0;
        let maxTimestamp = lastUpdatedAt;
        let hasMoreData = true;
        const batchSize = GenesisParticipantsAnalysisConstant.userTokenPairBatchSize;

        Logger.info(`UserTokenBalanceService::processUserTokenBalanceData:: Processing ${config.name} schema ${Constant.DevBundleSchemaNames[config.schemaId]} starting from timestamp ${lastUpdatedAt}`);

        try {
            while (hasMoreData) {
                batchCount++;

                const userTokenPairs = await oThis.genesisParticipantsAnalysisModel.getUserTokenPairs(batchSize, offset, config.chain);

                if (userTokenPairs.length === 0) {
                    hasMoreData = false;
                    Logger.info(`UserTokenBalanceService::processUserTokenBalanceData:: No more ${config.name} user-token pairs for schema ${Constant.DevBundleSchemaNames[config.schemaId]} after ${batchCount - 1} batches`);
                    break;
                }

                const batchResult = await oThis.processUserTokenBalanceBatch(config, userTokenPairs, lastUpdatedAt);

                if (!batchResult.hasData) {
                    Logger.info(`UserTokenBalanceService::processUserTokenBalanceData:: No ${config.name} token balance data for user-token batch ${batchCount} (${userTokenPairs.length} pairs) in schema ${Constant.DevBundleSchemaNames[config.schemaId]}`);
                } else {
                    Logger.info(`UserTokenBalanceService::processUserTokenBalanceData:: Batch ${batchCount} - Retrieved ${batchResult.tokenBalanceRecords.length} ${config.name} token balance records for ${userTokenPairs.length} user-token pairs in schema ${Constant.DevBundleSchemaNames[config.schemaId]}`);

                    if (batchResult.tokenBalanceRecords.length > 0) {
                        maxTimestamp = Math.max(maxTimestamp, batchResult.maxTimestamp);

                        await oThis.saveUserTokenBalanceData(config.schemaId, maxTimestamp, batchResult.tokenBalanceRecords);
                    }
                }

                offset += batchSize;
                hasMoreData = userTokenPairs.length >= batchSize;
            }

            Logger.info(`UserTokenBalanceService::processUserTokenBalanceData:: Completed processing ${config.name} token balance data for schema ${Constant.DevBundleSchemaNames[config.schemaId]} after ${batchCount} user-token pair batches`);
        } catch (error) {
            Logger.error(`UserTokenBalanceService::processUserTokenBalanceData:: Error processing ${config.name} token balance data for schema ${Constant.DevBundleSchemaNames[config.schemaId]} at batch ${batchCount}: ${error}`);
            throw error;
        }
    }

    private async processUserTokenBalanceBatch(config: any, userTokenPairs: { userAddress: string, tokenAddress: string }[], lastUpdatedAt: number) {
        const oThis = this;
        let maxTimestamp = lastUpdatedAt;

        const query = config.queryMethod(userTokenPairs, lastUpdatedAt);
        Logger.debug(`UserTokenBalanceService::processUserTokenBalanceBatch:: Executing ${config.name} query for ${userTokenPairs.length} user-token pairs with timestamp ${lastUpdatedAt}: ${query}`);

        const result = await oThis.client.query({ query, format: "JSONEachRow" });
        const data = await result.json();

        if (!data || data.length === 0) {
            return { hasData: false, tokenBalanceRecords: [], maxTimestamp: null };
        }

        const tokenBalanceRecords = [];
        for (const item of data) {
            const tokenBalanceRecord = oThis.parseTokenBalanceRecord(item);

            if (!tokenBalanceRecord) {
                continue;
            }

            tokenBalanceRecords.push(tokenBalanceRecord.record);
            maxTimestamp = Math.max(maxTimestamp, tokenBalanceRecord.timestamp);
        }

        return {
            hasData: tokenBalanceRecords.length > 0,
            tokenBalanceRecords,
            maxTimestamp
        };
    }

    private parseTokenBalanceRecord(item: any) {
        const userAddress = item.userAddress;
        const tokenAddress = item.tokenAddress;
        const tokenBalance = item.tokenBalance / Math.pow(10, 18);
        const lastUpdatedAt = item.lastUpdatedAt;

        if (!userAddress || !tokenAddress || lastUpdatedAt === undefined) {
            Logger.warn(`UserTokenBalanceService::parseTokenBalanceRecord:: Skipping invalid token balance with userAddress: ${userAddress}, tokenAddress: ${tokenAddress}, lastUpdatedAt: ${lastUpdatedAt}`);
            Logger.debug(`UserTokenBalanceService::parseTokenBalanceRecord:: Invalid token balance record: ${JSON.stringify(item)}`);
            return null;
        }

        return {
            record: {
                userAddress,
                tokenAddress,
                currentTokenBalance: tokenBalance
            },
            timestamp: lastUpdatedAt
        };
    }

    private async saveUserTokenBalanceData(schemaId: number, maxTimestamp: number, tokenBalanceRecords: any[]) {
        const oThis = this;
        const currentTime = new Date();

        await oThis.executeInTransaction(
            async (transaction) => {
                for (const tokenBalanceRecord of tokenBalanceRecords) {
                    await oThis.genesisParticipantsAnalysisModel.update(
                        {
                            userAddress: tokenBalanceRecord.userAddress,
                            tokenAddress: tokenBalanceRecord.tokenAddress
                        },
                        {
                            currentTokenBalance: tokenBalanceRecord.currentTokenBalance,
                            updatedAt: currentTime,
                            transaction
                        }
                    );
                }

                await oThis.updateTrackerInTransaction(
                    schemaId,
                    Constant.DevBundleQueryTypes.USER_TOKEN_BALANCE_TRACKER_FOR_PARTICIPANTS_DATA,
                    { lastRunId: maxTimestamp },
                    transaction
                );
            },
            "UserTokenBalanceService::saveUserTokenBalanceData"
        );

        Logger.info(`UserTokenBalanceService::saveUserTokenBalanceData:: Successfully updated ${tokenBalanceRecords.length} records with token balance data and updated tracker (max timestamp: ${maxTimestamp})`);
    }
} 
