import fetch from 'node-fetch';
import Container from "../../../lib/Container";
import Constant from "../../../config/Constant";
import ClickhouseClient from "../../../lib/dataStores/destination/ClickhouseClient";
import { ClickHouseConfig, ErrorResponse, SuccessResponse } from "../../../lib/Types";
import DevBundleTrackerModel from "../../model/DevBundleTrackerModel";
import NetworkBase from "../../../lib/NetworkBase";
import ResponseHelper from "../../../lib/ResponseHelper";
import Logger from "../../../lib/Logger";
import DevWalletFundingTransactionsConstant from "../../../lib/globalConstant/DevWalletFundingTransactionsConstant";
import DevWalletFundingTransactions from "../../model/DevWalletFundingTransactions";
import Utils from "../../../lib/Utils";
import EtherscanApiKeyManager from "../../../lib/helpers/EtherscanApiKeyManager";

export default class DevWalletFundingTransactionsService extends NetworkBase {
    private chConfig: ClickHouseConfig = {
        host: Constant.clickhouseHost,
        username: Constant.clickhouseUsername,
        password: Constant.clickhousePassword,
        database: null,
        max_open_connections: Constant.clickhouseMaxOpenConnection
    };

    private clickhouse_client: ClickhouseClient;

    private client: any;

    private devBundleTrackerModel: DevBundleTrackerModel;

    private devWalletFundingTransactionsModel: DevWalletFundingTransactions;

    private readonly QUERY_TYPE = Constant.DevBundleQueryTypes.DEV_WALLET_FUNDING_TRANSACTIONS_QUERY;

    private SCHEMA_IDS: number[];

    private lastProcessedBlockNumbers: Record<number, number> = {};

    private processedTokensCount: number = 0;

    private etherscanKeyManager: EtherscanApiKeyManager;

    public constructor(params: { schema?: string }) {
        super(params);
        const oThis = this;
        oThis.clickhouse_client = new ClickhouseClient(oThis.chConfig);
        oThis.client = oThis.clickhouse_client.getClient();
        oThis.devBundleTrackerModel = Container.get().models.devBundleTrackerModel;
        oThis.devWalletFundingTransactionsModel = Container.get().models.devWalletFundingTransactionsModel;
        oThis.etherscanKeyManager = new EtherscanApiKeyManager();

        if (!params.schema) {
            oThis.SCHEMA_IDS = [
                Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE,
                Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
                Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS
            ];
        } else if (params.schema === 'virtual_ai_agents_migrated') {
            oThis.SCHEMA_IDS = [Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED];
        } else if (params.schema === 'virtuals_genesis_agents') {
            oThis.SCHEMA_IDS = [Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS];
        } else if (params.schema === 'virtuals_ethereum_agents') {
            oThis.SCHEMA_IDS = [Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS];
        } else if (params.schema === 'virtual_ai_agents_old') {
            oThis.SCHEMA_IDS = [Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD];
        } else {
            oThis.SCHEMA_IDS = [Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE];
        }
    }

    protected async perform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        Logger.info("DevWalletFundingTransactionsService::perform:: Starting Dev Wallet Funding Transactions service.");

        await oThis.devWalletFundingTransactionsModel.createTableIfNotExists();
        await oThis.devBundleTrackerModel.createTableIfNotExists();

        await oThis.getLastProcessedBlockNumbers();
        await oThis.populateDevWalletFundingTransactions();

        return oThis.prepareResponse();
    }

    private async getLastProcessedBlockNumbers() {
        const oThis = this;
        try {
            const lastProcessedIds = await oThis.devBundleTrackerModel.getLastProcessedIds(
                oThis.QUERY_TYPE,
                oThis.SCHEMA_IDS
            );
            oThis.lastProcessedBlockNumbers = lastProcessedIds;
            Logger.info(`DevWalletFundingTransactionsService::getLastProcessedBlockNumbers:: Last processed block numbers: ${JSON.stringify(oThis.lastProcessedBlockNumbers)}`);
        } catch (error) {
            Logger.error("DevWalletFundingTransactionsService::getLastProcessedBlockNumbers::Error getting last processed block numbers", error);
            throw error;
        }
    }

    private async populateDevWalletFundingTransactions() {
        const oThis = this;
        try {
            const maxRetries = 3;
            oThis.processedTokensCount = 0;

            for (const schemaId of oThis.SCHEMA_IDS) {
                const isMigratedSchema = schemaId === Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED;
                const lastProcessedBlockNumber = oThis.lastProcessedBlockNumbers[schemaId] || 0;
                if (isMigratedSchema) {
                    Logger.info(`DevWalletFundingTransactionsService::populateDevWalletFundingTransactions:: Processing migrated tokens for schema ${schemaId}`);
                    const tokens = await oThis.getMigratedTokens();
                    await oThis.processTokens(tokens, maxRetries, schemaId, lastProcessedBlockNumber);
                } else {
                    Logger.info(`DevWalletFundingTransactionsService::populateDevWalletFundingTransactions:: Processing tokens from database for schema ${schemaId}`);
                    let page = 0;
                    let hasMoreTokens = true;

                    while (hasMoreTokens) {
                        try {
                            const tokens = await oThis.getTokensFromDB(page, schemaId, lastProcessedBlockNumber);
                            if (tokens.length === 0) {
                                hasMoreTokens = false;
                                Logger.info(`DevWalletFundingTransactionsService::populateDevWalletFundingTransactions:: Completed processing with ${oThis.processedTokensCount} tokens processed for schema ${schemaId}`);
                                break;
                            }

                            Logger.info(`DevWalletFundingTransactionsService::populateDevWalletFundingTransactions:: Processing page ${page} with ${tokens.length} tokens for schema ${schemaId}`);
                            await oThis.processTokens(tokens, maxRetries, schemaId, lastProcessedBlockNumber);
                            page++;
                        } catch (error) {
                            Logger.error(`DevWalletFundingTransactionsService::populateDevWalletFundingTransactions:: Error processing page ${page} for schema ${schemaId}`, error);
                            throw error;
                        }
                    }
                }
            }
        } catch (error) {
            Logger.error("DevWalletFundingTransactionsService::populateDevWalletFundingTransactions::Error populating dev wallet funding transactions", error);
            throw error;
        }
    }

    private async processTokens(tokens: Array<{ tokenAddress: string, devAddress: string, createdBlockNumber: number }>, maxRetries: number, schemaId: number, lastProcessedBlockNumber: number) {
        const oThis = this;

        for (const token of tokens) {
            const { tokenAddress, devAddress, createdBlockNumber } = token;

            if (createdBlockNumber <= lastProcessedBlockNumber) {
                Logger.debug(`DevWalletFundingTransactionsService::processTokens:: Skipping token ${tokenAddress} with block number ${createdBlockNumber} as it has already been processed (latest processed: ${lastProcessedBlockNumber}) for schema ${schemaId}`);
                continue;
            }

            Logger.info(`DevWalletFundingTransactionsService::processTokens:: Processing token ${tokenAddress} with dev wallet ${devAddress} at block ${createdBlockNumber} for schema ${schemaId}`);

            let retryCount = 0;
            let success = false;

            while (retryCount < maxRetries && !success) {
                try {
                    const [nativeTransactions, erc20Transactions] = await Promise.all([
                        oThis.fetchNativeTransactions(devAddress, createdBlockNumber),
                        oThis.fetchERC20Transactions(devAddress, createdBlockNumber)
                    ]);

                    const formattedNativeTransactions = nativeTransactions
                        .filter(tx => tx.to?.toLowerCase() === devAddress.toLowerCase())
                        .map(tx => oThis.formatTransactions(tx, tokenAddress));

                    const formattedERC20Transactions = erc20Transactions
                        .filter(tx => tx.to?.toLowerCase() === devAddress.toLowerCase())
                        .map(tx => oThis.formatTransactions(tx, tokenAddress));

                    const allTransactions = [...formattedNativeTransactions, ...formattedERC20Transactions];

                    if (allTransactions.length > 0) {
                        await oThis.processTransactionsWithTracker(
                            allTransactions,
                            createdBlockNumber,
                            tokenAddress,
                            schemaId
                        );
                        Logger.info(`DevWalletFundingTransactionsService::processTokens:: Processed transactions for token ${tokenAddress} for schema ${schemaId}`);
                    } else {
                        // Even if there are no transactions, we still need to update the tracker to mark this token as processed
                        await oThis.devBundleTrackerModel.updateTracker(
                            schemaId,
                            oThis.QUERY_TYPE,
                            {
                                lastRunId: createdBlockNumber,
                                lastRunAt: new Date().toISOString()
                            }
                        );
                        Logger.info(`DevWalletFundingTransactionsService::processTokens:: Updated tracker for token ${tokenAddress} (no transactions) for schema ${schemaId}`);
                    }

                    oThis.processedTokensCount++;
                    success = true;
                    await Utils.sleep(200);
                } catch (error) {
                    retryCount++;
                    Logger.error(`DevWalletFundingTransactionsService::processTokens:: Error processing token ${tokenAddress} (retry ${retryCount}/${maxRetries}) for schema ${schemaId}: ${error.message}`);
                    if (retryCount >= maxRetries) {
                        Logger.error(`DevWalletFundingTransactionsService::processTokens:: Max retries reached for token ${tokenAddress} for schema ${schemaId}.`);
                        throw error;
                    } else {
                        await Utils.sleep(5000); // Wait 5 seconds before retrying
                    }
                }
            }
        }
    }

    private async getTokensFromDB(page: number, schemaId: number, lastProcessedBlockNumber: number) {
        const oThis = this;
        try {
            const query = DevWalletFundingTransactionsConstant.tokensQuery(page, schemaId, lastProcessedBlockNumber);
            Logger.debug(`DevWalletFundingTransactionsService::getTokensFromDB:: Fetching tokens after block ${lastProcessedBlockNumber} for schema ${schemaId}`);
            const result = await oThis.client.query({ query, format: "JSONEachRow" });
            const tokens = await result.json();
            return tokens.map(token => ({
                tokenAddress: token.pregraduated_token_address,
                devAddress: token.dev_wallet,
                createdBlockNumber: token.created_block_number
            }));
        } catch (error) {
            Logger.error("DevWalletFundingTransactionsService::getTokensFromDB::Error getting tokens", error);
            throw error;
        }
    }

    private async getMigratedTokens() {
        const migratedTokens = DevWalletFundingTransactionsConstant.migratedTokens;
        // Sort tokens by created block number in ascending order
        return [...migratedTokens].sort((a, b) =>
            (a.createdBlockNumber as number) - (b.createdBlockNumber as number)
        ).map(token => ({
            tokenAddress: token.tokenAddress as string,
            devAddress: token.devAddress as string,
            createdBlockNumber: token.createdBlockNumber as number
        }));
    }

    private async processTransactionsWithTracker(
        transactions: any[],
        launchBlock: number,
        pregraduatedTokenAddress: string,
        schemaId: number
    ): Promise<number> {
        const oThis = this;
        const sequelize = oThis.devWalletFundingTransactionsModel.getSequelize();
        const dbTransaction = await sequelize.transaction();

        try {
            await oThis.devWalletFundingTransactionsModel.bulkCreate(transactions, { transaction: dbTransaction });

            await oThis.devBundleTrackerModel.updateTracker(
                schemaId,
                oThis.QUERY_TYPE,
                {
                    lastRunId: launchBlock,
                    lastRunAt: new Date().toISOString()
                },
                dbTransaction
            );

            await dbTransaction.commit();
            Logger.info(`DevWalletFundingTransactionsService::processTransactionsWithTracker:: Successfully processed ${transactions.length} transactions for token ${pregraduatedTokenAddress} for schema ${schemaId}`);

            return launchBlock;
        } catch (error) {
            await dbTransaction.rollback();
            Logger.error(`DevWalletFundingTransactionsService::processTransactionsWithTracker:: Transaction failed with error: ${error.message}`);
            throw error;
        }
    }

    private async fetchNativeTransactions(devWalletAddress: string, launchBlock: number) {
        try {
            const startBlock = launchBlock - DevWalletFundingTransactionsConstant.numberOfBaseBlocksIn2Weeks;
            const endBlock = launchBlock;
            const apiKey = this.etherscanKeyManager.getNextKey();
            const apiUrl = `https://api.etherscan.io/v2/api?chainid=8453&module=account&action=txlist&address=${devWalletAddress}&startblock=${startBlock}&endblock=${endBlock}&apikey=${apiKey}`;

            const response = await fetch(apiUrl);
            const data = await response.json();
            if (data.status == '0' && data.message == 'NOTOK') {
                Logger.error(`DevWalletFundingTransactionsService::fetchNativeTransactions::Error fetching native transactions for wallet ${devWalletAddress} with start block ${startBlock} and end block ${endBlock}: ${data.result}`);
                throw new Error(`DevWalletFundingTransactionsService::fetchNativeTransactions::Error fetching native transactions: ${data.result}`);
            }

            Logger.debug(`DevWalletFundingTransactionsService::fetchNativeTransactions:: Found ${data.result?.length || 0} native transactions for wallet ${devWalletAddress}`);
            return data.result || [];
        } catch (error) {
            Logger.error(`DevWalletFundingTransactionsService::fetchNativeTransactions::Error fetching native transactions for wallet ${devWalletAddress}`, error);
            throw error;
        }
    }

    private async fetchERC20Transactions(devWalletAddress: string, launchBlock: number) {
        try {
            const startBlock = launchBlock - DevWalletFundingTransactionsConstant.numberOfBaseBlocksIn2Weeks;
            const endBlock = launchBlock;
            const apiKey = this.etherscanKeyManager.getNextKey();
            const apiUrl = `https://api.etherscan.io/v2/api?chainid=8453&module=account&action=tokentx&address=${devWalletAddress}&startblock=${startBlock}&endblock=${endBlock}&apikey=${apiKey}`;

            const response = await fetch(apiUrl);
            const data = await response.json();
            if (data.status == '0' && data.message == 'NOTOK') {
                Logger.error(`DevWalletFundingTransactionsService::fetchERC20Transactions::Error fetching ERC20 transactions for wallet ${devWalletAddress} with start block ${startBlock} and end block ${endBlock}: ${data.result}`);
                throw new Error(`DevWalletFundingTransactionsService::fetchERC20Transactions::Error fetching ERC20 transactions: ${data.result}`);
            }

            Logger.debug(`DevWalletFundingTransactionsService::fetchERC20Transactions:: Found ${data.result?.length || 0} ERC20 transactions for wallet ${devWalletAddress}`);
            return data.result || [];
        } catch (error) {
            Logger.error(`DevWalletFundingTransactionsService::fetchERC20Transactions::Error fetching ERC20 transactions for wallet ${devWalletAddress}`, error);
            throw error;
        }
    }

    private formatTransactions(transaction: any, pregraduatedTokenAddress: string) {
        try {
            return {
                pregraduatedTokenAddress: pregraduatedTokenAddress,
                blockNumber: parseInt(transaction.blockNumber),
                transactionHash: transaction.hash || transaction.transactionHash,
                timestamp: new Date(parseInt(transaction.timeStamp) * 1000),
                from: transaction.from,
                to: transaction.to,
                contractAddress: transaction.contractAddress || null,
                value: transaction.value / Math.pow(10, transaction.tokenDecimal || 18),
                tokenName: transaction.tokenName || null,
                tokenSymbol: transaction.tokenSymbol || null
            };
        } catch (error) {
            Logger.error(`DevWalletFundingTransactionsService::formatTransactions::Error formatting transaction ${transaction.hash || transaction.transactionHash || 'unknown'}`, error);
            throw error;
        }
    }

    private prepareResponse(): SuccessResponse {
        Logger.info("DevWalletFundingTransactionsService::prepareResponse::Data sync completed, stopping service");
        return ResponseHelper.success({});
    }
}
