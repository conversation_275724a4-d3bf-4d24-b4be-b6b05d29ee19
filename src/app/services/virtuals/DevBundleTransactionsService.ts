import Constant from "../../../config/Constant";
import Container from "../../../lib/Container";
import ClickhouseClient from "../../../lib/dataStores/destination/ClickhouseClient";
import Logger from "../../../lib/Logger";
import NetworkBase from "../../../lib/NetworkBase";
import ResponseHelper from "../../../lib/ResponseHelper";
import { ClickHouseConfig, DevBundleTransactionsModelAttributes, ErrorResponse, SuccessResponse } from "../../../lib/Types";
import DevBundleTrackerModel from "../../model/DevBundleTrackerModel";
import DevBundleTransactionsModel from "../../model/DevBundleTransactionsBase";
import DevBundleDataModel from "../../model/DevBundleDataModel";
import DevBundleTransactionsConstants from "../../../lib/globalConstant/DevBundleTransactionsConstants";

export default class DevBundleTransactionsService extends NetworkBase {
    private chConfig: ClickHouseConfig = {
        host: Constant.clickhouseHost,
        username: Constant.clickhouseUsername,
        password: Constant.clickhousePassword,
        database: null,
        max_open_connections: Constant.clickhouseMaxOpenConnection
    };

    private clickhouse_client: ClickhouseClient;

    private client: any;

    private devBundleTransactionsModel: DevBundleTransactionsModel;

    private devBundleTrackerModel: DevBundleTrackerModel;

    private devBundleDataModel: DevBundleDataModel;

    public constructor(params: any) {
        super(params);
        const oThis = this;
        oThis.clickhouse_client = new ClickhouseClient(oThis.chConfig);
        oThis.client = oThis.clickhouse_client.getClient();
        oThis.devBundleTransactionsModel = Container.get().models.devBundleTransactionsBaseModel;
        oThis.devBundleTrackerModel = Container.get().models.devBundleTrackerModel;
        oThis.devBundleDataModel = Container.get().models.devBundleDataModel;
    }

    protected async perform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        Logger.info("DevBundleTransactionsService::perform:: Starting Dev Bundle Transactions service.");

        await oThis.devBundleTrackerModel.createTableIfNotExists();
        await oThis.devBundleTransactionsModel.createTableIfNotExists();

        await oThis.PopulateBundleTransactions();

        return oThis.prepareResponse();
    }

    private async PopulateBundleTransactions(): Promise<void> {
        const oThis = this;

        const schemaIds = [
            Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS,
            Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD,
            Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED,
            // Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE,
            Constant.DevBundleSchemaIds.VIRTUAL_TOKEN,
            Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
            Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS
        ];

        const lastProcessedAt = await oThis.devBundleTrackerModel.getLastProcessedTime(
            Constant.DevBundleQueryTypes.DEV_BUNDLE_TRANSACTIONS_QUERY,
            schemaIds
        );

        let processingStartTimestamps: Record<number, string> = {};
        schemaIds.forEach((schemaId) => {
            processingStartTimestamps[schemaId] = lastProcessedAt[schemaId].toISOString();
        });
        Logger.info(`DevBundleTransactionsService::PopulateBundleTransactions::Processing start timestamps for schemas: ${JSON.stringify(processingStartTimestamps)}`);

        // Get the last processed token rowId for resuming token processing
        let lastProcessedRowId = await oThis.getLastProcessedRowId();

        if (lastProcessedRowId === null || lastProcessedRowId === undefined) {
            lastProcessedRowId = 0;
        }

        Logger.info(`DevBundleTransactionsService::PopulateBundleTransactions::Starting token processing from rowId: ${lastProcessedRowId}`);

        let tokenPage = 0;
        const tokenBatchSize = DevBundleTransactionsConstants.tokenBatchSize;
        let hasMoreTokens = true;

        // Keep track of the maximum timestamp seen for each schema during the entire processing
        let maxTimestampsSeen: Record<number, string> = { ...processingStartTimestamps };

        while (hasMoreTokens) {
            try {
                const result = await oThis.devBundleDataModel.getTokenDevWalletMapForTransactions(tokenPage, tokenBatchSize, lastProcessedRowId);
                const tokenAddressDevAddressMap = result.tokens;
                const lastRowId = result.lastRowId;

                Logger.info(
                    `DevBundleTransactionsService::PopulateBundleTransactions::Processing token batch ${tokenPage}, found ${Object.keys(tokenAddressDevAddressMap).length} tokens${lastRowId ? ', last rowId: ' + lastRowId : ''}`
                );

                if (Object.keys(tokenAddressDevAddressMap).length === 0) {
                    hasMoreTokens = false;
                    break;
                }

                // Process tokens in parallel
                const tokenAddresses = Object.keys(tokenAddressDevAddressMap);
                const tokenProcessPromises = tokenAddresses.map(async (tokenAddress) => {
                    const devWalletAddresses = tokenAddressDevAddressMap[tokenAddress];
                    let txPage = 0;
                    let hasMoreTransactions = true;

                    while (hasMoreTransactions) {
                        Logger.info(
                            `DevBundleTransactionsService::PopulateBundleTransactions::Fetching transactions for token ${tokenAddress}, page ${txPage}`
                        );

                        const query = DevBundleTransactionsConstants.devBundleTransactionsQuery(
                            txPage,
                            tokenAddress,
                            devWalletAddresses,
                            processingStartTimestamps[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS],
                            processingStartTimestamps[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD],
                            processingStartTimestamps[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED],
                            processingStartTimestamps[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE],
                            processingStartTimestamps[Constant.DevBundleSchemaIds.VIRTUAL_TOKEN],
                            processingStartTimestamps[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS],
                            processingStartTimestamps[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS]
                        );

                        const result = await oThis.client.query({ query, format: "JSONEachRow" });
                        const rawTransactions = await result.json();

                        if (!rawTransactions || rawTransactions.length === 0) {
                            Logger.info(
                                `DevBundleTransactionsService::PopulateBundleTransactions::No more transactions found for token ${tokenAddress} at page ${txPage}`
                            );
                            hasMoreTransactions = false;
                            break;
                        }

                        Logger.info(
                            `DevBundleTransactionsService::PopulateBundleTransactions::Found ${rawTransactions.length} transactions for token ${tokenAddress} at page ${txPage}`
                        );

                        const formattedTransactions: DevBundleTransactionsModelAttributes[] = rawTransactions.map((tx: any) => {
                            // Track the maximum timestamp seen for each schema
                            if (tx.time_stamp && tx.schema !== undefined) {
                                const schemaId = tx.schema;
                                if (new Date(tx.time_stamp) > new Date(maxTimestampsSeen[schemaId])) {
                                    maxTimestampsSeen[schemaId] = tx.time_stamp;
                                }
                            }

                            // Determine if this is an inflow or outflow transaction
                            // If dev wallet is in 'from', it's an outflow (1)
                            // If dev wallet is in 'to', it's an inflow (0)
                            let transactionType = DevBundleTransactionsConstants.transactionType.OUT; // Default to outflow
                            let devWalletAddress = "";

                            if (devWalletAddresses.includes(tx.from)) {
                                transactionType = DevBundleTransactionsConstants.transactionType.OUT;
                                devWalletAddress = tx.from;
                            } else if (devWalletAddresses.includes(tx.to)) {
                                transactionType = DevBundleTransactionsConstants.transactionType.IN;
                                devWalletAddress = tx.to;
                            }

                            return {
                                id: tx.id,
                                tokenAddress: tx.token_address,
                                devWalletAddress: devWalletAddress,
                                transactionType: transactionType,
                                from: tx.from,
                                to: tx.to,
                                amount: tx.amount,
                                timestamp: tx.time_stamp,
                                txHash: tx.tx_hash
                            };
                        });

                        if (formattedTransactions.length > 0) {
                            await oThis.devBundleTransactionsModel.bulkCreate(formattedTransactions);
                        }

                        if (rawTransactions.length < DevBundleTransactionsConstants.resultLimit) {
                            hasMoreTransactions = false;
                            break;
                        }

                        txPage += 1;
                    }
                });


                // Wait for all tokens in this batch to be processed
                await Promise.all(tokenProcessPromises);

                // Update the token tracker with the rowId of the last token in the batch
                if (lastRowId) {
                    await oThis.updateTokenProcessingTracker(lastRowId);
                    Logger.debug(`DevBundleTransactionsService::PopulateBundleTransactions::Updated token tracker with last rowId: ${lastRowId}`);
                }

                tokenPage += 1;
            } catch (error) {
                Logger.error(`DevBundleTransactionsService::PopulateBundleTransactions::Error processing tokens: ${error.message}`);
                throw error;
            }
        }

        Logger.info(`DevBundleTransactionsService::PopulateBundleTransactions::Updating last processed times: ${JSON.stringify(maxTimestampsSeen)}`);

        // Update trackers for each schema only after ALL tokens have been processed
        for (const schemaId of schemaIds) {
            await oThis.devBundleTrackerModel.updateTracker(schemaId, Constant.DevBundleQueryTypes.DEV_BUNDLE_TRANSACTIONS_QUERY, {
                lastRunAt: maxTimestampsSeen[schemaId]
            });
        }

        // Reset the token tracker rowId to 0 after successful completion
        await oThis.resetTokenProcessingTracker();
        Logger.info('DevBundleTransactionsService::PopulateBundleTransactions::Successfully completed and reset token tracker rowId to 0');
    }

    private async getLastProcessedRowId(): Promise<number | null> {
        const oThis = this;
        try {
            const rowId = await oThis.devBundleTrackerModel.getLastProcessedIds(Constant.DevBundleQueryTypes.DEV_BUNDLE_TRANSACTIONS_PROCESSED_TOKEN_TRACKER, [Constant.DevBundleSchemaIds.NO_SCHEMA_REQUIRED]);
            return rowId[Constant.DevBundleSchemaIds.NO_SCHEMA_REQUIRED];
        } catch (e) {
            Logger.error(`DevBundleTransactionsService::getLastProcessedRowId::Error fetching row ID: ${e.message}`);
            throw e;
        }
    }

    private async updateTokenProcessingTracker(lastRowId?: number): Promise<void> {
        const oThis = this;
        try {
            if (!lastRowId) {
                Logger.debug(`DevBundleTransactionsService::updateTokenProcessingTracker::No lastRowId provided, using 0`);
                lastRowId = 0;
            }

            await oThis.devBundleTrackerModel.updateTracker(
                Constant.DevBundleSchemaIds.NO_SCHEMA_REQUIRED,
                Constant.DevBundleQueryTypes.DEV_BUNDLE_TRANSACTIONS_PROCESSED_TOKEN_TRACKER,
                { lastRunId: lastRowId, lastRunAt: new Date().toISOString() }
            );
            Logger.debug(`DevBundleTransactionsService::updateTokenProcessingTracker::Updated token tracker with last rowId: ${lastRowId}`);
        } catch (e) {
            Logger.error(`DevBundleTransactionsService::updateTokenProcessingTracker::Error updating tracker: ${e.message}`);
        }
    }

    private async resetTokenProcessingTracker(): Promise<void> {
        const oThis = this;
        try {
            // Reset the token tracker to rowId 0
            await oThis.devBundleTrackerModel.updateTracker(
                Constant.DevBundleSchemaIds.NO_SCHEMA_REQUIRED,
                Constant.DevBundleQueryTypes.DEV_BUNDLE_TRANSACTIONS_PROCESSED_TOKEN_TRACKER,
                { lastRunId: 0, lastRunAt: new Date().toISOString() }
            );
            Logger.debug('DevBundleTransactionsService::resetTokenProcessingTracker::Reset token tracker rowId to 0');
        } catch (e) {
            Logger.error(`DevBundleTransactionsService::resetTokenProcessingTracker::Error resetting tracker: ${e.message}`);
        }
    }

    private prepareResponse(): SuccessResponse {
        Logger.info("DevBundleTransactionsService::prepareResponse::Data sync completed, stopping service");
        return ResponseHelper.success({});
    }
}
