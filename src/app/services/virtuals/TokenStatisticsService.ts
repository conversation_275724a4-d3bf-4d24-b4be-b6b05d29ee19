import Constant from "../../../config/Constant";
import Container from "../../../lib/Container";
import ClickhouseClient from "../../../lib/dataStores/destination/ClickhouseClient";
import TokenStatisticsConstant from "../../../lib/globalConstant/TokenStatisticsConstant";
import Logger from "../../../lib/Logger";
import NetworkBase from "../../../lib/NetworkBase";
import ResponseHelper from "../../../lib/ResponseHelper";
import { ClickHouseConfig, ErrorResponse, SuccessResponse } from "../../../lib/Types";
import TokenStatisticsModel from "../../model/TokenStatisticsModel";
import DevBundleTrackerModel from "../../model/DevBundleTrackerModel";

export default class TokenStatisticsService extends NetworkBase {

    private chConfig: ClickHouseConfig = {
        host: Constant.clickhouseHost,
        username: Constant.clickhouseUsername,
        password: Constant.clickhousePassword,
        database: null,
        max_open_connections: Constant.clickhouseMaxOpenConnection,
    };

    private clickhouse_client: ClickhouseClient;

    private client: any;

    private tokenStatisticsModel: TokenStatisticsModel;

    private devBundleTrackerModel: DevBundleTrackerModel;

    public constructor(params: any) {
        super(params);
        const oThis = this;
        oThis.clickhouse_client = new ClickhouseClient(oThis.chConfig);
        oThis.client = oThis.clickhouse_client.getClient();
        oThis.tokenStatisticsModel = Container.get().models.tokenStatisticsModel;
        oThis.devBundleTrackerModel = Container.get().models.devBundleTrackerModel;
    }

    protected async perform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        Logger.info("TokenStatisticsService::perform: Starting Token Statistics service.")
        await oThis.tokenStatisticsModel.createTableIfNotExists();
        await oThis.devBundleTrackerModel.createTableIfNotExists();

        await oThis.populateTokenBurnRecords();
        await oThis.populateDevInitialSupply();
        return oThis.prepareResponse();
    }

    private async populateTokenBurnRecords(): Promise<void> {
        const oThis = this;
        Logger.info("TokenStatisticsService::populateTokenBurnRecords: Starting token burn records population");

        const lastProcessedTimes = await oThis.devBundleTrackerModel.getLastProcessedTime(
            Constant.DevBundleQueryTypes.TOKEN_BURN_AMOUNT_QUERY,
            [
                Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS,
                Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD,
                Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE,
                Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED,
                Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
                Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS
            ]
        );

        await oThis.processDataInBatches({
            queryGenerator: (page) => TokenStatisticsConstant.tokenBurnRecordsQuery(
                page,
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS].toISOString(),
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD].toISOString(),
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE].toISOString(),
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED].toISOString(),
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS].toISOString(),
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS].toISOString()
            ),
            dataFormatter: async (data) => await oThis.formatTokenBurnRecordsData(data),
            onBatchComplete: async () => {
                const now = new Date().toISOString();
                for (const schema of [
                    Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS,
                    Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD,
                    Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE,
                    Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED,
                    Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
                    Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS
                ]) {
                    await oThis.devBundleTrackerModel.updateTracker(
                        schema,
                        Constant.DevBundleQueryTypes.TOKEN_BURN_AMOUNT_QUERY,
                        { lastRunAt: now }
                    );
                }
            }
        });
    }

    private async populateDevInitialSupply(): Promise<void> {
        const oThis = this;
        Logger.info("TokenStatisticsService::populateDevInitialSupply: Starting dev initial supply population");

        const lastProcessedTimes = await oThis.devBundleTrackerModel.getLastProcessedTime(
            Constant.DevBundleQueryTypes.TOKEN_DEV_INITIAL_SUPPLY_QUERY,
            [
                Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS,
                Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD,
                Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE,
                Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED,
                Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
                Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS
            ]
        );

        await oThis.processDataInBatches({
            queryGenerator: (page) => TokenStatisticsConstant.tokenDevInitialSupplyQuery(
                page,
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS].toISOString(),
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD].toISOString(),
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE].toISOString(),
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED].toISOString(),
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS].toISOString(),
                lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS].toISOString()
            ),
            dataFormatter: async (data) => await oThis.formatDevInitialSupplyData(data),
            onBatchComplete: async () => {
                const now = new Date().toISOString();
                for (const schema of [
                    Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS,
                    Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD,
                    Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE,
                    Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED,
                    Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
                    Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS
                ]) {
                    await oThis.devBundleTrackerModel.updateTracker(
                        schema,
                        Constant.DevBundleQueryTypes.TOKEN_DEV_INITIAL_SUPPLY_QUERY,
                        { lastRunAt: now }
                    );
                }
            }
        });
    }

    /**
     * Common method to handle data processing in batches until all data is processed
     * @param options Configuration options for data processing
     * @param options.queryGenerator Function that generates the query for a given page
     * @param options.dataFormatter Function that formats the result data
     * @param options.onBatchComplete Function to call when a batch is complete (for updating trackers)
     */
    private async processDataInBatches(options: {
        queryGenerator: (page: number) => string,
        dataFormatter: (data: any[]) => Promise<any[]>,
        onBatchComplete?: () => Promise<void>
    }): Promise<void> {
        const oThis = this;
        let page = 0;
        let hasMoreData = true;
        let totalRecordsProcessed = 0;

        Logger.debug(`TokenStatisticsService::processDataInBatches::Starting data insertion process`);
        try {
            while (hasMoreData) {
                const query = options.queryGenerator(page);
                Logger.debug(`TokenStatisticsService::processDataInBatches::Query: ${query}`);

                const queryResult = await oThis.client.query({ query, format: 'JSONEachRow' });
                const result = await queryResult.json();

                if (!result || result.length === 0) {
                    hasMoreData = false;
                    Logger.debug(`TokenStatisticsService::processDataInBatches::No more data to process. Total records processed: ${totalRecordsProcessed}`);
                    break;
                }

                const formattedData = await options.dataFormatter(result);
                await oThis.tokenStatisticsModel.bulkCreate(formattedData);

                totalRecordsProcessed += result.length;
                Logger.debug(`TokenStatisticsService::processDataInBatches::Processed page ${page} with ${result.length} records. Total processed: ${totalRecordsProcessed}`);

                // Call the onBatchComplete callback if provided
                if (options.onBatchComplete) {
                    await options.onBatchComplete();
                }

                page++;
            }

            Logger.debug(`TokenStatisticsService::processDataInBatches::Data insertion completed successfully. Total records: ${totalRecordsProcessed}`);
        } catch (error) {
            Logger.error(`TokenStatisticsService::processDataInBatches::Error during data insertion: ${error.message}`);
            throw error;
        }
    }

    private async formatTokenBurnRecordsData(data: any) {
        const oThis = this;
        const tokenAddresses = data.map((item: any) => item.token_address);
        const existingBurnAmounts = await oThis.tokenStatisticsModel.getTokenBurnAmounts(tokenAddresses);

        return data.map((item: any) => {
            const existingAmount = existingBurnAmounts[item.token_address] || 0;
            const newAmount = Number(item.amount) || 0;
            Logger.debug(`TokenStatisticsService::formatTokenBurnRecordsData::Existing amount: ${existingAmount}, New amount: ${newAmount} for token: ${item.token_address}`);

            return {
                tokenAddress: item.token_address,
                tokenBurnAmount: existingAmount + newAmount,
                lastBurnedAt: item.last_burned_at
            };
        });
    }

    private async formatDevInitialSupplyData(data: any) {
        return data.map((item: any) => {
            return {
                tokenAddress: item.token_address,
                devInitialSupply: item.amount
            };
        });
    }

    private prepareResponse(): SuccessResponse {
        Logger.info(`TokenStatisticsService::prepareResponse::Data sync completed, stopping service`)
        return ResponseHelper.success({});
    }
}
