import Constant from "../../../config/Constant";
import Container from "../../../lib/Container";
import ClickhouseClient from "../../../lib/dataStores/destination/ClickhouseClient";
import DevBundleDataConstants from "../../../lib/globalConstant/DevBundleDataConstants";
import Logger from "../../../lib/Logger";
import NetworkBase from "../../../lib/NetworkBase";
import ResponseHelper from "../../../lib/ResponseHelper";
import { ClickHouseConfig, ErrorResponse, SuccessResponse, DevBundleDataModelAttributes } from "../../../lib/Types";
import DevBundleDataModel from "../../model/DevBundleDataModel";
import TokenStatisticsModel from "../../model/TokenStatisticsModel";
import DevBundleTrackerModel from "../../model/DevBundleTrackerModel";

export default class DevBundleDataService extends NetworkBase {

    private chConfig: ClickHouseConfig = {
        host: Constant.clickhouseHost,
        username: Constant.clickhouseUsername,
        password: Constant.clickhousePassword,
        database: null,
        max_open_connections: Constant.clickhouseMaxOpenConnection,
    };

    private clickhouse_client: ClickhouseClient;

    private client: any;

    private devBundleDataModel: DevBundleDataModel;

    private tokenStatisticsModel: TokenStatisticsModel;

    private devBundleTrackerModel: DevBundleTrackerModel;

    public constructor(params: any) {
        super(params);
        const oThis = this;
        oThis.clickhouse_client = new ClickhouseClient(oThis.chConfig);
        oThis.client = oThis.clickhouse_client.getClient();
        oThis.devBundleDataModel = Container.get().models.devBundleDataModel;
        oThis.tokenStatisticsModel = Container.get().models.tokenStatisticsModel;
        oThis.devBundleTrackerModel = Container.get().models.devBundleTrackerModel;
    }

    protected async perform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        Logger.info("DevBundleDataService::perform:: Starting Dev Bundle Data service.")

        await oThis.devBundleTrackerModel.createTableIfNotExists();
        await oThis.devBundleDataModel.createTableIfNotExists();

        await oThis.populateDevWalletsData();
        await oThis.populateMigratedTokensDevWallets();
        await oThis.populateSpreadWalletsData();

        await oThis.updateDevWalletsFlow();
        await oThis.updateSpreadWalletsFlow();
        await oThis.updateLevel2SpreadWalletsFlow();

        await oThis.updateWalletBalanceData();
        // await oThis.updateRugStatus(); // Rug status update is now being handled in Loky Backend

        return oThis.prepareResponse();
    }

    private async populateDevWalletsData(): Promise<void> {
        const oThis = this;
        let page = 0;
        let hasMoreData = true;
        let totalRecordsProcessed = 0;

        Logger.debug("DevBundleDataService::populateDevWalletsData::Starting data insertion process");
        try {
            const lastProcessedTimes = await oThis.devBundleTrackerModel.getLastProcessedTime(
                Constant.DevBundleQueryTypes.DEV_WALLET_BUNDLE_QUERY,
                Constant.virtualSchemasWithoutMigrated
            );

            while (hasMoreData) {
                const query = DevBundleDataConstants.devWalletBundleDataQuery(
                    page,
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS].toISOString()
                );
                Logger.debug(`DevBundleDataService::populateDevWalletsData::Query: ${query}`);

                const queryResult = await oThis.client.query({ query, format: 'JSONEachRow' });
                const result = await queryResult.json();

                if (!result || result.length === 0) {
                    hasMoreData = false;
                    Logger.debug(`DevBundleDataService::populateDevWalletsData::No more data to process. Total records processed: ${totalRecordsProcessed}`);
                    break;
                }

                const formattedData = result.map((item: any) => ({
                    tokenAddress: item.token_address,
                    devWalletAddress: item.dev_wallet,
                    devWalletType: DevBundleDataConstants.devBundleWalletType.DEV_WALLET,
                }));

                const { filteredItems, shouldSkip } = await oThis.filterRuggedTokens(formattedData);
                if (shouldSkip) {
                    Logger.debug(`DevBundleDataService::populateDevWalletsData::Skipped page ${page} - no valid records after filtering`);
                    page++;
                    continue;
                }

                if (filteredItems.length > 0) {
                    await oThis.devBundleDataModel.bulkCreate(filteredItems as DevBundleDataModelAttributes[]);
                    totalRecordsProcessed += filteredItems.length;
                    Logger.debug(`DevBundleDataService::populateDevWalletsData::Processed page ${page} with ${filteredItems.length} records. Total processed: ${totalRecordsProcessed}`);

                    // Update lastRunAt for each schema based on the maximum created_at timestamp from the query response
                    const schemaTimestamps: Record<number, string> = {};

                    // Group results by schema and find the maximum created_at for each schema
                    result.forEach((item: any) => {
                        const schemaId = oThis.getSchemaIdFromSchemaName(item.schema);
                        if (schemaId !== null && item.created_at) {
                            if (!schemaTimestamps[schemaId] || item.created_at > schemaTimestamps[schemaId]) {
                                schemaTimestamps[schemaId] = item.created_at;
                            }
                        }
                    });

                    // Update tracker for each schema with its respective maximum timestamp
                    for (const [schemaId, timestamp] of Object.entries(schemaTimestamps)) {
                        await oThis.devBundleTrackerModel.updateTracker(
                            parseInt(schemaId),
                            Constant.DevBundleQueryTypes.DEV_WALLET_BUNDLE_QUERY,
                            { lastRunAt: timestamp }
                        );
                        Logger.debug(`DevBundleDataService::populateDevWalletsData::Updated tracker for schema ${schemaId} with timestamp ${timestamp}`);
                    }
                }

                page++;
            }
        } catch (error) {
            Logger.error(`DevBundleDataService::populateDevWalletsData::Error during data insertion: ${error.message}`);
            throw error;
        }
    }

    private getSchemaIdFromSchemaName(schemaName: string): number | null {
        const schemaMapping: Record<string, number> = {
            'virtual_ai_agents': Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS,
            'virtual_ai_agents_old': Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD,
            'virtual_ai_agents_prototype': Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE,
            'virtual_ai_agents_migrated': Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED,
            'virtual_token': Constant.DevBundleSchemaIds.VIRTUAL_TOKEN,
            'virtuals_genesis_agents': Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
            'virtuals_ethereum_agents': Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS
        };

        return schemaMapping[schemaName] ?? null;
    }

    private async populateMigratedTokensDevWallets(): Promise<void> {
        const oThis = this;
        try {
            const migratedTokens = DevBundleDataConstants.migratedTokenAndDevAddresses;
            const bulkData = migratedTokens.map((item: any) => ({
                tokenAddress: item.tokenAddress,
                devWalletAddress: item.devAddress,
                devWalletType: DevBundleDataConstants.devBundleWalletType.DEV_WALLET
            }));
            Logger.debug(`DevBundleDataService::populateMigratedTokensDevWallets::Bulk data: ${JSON.stringify(bulkData)}`);
            await oThis.devBundleDataModel.bulkCreate(bulkData as DevBundleDataModelAttributes[]);
        } catch (error) {
            Logger.error(`DevBundleDataService::populateMigratedTokensDevWallets::Error during data insertion: ${error.message}`);
            throw error;
        }
    }

    private async populateSpreadWalletsData(): Promise<void> {
        const oThis = this;
        let page = 0;
        let hasMoreData = true;
        let totalRecordsProcessed = 0;

        Logger.debug("DevBundleDataService::populateSpreadWalletsData::Starting data insertion process");
        try {
            const lastProcessedTimes = await oThis.devBundleTrackerModel.getLastProcessedTime(
                Constant.DevBundleQueryTypes.SPREAD_WALLET_BUNDLE_QUERY,
                Constant.allVirtualSchemas
            );

            while (hasMoreData) {
                const query = DevBundleDataConstants.spreadWalletBundleDataQuery(
                    page,
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS].toISOString()
                );
                Logger.debug(`DevBundleDataService::populateSpreadWalletsData::Query: ${query}`);

                const queryResult = await oThis.client.query({ query, format: 'JSONEachRow' });
                const result = await queryResult.json();

                if (!result || result.length === 0) {
                    hasMoreData = false;
                    Logger.debug(`DevBundleDataService::populateSpreadWalletsData::No more data to process. Total records processed: ${totalRecordsProcessed}`);
                    break;
                }

                const formattedData = result
                    .filter((item: any) => item.amount && Number(item.amount) >= DevBundleDataConstants.maxOutflowThresholdForDevWallets)
                    .map((item: any) => ({
                        tokenAddress: item.token_address,
                        devWalletAddress: item.spread_wallet_address,
                        devWalletType: DevBundleDataConstants.devBundleWalletType.SPREAD_WALLET,
                        parentWallet: item.dev_wallet
                    }));

                const { filteredItems, shouldSkip } = await oThis.filterRuggedTokens(formattedData);
                if (shouldSkip) {
                    Logger.debug(`DevBundleDataService::populateSpreadWalletsData::Skipped page ${page} - no valid records after filtering`);
                    page++;
                    continue;
                }

                if (filteredItems.length > 0) {
                    await oThis.devBundleDataModel.bulkCreate(filteredItems as DevBundleDataModelAttributes[]);
                    totalRecordsProcessed += filteredItems.length;
                    Logger.debug(`DevBundleDataService::populateSpreadWalletsData::Processed page ${page} with ${filteredItems.length} records. Total processed: ${totalRecordsProcessed}`);

                    // Update lastRunAt for each schema based on the maximum created_at timestamp from the query response
                    const schemaTimestamps: Record<number, string> = {};

                    // Group results by schema and find the maximum created_at for each schema
                    result.forEach((item: any) => {
                        const schemaId = oThis.getSchemaIdFromSchemaName(item.schema);
                        if (schemaId !== null && item.created_at) {
                            if (!schemaTimestamps[schemaId] || item.created_at > schemaTimestamps[schemaId]) {
                                schemaTimestamps[schemaId] = item.created_at;
                            }
                        }
                    });

                    // Update tracker for each schema with its respective maximum timestamp
                    for (const [schemaId, timestamp] of Object.entries(schemaTimestamps)) {
                        await oThis.devBundleTrackerModel.updateTracker(
                            parseInt(schemaId),
                            Constant.DevBundleQueryTypes.SPREAD_WALLET_BUNDLE_QUERY,
                            { lastRunAt: timestamp }
                        );
                        Logger.debug(`DevBundleDataService::populateSpreadWalletsData::Updated tracker for schema ${schemaId} with timestamp ${timestamp}`);
                    }
                }

                page++;
            }
        } catch (error) {
            Logger.error(`DevBundleDataService::populateSpreadWalletsData::Error during data insertion: ${error.message}`);
            throw error;
        }
    }

    private async updateDevWalletsFlow(): Promise<void> {
        const oThis = this;
        let page = 0;
        let hasMoreData = true;
        const BATCH_SIZE = DevBundleDataConstants.batchSize;

        Logger.debug("DevBundleDataService::updateDevWalletsFlow::Starting data population process");
        try {
            // Get last processed IDs for DEV_WALLET_FLOW_QUERY
            const lastProcessedIds = await oThis.devBundleTrackerModel.getLastProcessedIds(
                Constant.DevBundleQueryTypes.DEV_WALLET_FLOW_QUERY,
                Constant.allVirtualSchemas
            );

            // Get last processed IDs for DEV_INCOMING_TRANSACTIONS_QUERY
            const incomingLastProcessedIds = await oThis.devBundleTrackerModel.getLastProcessedIds(
                Constant.DevBundleQueryTypes.DEV_INCOMING_TRANSACTIONS_QUERY,
                Constant.allVirtualSchemas
            );

            Logger.debug(`DevBundleDataService::updateDevWalletsFlow::Last processed IDs: ${JSON.stringify(lastProcessedIds)}`);
            Logger.debug(`DevBundleDataService::updateDevWalletsFlow::Incoming last processed IDs: ${JSON.stringify(incomingLastProcessedIds)}`);

            while (hasMoreData) {
                const walletsData = await oThis.devBundleDataModel.getWalletsByType(
                    DevBundleDataConstants.devBundleWalletType.DEV_WALLET,
                    page,
                    BATCH_SIZE
                );

                if (walletsData.length === 0) {
                    hasMoreData = false;
                    break;
                }

                const walletTokenPairs = walletsData.map((item: any) => ({
                    walletAddress: item.devWalletAddress,
                    tokenAddress: item.tokenAddress
                }));

                const { filteredItems, shouldSkip } = await oThis.filterRuggedTokens(walletTokenPairs);
                if (shouldSkip) {
                    Logger.info(`DevBundleDataService::updateDevWalletsFlow::Skipped page ${page} - all tokens were rugged`);
                    page++;
                    continue;
                }

                const walletAddresses = Array.from(new Set(filteredItems.map(pair => pair.walletAddress)));
                const {
                    transactions,
                    receivedFromDevAmounts,
                    highestProcessedUuids,
                    outflowToNonDevWallet,
                    soldAmount,
                    stakedOrVestedAmount
                } = await oThis.getWalletTransactions(
                    walletAddresses,
                    filteredItems,
                    DevBundleDataConstants.maxOutflowThresholdForDevWallets,
                    false,
                    false,
                    lastProcessedIds
                );

                // Get incoming transactions
                const {
                    transactions: incomingTransactions,
                    highestProcessedUuids: incomingHighestProcessedUuids,
                    inflowFromAllWallets,
                    buyAmount
                } = await oThis.getIncomingTransactions(
                    walletAddresses,
                    filteredItems,
                    incomingLastProcessedIds
                );

                const existingData = await oThis.getExistingWalletData(filteredItems);
                Logger.debug(`DevBundleDataService::updateDevWalletsFlow::Found ${Object.keys(existingData).length} existing wallet-token pairs with data`);

                // Process all transaction types in a single transaction
                await oThis.updateTransactionAmountsInBatch(
                    transactions,
                    outflowToNonDevWallet,
                    soldAmount,
                    receivedFromDevAmounts,
                    inflowFromAllWallets,
                    buyAmount,
                    incomingTransactions,
                    existingData,
                    highestProcessedUuids,
                    incomingHighestProcessedUuids,
                    Constant.DevBundleQueryTypes.DEV_WALLET_FLOW_QUERY,
                    Constant.DevBundleQueryTypes.DEV_INCOMING_TRANSACTIONS_QUERY,
                    stakedOrVestedAmount
                );

                page++;
            }

        } catch (error) {
            Logger.error(`DevBundleDataService::updateDevWalletsFlow::Error during data population: ${error.message}`);
            throw error;
        }
    }

    private async updateSpreadWalletsFlow(): Promise<void> {
        const oThis = this;
        let page = 0;
        let hasMoreData = true;
        const BATCH_SIZE = DevBundleDataConstants.batchSize;

        Logger.debug("DevBundleDataService::updateSpreadWalletsFlow::Starting data population process");
        try {
            // Get last processed IDs for SPREAD_WALLET_FLOW_QUERY
            const lastProcessedIds = await oThis.devBundleTrackerModel.getLastProcessedIds(
                Constant.DevBundleQueryTypes.SPREAD_WALLET_FLOW_QUERY,
                Constant.allVirtualSchemas
            );

            // Get last processed IDs for SPREAD_INCOMING_TRANSACTIONS_QUERY
            const incomingLastProcessedIds = await oThis.devBundleTrackerModel.getLastProcessedIds(
                Constant.DevBundleQueryTypes.SPREAD_INCOMING_TRANSACTIONS_QUERY,
                Constant.allVirtualSchemas
            );

            Logger.debug(`DevBundleDataService::updateSpreadWalletsFlow::Last processed IDs: ${JSON.stringify(lastProcessedIds)}`);
            Logger.debug(`DevBundleDataService::updateSpreadWalletsFlow::Incoming last processed IDs: ${JSON.stringify(incomingLastProcessedIds)}`);

            while (hasMoreData) {
                const walletsData = await oThis.devBundleDataModel.getWalletsByType(
                    DevBundleDataConstants.devBundleWalletType.SPREAD_WALLET,
                    page,
                    BATCH_SIZE
                );

                if (walletsData.length === 0) {
                    hasMoreData = false;
                    break;
                }

                const walletTokenPairs = walletsData.map((item: any) => ({
                    walletAddress: item.devWalletAddress,
                    tokenAddress: item.tokenAddress
                }));

                const { filteredItems, shouldSkip } = await oThis.filterRuggedTokens(walletTokenPairs);
                if (shouldSkip) {
                    Logger.info(`DevBundleDataService::updateSpreadWalletsFlow::Skipped page ${page} - all tokens were rugged`);
                    page++;
                    continue;
                }

                const walletAddresses = Array.from(new Set(filteredItems.map(pair => pair.walletAddress)));
                const {
                    transactions,
                    receivedFromDevAmounts,
                    level2SpreadWallets,
                    highestProcessedUuids,
                    outflowToNonDevWallet,
                    soldAmount,
                    stakedOrVestedAmount
                } = await oThis.getWalletTransactions(
                    walletAddresses,
                    filteredItems,
                    DevBundleDataConstants.maxOutflowThresholdForDevWallets,
                    false,
                    true,
                    lastProcessedIds
                );

                // Get incoming transactions
                const {
                    transactions: incomingTransactions,
                    highestProcessedUuids: incomingHighestProcessedUuids,
                    inflowFromAllWallets,
                    buyAmount
                } = await oThis.getIncomingTransactions(
                    walletAddresses,
                    filteredItems,
                    incomingLastProcessedIds
                );

                if (level2SpreadWallets && Object.keys(level2SpreadWallets).length > 0) {
                    const bulkData = Object.values(level2SpreadWallets).map(wallet => ({
                        tokenAddress: wallet.tokenAddress,
                        devWalletAddress: wallet.recipientAddress,
                        devWalletType: DevBundleDataConstants.devBundleWalletType.LEVEL_2_SPREAD_WALLET,
                        parentWallet: wallet.parentAddress
                    }));
                    await oThis.devBundleDataModel.bulkCreate(bulkData);
                    Logger.debug(`DevBundleDataService::updateSpreadWalletsFlow::Stored ${bulkData.length} Level 2 spread wallets`);
                }

                const existingData = await oThis.getExistingWalletData(filteredItems);
                Logger.debug(`DevBundleDataService::updateSpreadWalletsFlow::Found ${Object.keys(existingData).length} existing wallet-token pairs with data`);

                // Process all transaction types in a single transaction
                await oThis.updateTransactionAmountsInBatch(
                    transactions,
                    outflowToNonDevWallet,
                    soldAmount,
                    receivedFromDevAmounts,
                    inflowFromAllWallets,
                    buyAmount,
                    incomingTransactions,
                    existingData,
                    highestProcessedUuids,
                    incomingHighestProcessedUuids,
                    Constant.DevBundleQueryTypes.SPREAD_WALLET_FLOW_QUERY,
                    Constant.DevBundleQueryTypes.SPREAD_INCOMING_TRANSACTIONS_QUERY,
                    stakedOrVestedAmount
                );

                page++;
            }

        } catch (error) {
            Logger.error(`DevBundleDataService::updateSpreadWalletsFlow::Error during data population: ${error.message}`);
            throw error;
        }
    }

    private async updateLevel2SpreadWalletsFlow(): Promise<void> {
        const oThis = this;
        let page = 0;
        let hasMoreData = true;
        const BATCH_SIZE = DevBundleDataConstants.batchSize;

        Logger.debug("DevBundleDataService::updateLevel2SpreadWalletsFlow::Starting data population process");
        try {
            // Get last processed IDs for LEVEL_2_SPREAD_WALLET_FLOW_QUERY
            const lastProcessedIds = await oThis.devBundleTrackerModel.getLastProcessedIds(
                Constant.DevBundleQueryTypes.LEVEL_2_SPREAD_WALLET_FLOW_QUERY,
                Constant.allVirtualSchemas
            );

            // Get last processed IDs for LEVEL_2_SPREAD_INCOMING_TRANSACTIONS_QUERY
            const incomingLastProcessedIds = await oThis.devBundleTrackerModel.getLastProcessedIds(
                Constant.DevBundleQueryTypes.LEVEL_2_SPREAD_INCOMING_TRANSACTIONS_QUERY,
                Constant.allVirtualSchemas
            );

            Logger.debug(`DevBundleDataService::updateLevel2SpreadWalletsFlow::Last processed IDs: ${JSON.stringify(lastProcessedIds)}`);
            Logger.debug(`DevBundleDataService::updateLevel2SpreadWalletsFlow::Incoming last processed IDs: ${JSON.stringify(incomingLastProcessedIds)}`);

            while (hasMoreData) {
                const walletsData = await oThis.devBundleDataModel.getWalletsByType(
                    DevBundleDataConstants.devBundleWalletType.LEVEL_2_SPREAD_WALLET,
                    page,
                    BATCH_SIZE
                );

                if (walletsData.length === 0) {
                    hasMoreData = false;
                    break;
                }

                const walletTokenPairs = walletsData.map((item: any) => ({
                    walletAddress: item.devWalletAddress,
                    tokenAddress: item.tokenAddress
                }));

                const { filteredItems, shouldSkip } = await oThis.filterRuggedTokens(walletTokenPairs);
                if (shouldSkip) {
                    Logger.info(`DevBundleDataService::updateLevel2SpreadWalletsFlow::Skipped page ${page} - all tokens were rugged`);
                    page++;
                    continue;
                }

                const walletAddresses = Array.from(new Set(filteredItems.map(pair => pair.walletAddress)));
                const {
                    transactions,
                    highestProcessedUuids,
                    outflowToNonDevWallet,
                    soldAmount,
                    stakedOrVestedAmount
                } = await oThis.getWalletTransactions(
                    walletAddresses,
                    filteredItems,
                    undefined,
                    true,
                    false,
                    lastProcessedIds
                );

                // Get incoming transactions
                const {
                    transactions: incomingTransactions,
                    highestProcessedUuids: incomingHighestProcessedUuids,
                    inflowFromAllWallets,
                    buyAmount
                } = await oThis.getIncomingTransactions(
                    walletAddresses,
                    filteredItems,
                    incomingLastProcessedIds
                );

                const existingData = await oThis.getExistingWalletData(filteredItems);
                Logger.debug(`DevBundleDataService::updateLevel2SpreadWalletsFlow::Found ${Object.keys(existingData).length} existing wallet-token pairs with data`);

                // Process all transaction types in a single transaction
                await oThis.updateTransactionAmountsInBatch(
                    transactions,
                    outflowToNonDevWallet,
                    soldAmount,
                    {},
                    inflowFromAllWallets,
                    buyAmount,
                    incomingTransactions,
                    existingData,
                    highestProcessedUuids,
                    incomingHighestProcessedUuids,
                    Constant.DevBundleQueryTypes.LEVEL_2_SPREAD_WALLET_FLOW_QUERY,
                    Constant.DevBundleQueryTypes.LEVEL_2_SPREAD_INCOMING_TRANSACTIONS_QUERY,
                    stakedOrVestedAmount
                );

                page++;
            }

        } catch (error) {
            Logger.error(`DevBundleDataService::updateLevel2SpreadWalletsFlow::Error during data population: ${error.message}`);
            throw error;
        }
    }

    private async updateTrackerWithHigherUuid(
        schema: number,
        queryType: number,
        maxUuid: number,
        transaction?: any
    ): Promise<void> {
        const oThis = this;
        try {
            const currentProcessedIds = await oThis.devBundleTrackerModel.getLastProcessedIds(
                queryType,
                [schema]
            );

            const currentId = currentProcessedIds[schema] || 0;

            const now = new Date().toISOString();
            if (maxUuid > currentId) {
                await oThis.devBundleTrackerModel.updateTracker(
                    schema,
                    queryType,
                    { lastRunId: maxUuid, lastRunAt: now },
                    transaction
                );
                Logger.debug(`DevBundleDataService::updateTrackerWithHigherUuid::Updated tracker for schema ${schema} with ID ${maxUuid} (previous: ${currentId})`);
            } else {
                Logger.debug(`DevBundleDataService::updateTrackerWithHigherUuid::Skipped update for schema ${schema} - new ID ${maxUuid} not higher than current ${currentId}`);
            }
        } catch (error) {
            Logger.error(`DevBundleDataService::updateTrackerWithHigherUuid::Error updating tracker: ${error.message}`);
            throw error;
        }
    }

    private async updateWalletBalanceData(): Promise<void> {
        const oThis = this;
        let page = 0;
        let hasMoreData = true;
        const BATCH_SIZE = DevBundleDataConstants.batchSize;

        Logger.debug("DevBundleDataService::updateWalletBalanceData::Starting data population process");
        try {
            const lastProcessedTimes = await oThis.devBundleTrackerModel.getLastProcessedTime(
                Constant.DevBundleQueryTypes.WALLET_BALANCES_QUERY,
                Constant.allVirtualSchemas
            );

            Logger.debug(`DevBundleDataService::updateWalletBalanceData::Last processed times: ${JSON.stringify(lastProcessedTimes)}`);

            while (hasMoreData) {
                const walletsData = await oThis.devBundleDataModel.getAllWallets(page, BATCH_SIZE);

                if (walletsData.length === 0) {
                    hasMoreData = false;
                    break;
                }

                const walletTokenPairs = walletsData.map((item: any) => ({
                    walletAddress: item.devWalletAddress,
                    tokenAddress: item.tokenAddress
                }));

                const { filteredItems, shouldSkip } = await oThis.filterRuggedTokens(walletTokenPairs);
                if (shouldSkip) {
                    Logger.info(`DevBundleDataService::updateWalletBalanceData::Skipped page ${page} - all tokens were rugged`);
                    page++;
                    continue;
                }

                const walletAddresses = Array.from(new Set(filteredItems.map(pair => pair.walletAddress)));

                const walletTokenBalances = await oThis.getWalletBalances(
                    walletAddresses,
                    filteredItems,
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS].toISOString(),
                    lastProcessedTimes[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS].toISOString()
                );

                Logger.debug(`DevBundleDataService::updateWalletBalanceData::Found ${Object.keys(walletTokenBalances).length} wallet-token balances`);

                if (Object.keys(walletTokenBalances).length === 0) {
                    Logger.debug(`DevBundleDataService::updateWalletBalanceData::No balance changes detected since last run, skipping update`);
                    page++;
                    continue;
                }

                const uniqueTokenAddresses = Array.from(new Set(filteredItems.map(pair => pair.tokenAddress)));
                const tokenBurnAmounts = await oThis.tokenStatisticsModel.getTokenBurnAmounts(uniqueTokenAddresses);
                Logger.debug(`DevBundleDataService::updateWalletBalanceData::Found burn amounts for ${Object.keys(tokenBurnAmounts).length} tokens`);

                const tokenCirculatingSupply: Record<string, number> = {};
                uniqueTokenAddresses.forEach(tokenAddress => {
                    const burnAmount = tokenBurnAmounts[tokenAddress] || 0;
                    const circulatingSupply = DevBundleDataConstants.defaultTotalSupply - burnAmount;
                    tokenCirculatingSupply[tokenAddress] = circulatingSupply;

                    if (burnAmount > 0) {
                        const burnPercentage = (burnAmount / DevBundleDataConstants.defaultTotalSupply) * 100;
                        Logger.debug(`DevBundleDataService::updateWalletBalanceData::Token ${tokenAddress} has ${burnAmount.toFixed(2)} burned tokens (${burnPercentage.toFixed(2)}%), Circulating Supply: ${circulatingSupply.toFixed(2)}`);
                    }
                });

                for (const [key, balance] of Object.entries(walletTokenBalances)) {
                    if (balance === null) continue;

                    const [walletAddress, tokenAddress] = key.split('|');
                    const circulatingSupply = tokenCirculatingSupply[tokenAddress];
                    let holdingPercentage = 0;

                    if (circulatingSupply > 0) {
                        holdingPercentage = (balance / circulatingSupply) * 100;
                        holdingPercentage = Number(holdingPercentage.toFixed(5));
                    }

                    await oThis.devBundleDataModel.update(
                        {
                            devWalletAddress: walletAddress,
                            tokenAddress: tokenAddress
                        },
                        {
                            balance: balance,
                            holdingPercentage: holdingPercentage
                        }
                    );

                    const now = new Date().toISOString();
                    for (const schema of Constant.allVirtualSchemas) {
                        await oThis.devBundleTrackerModel.updateTracker(
                            schema,
                            Constant.DevBundleQueryTypes.WALLET_BALANCES_QUERY,
                            { lastRunAt: now }
                        );
                    }
                }

                page++;
            }
        } catch (error) {
            Logger.error(`DevBundleDataService::updateWalletBalanceData::Error during data population: ${error.message}`);
            throw error;
        }
    }

    /*
    private async updateRugStatus(): Promise<void> {
        const oThis = this;
        Logger.info("DevBundleDataService::updateRugStatus::Starting rug status evaluation");

        try {
            const TOKEN_BATCH_SIZE = DevBundleDataConstants.batchSize;
            let tokenPage = 0;
            let hasMoreTokens = true;
            let processedTokenCount = 0;

            const rugRanges = DevBundleDataConstants.rugStatusThresholdRanges;
            const rugStatus = DevBundleDataConstants.rugStatus;

            while (hasMoreTokens) {
                const tokenBatch = await oThis.devBundleDataModel.getTokensByRugStatus(
                    [rugStatus.SAFE, rugStatus.CAUTION, rugStatus.HIGH_RISK],
                    tokenPage,
                    TOKEN_BATCH_SIZE
                );

                Logger.debug(`DevBundleDataService::updateRugStatus::Processing token batch ${tokenPage + 1} with ${tokenBatch.length} tokens`);

                if (tokenBatch.length === 0) {
                    hasMoreTokens = false;
                    Logger.info("DevBundleDataService::updateRugStatus::No more tokens to process");
                    break;
                }

                const tokenAddresses = tokenBatch.map(token => token.tokenAddress);
                const tokenTotalSoldAmounts = await oThis.devBundleDataModel.getTokenTotalSoldAmount(tokenAddresses);
                Logger.debug(`DevBundleDataService::updateRugStatus::Retrieved sold amounts for ${Object.keys(tokenTotalSoldAmounts).length} tokens in batch ${tokenPage + 1}`);

                const tokensByCategory: Record<number, string[]> = {
                    [rugStatus.RUGGED]: [],
                    [rugStatus.HIGH_RISK]: [],
                    [rugStatus.CAUTION]: [],
                    [rugStatus.SAFE]: []
                };

                for (const [tokenAddress, soldAmount] of Object.entries(tokenTotalSoldAmounts)) {
                    let category = rugStatus.SAFE;

                    if (soldAmount > rugRanges.RUGGED.min) {
                        category = rugStatus.RUGGED;
                    } else if (soldAmount > rugRanges.HIGH_RISK.min) {
                        category = rugStatus.HIGH_RISK;
                    } else if (soldAmount > rugRanges.CAUTION.min) {
                        category = rugStatus.CAUTION;
                    }

                    tokensByCategory[category].push(tokenAddress);
                    Logger.debug(`DevBundleDataService::updateRugStatus::Token ${tokenAddress} identified as ${category} with sold amount ${soldAmount.toLocaleString()}`);
                }

                for (const [category, tokens] of Object.entries(tokensByCategory)) {
                    if (tokens.length > 0) {
                        await oThis.devBundleDataModel.updateRugStatusForTokens(tokens, Number(category));
                        Logger.debug(`DevBundleDataService::updateRugStatus::Updated ${tokens.length} tokens to status ${category}`);
                    }
                }

                processedTokenCount += tokenBatch.length;
                Logger.debug(`DevBundleDataService::updateRugStatus::Processed ${processedTokenCount} tokens so far`);
                tokenPage++;
            }
            Logger.info(`DevBundleDataService::updateRugStatus::Rug status evaluation completed successfully. Total tokens processed: ${processedTokenCount}`);
        } catch (error) {
            Logger.error(`DevBundleDataService::updateRugStatus::Error evaluating rug status: ${error.message}`);
            throw error;
        }
    }
    */

    private async getWalletTransactions(
        walletAddresses: string[],
        walletTokenPairs: Array<{ walletAddress: string, tokenAddress: string }>,
        maxValidAmount?: number,
        isLevel2SpreadWallet: boolean = false,
        shouldIdentifyLevel2Wallets: boolean = false,
        lastProcessedIds?: Record<number, number>,
        batchSize: number = DevBundleDataConstants.transactionBatchSize
    ): Promise<{
        transactions: Record<string, number[]>,
        receivedFromDevAmounts: Record<string, number[]>,
        level2SpreadWallets?: Record<string, { recipientAddress: string, tokenAddress: string, parentAddress: string }>,
        highestProcessedUuids?: Record<number, number>,
        outflowToNonDevWallet: Record<string, number[]>,
        soldAmount: Record<string, number[]>,
        stakedOrVestedAmount: Record<string, number[]>
    }> {
        const oThis = this;
        const walletTokenRecipientAmounts: Record<string, Record<string, number>> = {};
        const walletTokenRecipientOutflowToNonDevWalletAmounts: Record<string, Record<string, number>> = {};
        const walletTokenRecipientOutflowToContractAmounts: Record<string, Record<string, number>> = {};
        const walletTokenRecipientStakedOrVestedAmounts: Record<string, Record<string, number>> = {};
        const receivedFromDevAmounts: Record<string, Record<string, number>> = {};
        const level2SpreadWallets: Record<string, {
            recipientAddress: string,
            tokenAddress: string,
            parentAddress: string
        }> = {};
        const highestProcessedUuids: Record<number, number> = {};

        // Track transaction counts per wallet-token pair for validation
        const walletTokenTransactionCounts: Record<string, number> = {};

        // Get the list of staking/vesting contract addresses
        const stakingVestingContracts = DevBundleDataConstants.devStakingAndVestingContracts.map(address => address.toLowerCase());

        try {
            Logger.info(`DevBundleDataService::getWalletTransactions::Starting transaction fetch for ${walletAddresses.length} wallets with ${walletTokenPairs.length} wallet-token pairs`);
            Logger.info(`DevBundleDataService::getWalletTransactions::Wallet addresses: ${JSON.stringify(walletAddresses)}`);
            Logger.debug(`DevBundleDataService::getWalletTransactions::Batch size: ${batchSize}`);
            if (maxValidAmount) {
                Logger.debug(`DevBundleDataService::getWalletTransactions::Max valid amount filter: ${maxValidAmount}`);
            }

            const walletToTokensMap: Record<string, string[]> = {};
            walletTokenPairs.forEach(pair => {
                if (!walletToTokensMap[pair.walletAddress]) {
                    walletToTokensMap[pair.walletAddress] = [];
                }
                walletToTokensMap[pair.walletAddress].push(pair.tokenAddress);
            });

            if (lastProcessedIds) {
                Object.entries(lastProcessedIds).forEach(([schema, lastId]) => {
                    highestProcessedUuids[Number(schema)] = lastId;
                });
                Logger.info(`DevBundleDataService::getWalletTransactions::Starting from last processed IDs: ${JSON.stringify(lastProcessedIds)}`);
            } else {
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS] = 0;
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD] = 0;
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE] = 0;
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED] = 0;
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS] = 0;
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS] = 0;
            }

            let hasMoreData = true;
            let currentBatch = 0;
            let totalProcessedTransactions = 0;
            const processedIds = lastProcessedIds && Object.keys(lastProcessedIds).length > 0 ? lastProcessedIds : {};

            while (hasMoreData) {
                const query = DevBundleDataConstants.walletTransactionsQuery(
                    walletAddresses,
                    processedIds,
                    batchSize,
                    currentBatch * batchSize
                );

                Logger.debug(`DevBundleDataService::getWalletTransactions::Executing batch ${currentBatch + 1} with query: ${query}`);

                const queryResult = await oThis.client.query({ query, format: 'JSONEachRow' });
                const transactions = await queryResult.json();

                Logger.debug(`DevBundleDataService::getWalletTransactions::Raw response for batch ${currentBatch + 1}: ${JSON.stringify(transactions)}`);
                Logger.info(`DevBundleDataService::getWalletTransactions::Batch ${currentBatch + 1}: Found ${transactions?.length || 0} transactions`);

                if (!transactions || transactions.length === 0) {
                    hasMoreData = false;
                    break;
                }

                let skippedTransactions = 0;
                let processedInBatch = 0;
                totalProcessedTransactions += transactions.length;

                for (const transaction of transactions) {
                    const walletAddress = transaction.wallet_address;
                    const tokenAddress = transaction.token_address;
                    const recipientAddress = transaction.to;
                    const transactionAmount = Number(transaction.amount);
                    const isWalletAddress = transaction.is_wallet_address || false;
                    const schema = Number(transaction.schema);
                    const uuid = Number(transaction.uuid);

                    // Log individual transaction details for debugging
                    Logger.info(`DevBundleDataService::getWalletTransactions::Processing transaction - Wallet: ${walletAddress}, Token: ${tokenAddress}, Amount: ${transactionAmount}, UUID: ${uuid}, Schema: ${schema}`);

                    if (schema !== undefined && uuid !== undefined && highestProcessedUuids[schema] < uuid) {
                        highestProcessedUuids[schema] = uuid;
                    }

                    if (isNaN(transactionAmount) || transactionAmount <= 0) {
                        Logger.debug(`DevBundleDataService::getWalletTransactions::Skipping invalid transaction amount: ${transactionAmount} for wallet ${walletAddress} and token ${tokenAddress}`);
                        skippedTransactions++;
                        continue;
                    }

                    const tokensForWallet = walletToTokensMap[walletAddress] || [];
                    if (!tokensForWallet.includes(tokenAddress)) {
                        skippedTransactions++;
                        continue;
                    }

                    const key = `${walletAddress}|${tokenAddress}`;
                    walletTokenTransactionCounts[key] = (walletTokenTransactionCounts[key] || 0) + 1;
                    processedInBatch++;

                    if (isWalletAddress && maxValidAmount && transactionAmount >= maxValidAmount) {
                        const recipientKey = `${recipientAddress}|${tokenAddress}`;

                        if (isLevel2SpreadWallet) {
                            // Continue processing this transaction
                        } else if (shouldIdentifyLevel2Wallets) {
                            if (!level2SpreadWallets[recipientKey]) {
                                level2SpreadWallets[recipientKey] = {
                                    recipientAddress,
                                    tokenAddress,
                                    parentAddress: walletAddress
                                };
                                Logger.debug(`DevBundleDataService::getWalletTransactions::Identified Level 2 spread wallet ${recipientAddress} for token ${tokenAddress} with amount ${transactionAmount}`);
                            }
                            if (!receivedFromDevAmounts[recipientKey]) {
                                receivedFromDevAmounts[recipientKey] = {};
                            }
                            const existingAmount = receivedFromDevAmounts[recipientKey][walletAddress] || 0;
                            receivedFromDevAmounts[recipientKey][walletAddress] = existingAmount + transactionAmount;
                            continue;
                        } else {
                            if (!receivedFromDevAmounts[recipientKey]) {
                                receivedFromDevAmounts[recipientKey] = {};
                            }
                            const existingAmount = receivedFromDevAmounts[recipientKey][walletAddress] || 0;
                            receivedFromDevAmounts[recipientKey][walletAddress] = existingAmount + transactionAmount;
                            Logger.debug(`DevBundleDataService::getWalletTransactions::Skipping large transaction from wallet ${walletAddress} to ${recipientAddress} (amount: ${transactionAmount})`);
                            continue;
                        }
                    }

                    // Track total outflow
                    if (!walletTokenRecipientAmounts[key]) {
                        walletTokenRecipientAmounts[key] = {};
                    }
                    if (!walletTokenRecipientAmounts[key][recipientAddress]) {
                        walletTokenRecipientAmounts[key][recipientAddress] = 0;
                    }
                    walletTokenRecipientAmounts[key][recipientAddress] += transactionAmount;

                    // Track outflow by recipient type (wallet vs contract)
                    if (isWalletAddress) {
                        if (!walletTokenRecipientOutflowToNonDevWalletAmounts[key]) {
                            walletTokenRecipientOutflowToNonDevWalletAmounts[key] = {};
                        }
                        if (!walletTokenRecipientOutflowToNonDevWalletAmounts[key][recipientAddress]) {
                            walletTokenRecipientOutflowToNonDevWalletAmounts[key][recipientAddress] = 0;
                        }
                        walletTokenRecipientOutflowToNonDevWalletAmounts[key][recipientAddress] += transactionAmount;
                    } else {
                        if (!walletTokenRecipientOutflowToContractAmounts[key]) {
                            walletTokenRecipientOutflowToContractAmounts[key] = {};
                        }
                        if (!walletTokenRecipientOutflowToContractAmounts[key][recipientAddress]) {
                            walletTokenRecipientOutflowToContractAmounts[key][recipientAddress] = 0;
                        }
                        walletTokenRecipientOutflowToContractAmounts[key][recipientAddress] += transactionAmount;

                        // Check if this contract is a staking/vesting contract
                        if (stakingVestingContracts.includes(recipientAddress.toLowerCase())) {
                            // Track staking/vesting amounts separately
                            if (!walletTokenRecipientStakedOrVestedAmounts[key]) {
                                walletTokenRecipientStakedOrVestedAmounts[key] = {};
                            }
                            if (!walletTokenRecipientStakedOrVestedAmounts[key][recipientAddress]) {
                                walletTokenRecipientStakedOrVestedAmounts[key][recipientAddress] = 0;
                            }
                            walletTokenRecipientStakedOrVestedAmounts[key][recipientAddress] += transactionAmount;
                            Logger.debug(`DevBundleDataService::getWalletTransactions::Detected staking/vesting: ${walletAddress} sent ${transactionAmount} of ${tokenAddress} to contract ${recipientAddress}`);
                        }
                    }
                }

                // If we got fewer transactions than the batch size, we've reached the end
                if (transactions.length < batchSize) {
                    hasMoreData = false;
                    Logger.debug(`DevBundleDataService::getWalletTransactions::Reached end of data with ${transactions.length} transactions in final batch`);
                }

                currentBatch++;
            }

            if (!hasMoreData && totalProcessedTransactions === 0) {
                Logger.debug("DevBundleDataService::getWalletTransactions::No transactions found for the provided wallets");
                return {
                    transactions: {},
                    receivedFromDevAmounts: {},
                    level2SpreadWallets: shouldIdentifyLevel2Wallets ? {} : undefined,
                    highestProcessedUuids,
                    outflowToNonDevWallet: {},
                    soldAmount: {},
                    stakedOrVestedAmount: {}
                };
            }

            // Log final transaction counts for each wallet-token pair
            Logger.info("DevBundleDataService::getWalletTransactions::Final transaction counts per wallet-token pair:");
            for (const [key, count] of Object.entries(walletTokenTransactionCounts)) {
                const [wallet, token] = key.split('|');
                Logger.info(`- Wallet: ${wallet}, Token: ${token}, Total Transactions: ${count}`);
            }

            const finalWalletTokenAmounts: Record<string, number[]> = {};
            const finalWalletTokenOutflowToNonDevWalletAmounts: Record<string, number[]> = {};
            const finalWalletTokenOutflowToContractAmounts: Record<string, number[]> = {};
            const finalWalletTokenStakedOrVestedAmounts: Record<string, number[]> = {};
            const finalReceivedFromDevAmounts: Record<string, number[]> = {};

            for (const [key, recipients] of Object.entries(walletTokenRecipientAmounts)) {
                finalWalletTokenAmounts[key] = Object.values(recipients);
            }

            for (const [key, recipients] of Object.entries(walletTokenRecipientOutflowToNonDevWalletAmounts)) {
                finalWalletTokenOutflowToNonDevWalletAmounts[key] = Object.values(recipients);
            }

            for (const [key, recipients] of Object.entries(walletTokenRecipientOutflowToContractAmounts)) {
                finalWalletTokenOutflowToContractAmounts[key] = Object.values(recipients);
            }

            for (const [key, recipients] of Object.entries(walletTokenRecipientStakedOrVestedAmounts)) {
                finalWalletTokenStakedOrVestedAmounts[key] = Object.values(recipients);
            }

            for (const [key, senders] of Object.entries(receivedFromDevAmounts)) {
                finalReceivedFromDevAmounts[key] = Object.values(senders);
            }

            const level2Count = shouldIdentifyLevel2Wallets ? Object.keys(level2SpreadWallets).length : 0;
            if (shouldIdentifyLevel2Wallets && level2Count > 0) {
                Logger.debug(`DevBundleDataService::getWalletTransactions::Identified ${level2Count} Level 2 spread wallets during transaction processing`);
            }

            Logger.debug(`DevBundleDataService::getWalletTransactions::Final processing summary:`);
            Logger.debug(`- Total transactions processed: ${totalProcessedTransactions}`);
            Logger.debug(`- Total batches processed: ${currentBatch}`);
            Logger.debug(`- Wallet-token pairs with transactions: ${Object.keys(finalWalletTokenAmounts).length}`);
            Logger.debug(`- Staking/vesting pairs: ${Object.keys(finalWalletTokenStakedOrVestedAmounts).length}`);
            Logger.debug(`- Highest processed UUIDs: ${JSON.stringify(highestProcessedUuids)}`);

            return {
                transactions: finalWalletTokenAmounts,
                receivedFromDevAmounts: finalReceivedFromDevAmounts,
                level2SpreadWallets: shouldIdentifyLevel2Wallets ? level2SpreadWallets : undefined,
                highestProcessedUuids,
                outflowToNonDevWallet: finalWalletTokenOutflowToNonDevWalletAmounts,
                soldAmount: finalWalletTokenOutflowToContractAmounts,
                stakedOrVestedAmount: finalWalletTokenStakedOrVestedAmounts
            };
        } catch (error) {
            Logger.error(`DevBundleDataService::getWalletTransactions::Error fetching or processing transactions: ${error.message}`);
            throw error;
        }
    }

    private async getIncomingTransactions(
        walletAddresses: string[],
        walletTokenPairs: Array<{ walletAddress: string, tokenAddress: string }>,
        lastProcessedIds?: Record<number, number>,
        batchSize: number = DevBundleDataConstants.transactionBatchSize
    ): Promise<{
        transactions: Record<string, number[]>,
        highestProcessedUuids?: Record<number, number>,
        inflowFromAllWallets: Record<string, number[]>,
        buyAmount: Record<string, number[]>
    }> {
        const oThis = this;
        const walletTokenSenderAmounts: Record<string, Record<string, number>> = {};
        const walletTokenInflowFromAllWalletsAmounts: Record<string, Record<string, number>> = {};
        const walletTokenBuyAmounts: Record<string, Record<string, number>> = {};
        const highestProcessedUuids: Record<number, number> = {};

        try {
            Logger.debug(`DevBundleDataService::getIncomingTransactions::Fetching incoming transactions for ${walletAddresses.length} wallets with ${walletTokenPairs.length} wallet-token pairs`);

            const walletToTokensMap: Record<string, string[]> = {};
            walletTokenPairs.forEach(pair => {
                if (!walletToTokensMap[pair.walletAddress]) {
                    walletToTokensMap[pair.walletAddress] = [];
                }
                walletToTokensMap[pair.walletAddress].push(pair.tokenAddress);
            });

            if (lastProcessedIds) {
                Object.entries(lastProcessedIds).forEach(([schema, lastId]) => {
                    highestProcessedUuids[Number(schema)] = lastId;
                });
            } else {
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS] = 0;
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD] = 0;
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE] = 0;
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED] = 0;
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS] = 0;
                highestProcessedUuids[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS] = 0;
            }

            let hasMoreData = true;
            let currentBatch = 0;
            let totalProcessedTransactions = 0;
            const processedIds = lastProcessedIds && Object.keys(lastProcessedIds).length > 0 ? lastProcessedIds : {};

            while (hasMoreData) {
                const query = DevBundleDataConstants.incomingTransactionsQuery(
                    walletAddresses,
                    processedIds,
                    batchSize,
                    currentBatch * batchSize
                );

                const queryResult = await oThis.client.query({ query, format: 'JSONEachRow' });
                const transactions = await queryResult.json();

                Logger.debug(`DevBundleDataService::getIncomingTransactions::Batch ${currentBatch + 1}: Found ${transactions?.length || 0} transactions`);

                if (!transactions || transactions.length === 0) {
                    hasMoreData = false;
                    break;
                }

                totalProcessedTransactions += transactions.length;

                for (const transaction of transactions) {
                    const walletAddress = transaction.wallet_address;
                    const tokenAddress = transaction.token_address;
                    const senderAddress = transaction.from;
                    const transactionAmount = Number(transaction.amount);
                    const isWalletAddress = transaction.is_wallet_address || false;
                    const schema = Number(transaction.schema);
                    const uuid = Number(transaction.uuid);

                    if (schema !== undefined && uuid !== undefined && highestProcessedUuids[schema] < uuid) {
                        highestProcessedUuids[schema] = uuid;
                    }

                    if (isNaN(transactionAmount) || transactionAmount <= 0) continue;

                    const tokensForWallet = walletToTokensMap[walletAddress] || [];
                    if (!tokensForWallet.includes(tokenAddress)) continue;

                    const key = `${walletAddress}|${tokenAddress}`;

                    // Track total inflow
                    if (!walletTokenSenderAmounts[key]) {
                        walletTokenSenderAmounts[key] = {};
                    }
                    if (!walletTokenSenderAmounts[key][senderAddress]) {
                        walletTokenSenderAmounts[key][senderAddress] = 0;
                    }
                    walletTokenSenderAmounts[key][senderAddress] += transactionAmount;

                    // Track inflow by sender type (wallet vs contract)
                    if (isWalletAddress) {
                        if (!walletTokenInflowFromAllWalletsAmounts[key]) {
                            walletTokenInflowFromAllWalletsAmounts[key] = {};
                        }
                        if (!walletTokenInflowFromAllWalletsAmounts[key][senderAddress]) {
                            walletTokenInflowFromAllWalletsAmounts[key][senderAddress] = 0;
                        }
                        walletTokenInflowFromAllWalletsAmounts[key][senderAddress] += transactionAmount;
                    } else {
                        if (!walletTokenBuyAmounts[key]) {
                            walletTokenBuyAmounts[key] = {};
                        }
                        if (!walletTokenBuyAmounts[key][senderAddress]) {
                            walletTokenBuyAmounts[key][senderAddress] = 0;
                        }
                        walletTokenBuyAmounts[key][senderAddress] += transactionAmount;
                    }
                }

                // If we got fewer transactions than the batch size, we've reached the end
                if (transactions.length < batchSize) {
                    hasMoreData = false;
                }

                currentBatch++;
            }

            if (!hasMoreData && totalProcessedTransactions === 0) {
                Logger.debug("DevBundleDataService::getIncomingTransactions::No incoming transactions found for the provided wallets");
                return {
                    transactions: {},
                    highestProcessedUuids,
                    inflowFromAllWallets: {},
                    buyAmount: {}
                };
            }

            const finalWalletTokenAmounts: Record<string, number[]> = {};
            const finalWalletTokenInflowFromAllWalletsAmounts: Record<string, number[]> = {};
            const finalWalletTokenBuyAmounts: Record<string, number[]> = {};

            for (const [key, senders] of Object.entries(walletTokenSenderAmounts)) {
                finalWalletTokenAmounts[key] = Object.values(senders);
            }

            for (const [key, senders] of Object.entries(walletTokenInflowFromAllWalletsAmounts)) {
                finalWalletTokenInflowFromAllWalletsAmounts[key] = Object.values(senders);
            }

            for (const [key, senders] of Object.entries(walletTokenBuyAmounts)) {
                finalWalletTokenBuyAmounts[key] = Object.values(senders);
            }

            Logger.debug(`DevBundleDataService::getIncomingTransactions::Processed ${totalProcessedTransactions} total transactions across ${currentBatch} batches`);
            Logger.debug(`DevBundleDataService::getIncomingTransactions::Processed incoming transaction amounts for ${Object.keys(finalWalletTokenAmounts).length} wallet-token pairs`);
            Logger.debug(`DevBundleDataService::getIncomingTransactions::Highest processed UUIDs tracked: ${JSON.stringify(highestProcessedUuids)}`);

            return {
                transactions: finalWalletTokenAmounts,
                highestProcessedUuids,
                inflowFromAllWallets: finalWalletTokenInflowFromAllWalletsAmounts,
                buyAmount: finalWalletTokenBuyAmounts
            };
        } catch (error) {
            Logger.error(`DevBundleDataService::getIncomingTransactions::Error fetching or processing incoming transactions: ${error.message}`);
            throw error;
        }
    }

    private async getWalletBalances(
        walletAddresses: string[],
        walletTokenPairs: Array<{ walletAddress: string, tokenAddress: string }>,
        virtualAiAgentsLastRunAt: string,
        virtualAiAgentsOldLastRunAt: string,
        virtualAiAgentsPrototypeLastRunAt: string,
        virtualAiAgentsMigratedLastRunAt: string,
        virtualsGenesisAgentsLastRunAt: string,
        virtualsEthereumAgentsLastRunAt: string
    ): Promise<Record<string, number>> {
        const oThis = this;
        const walletTokenBalances: Record<string, number> = {};

        try {
            Logger.debug(`DevBundleDataService::getWalletBalances::Fetching balances for ${walletAddresses.length} wallets with ${walletTokenPairs.length} wallet-token pairs since last run`);

            const walletToTokensMap: Record<string, string[]> = {};
            walletTokenPairs.forEach(pair => {
                if (!walletToTokensMap[pair.walletAddress]) {
                    walletToTokensMap[pair.walletAddress] = [];
                }
                walletToTokensMap[pair.walletAddress].push(pair.tokenAddress);
            });

            const query = DevBundleDataConstants.walletBalancesQuery(
                walletAddresses,
                virtualAiAgentsLastRunAt,
                virtualAiAgentsOldLastRunAt,
                virtualAiAgentsPrototypeLastRunAt,
                virtualAiAgentsMigratedLastRunAt,
                virtualsGenesisAgentsLastRunAt,
                virtualsEthereumAgentsLastRunAt
            );
            Logger.debug(`DevBundleDataService::getWalletBalances::Query: ${query}`);

            const queryResult = await oThis.client.query({ query, format: 'JSONEachRow' });
            const balances = await queryResult.json();

            Logger.debug(`DevBundleDataService::getWalletBalances::Found ${balances?.length || 0} balances for ${walletAddresses.length} wallets since last run`);

            if (!balances || balances.length === 0) {
                Logger.debug("DevBundleDataService::getWalletBalances::No balances found for the provided wallets since last run");
                return walletTokenBalances;
            }

            balances.forEach(balance => {
                const walletAddress = balance.user_address;
                const tokenAddress = balance.token_address;
                const amount = Number(balance.amount);

                const tokensForWallet = walletToTokensMap[walletAddress] || [];
                if (tokensForWallet.includes(tokenAddress)) {
                    const key = `${walletAddress}|${tokenAddress}`;
                    walletTokenBalances[key] = amount;
                }
            });

            Logger.debug(`DevBundleDataService::getWalletBalances::Processed balances for ${Object.keys(walletTokenBalances).length} wallet-token pairs`);
            return walletTokenBalances;
        } catch (error) {
            Logger.error(`DevBundleDataService::getWalletBalances::Error fetching balances: ${error.message}`);
            throw error;
        }
    }

    private async getExistingWalletData(
        walletTokenPairs: Array<{ walletAddress: string, tokenAddress: string }>
    ): Promise<Record<string, {
        totalOutflowAmount: number,
        inflowFromDevWallet?: number,
        outflowToNonDevWallet: number,
        soldAmount: number,
        totalInflowAmount: number,
        buyAmount: number,
        inflowFromAllWallets: number,
        stakedOrVestedAmount: number
    }>> {
        const oThis = this;
        const result: Record<string, {
            totalOutflowAmount: number,
            inflowFromDevWallet?: number,
            outflowToNonDevWallet: number,
            soldAmount: number,
            totalInflowAmount: number,
            buyAmount: number,
            inflowFromAllWallets: number,
            stakedOrVestedAmount: number
        }> = {};

        try {
            const BATCH_SIZE = DevBundleDataConstants.batchSize;
            for (let i = 0; i < walletTokenPairs.length; i += BATCH_SIZE) {
                const batch = walletTokenPairs.slice(i, i + BATCH_SIZE);
                const whereConditions = batch.map(pair => ({
                    devWalletAddress: pair.walletAddress,
                    tokenAddress: pair.tokenAddress
                }));
                const existingRecords = await oThis.devBundleDataModel.findByWalletTokenPairs(whereConditions);

                existingRecords.forEach((record: any) => {
                    const key = `${record.devWalletAddress}|${record.tokenAddress}`;
                    result[key] = {
                        totalOutflowAmount: Number(record.totalOutflowAmount) || 0,
                        inflowFromDevWallet: Number(record.inflowFromDevWallet) || 0,
                        outflowToNonDevWallet: Number(record.outflowToNonDevWallet) || 0,
                        soldAmount: Number(record.soldAmount) || 0,
                        totalInflowAmount: Number(record.totalInflowAmount) || 0,
                        buyAmount: Number(record.buyAmount) || 0,
                        inflowFromAllWallets: Number(record.inflowFromAllWallets) || 0,
                        stakedOrVestedAmount: Number(record.stakedOrVestedAmount) || 0
                    };
                });
            }
            return result;
        } catch (error) {
            Logger.error(`DevBundleDataService::getExistingWalletData::Error fetching wallet data: ${error.message}`);
            return result;
        }
    }

    private async filterRuggedTokens<T extends { tokenAddress: string }>(
        items: T[]
    ): Promise<{ filteredItems: T[], shouldSkip: boolean }> {
        const oThis = this;
        const tokenAddresses = Array.from(new Set(items.map(item => item.tokenAddress)));

        if (tokenAddresses.length === 0) {
            return { filteredItems: items, shouldSkip: false };
        }
        const tokenRugStatus = await oThis.devBundleDataModel.getTokensRugStatus(tokenAddresses);
        const filteredItems = items.filter(item => !tokenRugStatus[item.tokenAddress]);
        Logger.debug(`DevBundleDataService::filterRuggedTokens::Filtered out ${items.length - filteredItems.length} items as non-rugged tokens`);

        const shouldSkip = filteredItems.length === 0;
        if (shouldSkip) {
            Logger.debug("DevBundleDataService::filterRuggedTokens::All items were rugged");
        }
        return { filteredItems, shouldSkip };
    }

    private async updateTransactionAmountsInBatch(
        transactions: Record<string, number[]>,
        outflowToNonDevWallet: Record<string, number[]>,
        soldAmount: Record<string, number[]>,
        receivedFromDevAmounts: Record<string, number[]>,
        inflowFromAllWallets: Record<string, number[]>,
        buyAmount: Record<string, number[]>,
        incomingTransactions: Record<string, number[]>,
        existingData: { [key: string]: any },
        highestProcessedUuids?: Record<number, number>,
        incomingHighestProcessedUuids?: Record<number, number>,
        queryType?: number,
        incomingQueryType?: number,
        stakedOrVestedAmount?: Record<string, number[]>
    ): Promise<void> {
        const oThis = this;
        Logger.debug(`DevBundleDataService::updateTransactionAmountsInBatch::Updating transaction amounts for ${Object.keys(transactions).length} transactions`);
        Logger.info(`DevBundleDataService::updateTransactionAmountsInBatch::Input parameters:
            - outflow queryType: ${queryType}
            - inflow incomingQueryType: ${incomingQueryType}
            - transactions: ${JSON.stringify(transactions)}
            - outflow highestProcessedUuids: ${JSON.stringify(highestProcessedUuids)}
            - inflow highestProcessedUuids: ${JSON.stringify(incomingHighestProcessedUuids)}
            - outflowToNonDevWallet: ${JSON.stringify(outflowToNonDevWallet)}
            - soldAmount: ${JSON.stringify(soldAmount)}
            - receivedFromDevAmounts: ${JSON.stringify(receivedFromDevAmounts)}
            - inflowFromAllWallets: ${JSON.stringify(inflowFromAllWallets)}
            - buyAmount: ${JSON.stringify(buyAmount)}
            - incomingTransactions: ${JSON.stringify(incomingTransactions)}
            - existingData: ${JSON.stringify(existingData)}
            - stakedOrVestedAmount: ${JSON.stringify(stakedOrVestedAmount)}`);

        const sequelize = oThis.devBundleDataModel.getSequelize();
        const transaction = await sequelize.transaction();

        try {
            // Process each transaction type within the same transaction
            for (const [key, amounts] of Object.entries(transactions)) {
                await oThis.updateTransactionAmount(key, amounts, existingData, 'totalOutflowAmount', transaction);
            }

            for (const [key, amounts] of Object.entries(outflowToNonDevWallet)) {
                await oThis.updateTransactionAmount(key, amounts, existingData, 'outflowToNonDevWallet', transaction);
            }

            for (const [key, amounts] of Object.entries(soldAmount)) {
                await oThis.updateTransactionAmount(key, amounts, existingData, 'soldAmount', transaction);
            }

            for (const [key, amounts] of Object.entries(receivedFromDevAmounts)) {
                await oThis.updateTransactionAmount(key, amounts, existingData, 'inflowFromDevWallet', transaction);
            }

            for (const [key, amounts] of Object.entries(inflowFromAllWallets)) {
                await oThis.updateTransactionAmount(key, amounts, existingData, 'inflowFromAllWallets', transaction);
            }

            for (const [key, amounts] of Object.entries(buyAmount)) {
                await oThis.updateTransactionAmount(key, amounts, existingData, 'buyAmount', transaction);
            }

            for (const [key, amounts] of Object.entries(incomingTransactions)) {
                await oThis.updateTransactionAmount(key, amounts, existingData, 'totalInflowAmount', transaction);
            }

            for (const [key, amounts] of Object.entries(stakedOrVestedAmount)) {
                await oThis.updateTransactionAmount(key, amounts, existingData, 'stakedOrVestedAmount', transaction);
            }

            // Update trackers within the same transaction
            if (highestProcessedUuids && queryType) {
                for (const [schema, uuid] of Object.entries(highestProcessedUuids)) {
                    const schemaNum = Number(schema);
                    const uuidNum = Number(uuid);
                    await oThis.updateTrackerWithHigherUuid(
                        schemaNum,
                        queryType,
                        uuidNum,
                        transaction
                    );
                }
            }

            if (incomingHighestProcessedUuids && incomingQueryType) {
                for (const [schema, uuid] of Object.entries(incomingHighestProcessedUuids)) {
                    const schemaNum = Number(schema);
                    const uuidNum = Number(uuid);
                    await oThis.updateTrackerWithHigherUuid(
                        schemaNum,
                        incomingQueryType,
                        uuidNum,
                        transaction
                    );
                }
            }

            await transaction.commit();
        } catch (error) {
            Logger.error(`DevBundleDataService::updateTransactionAmountsInBatch::Error updating transaction amounts: ${error.message}`);
            await transaction.rollback();
            throw error;
        }
    }

    private async updateTransactionAmount(
        key: string,
        amounts: number[],
        existingData: { [key: string]: any },
        fieldName: string,
        transaction?: any
    ): Promise<void> {
        const oThis = this;
        const totalAmount = amounts.reduce((sum, amount) => sum + amount, 0);
        const existingAmount = existingData[key]?.[fieldName] || 0;

        await oThis.devBundleDataModel.update(
            {
                devWalletAddress: key.split('|')[0],
                tokenAddress: key.split('|')[1]
            },
            {
                [fieldName]: existingAmount + totalAmount
            },
            { transaction }
        );
        Logger.debug(`DevBundleDataService::updateTransactionAmount::Updated ${fieldName} for ${key} to ${existingAmount + totalAmount}`);
    }

    private prepareResponse(): SuccessResponse {
        Logger.info("DevBundleDataService::prepareResponse::Data sync completed, stopping service");
        return ResponseHelper.success({});
    }
}
