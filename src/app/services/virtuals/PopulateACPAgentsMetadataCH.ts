import moment from "moment";
import Logger from "../../../lib/Logger";
import NetworkBase from "../../../lib/NetworkBase";
import ResponseHelper from "../../../lib/ResponseHelper";
import { ErrorResponse, SuccessResponse } from "../../../lib/Types";
import ACPAgentsMetadataCH from "../../model/clickhouse/ACPAgentsMetadataCH";
import fetch from "node-fetch";

export default class PopulateACPAgentsMetadataCH extends NetworkBase {

    private acpAgentsMetadataCH: ACPAgentsMetadataCH;
    private totalRecordsProcessed: number = 0;

    constructor(params: any) {
        super(params);
    }

    public async perform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        oThis.acpAgentsMetadataCH = new ACPAgentsMetadataCH();
        await oThis.acpAgentsMetadataCH.createTableIfNotExists();

        await oThis.fetchAndPopulateAgentsData();

        return oThis.prepareResponse();
    }

    private async fetchAndPopulateAgentsData(): Promise<void> {
        const oThis = this;
        Logger.info('PopulateACPAgentsMetadataCH::fetchAndPopulateAgentsData::Fetching ACP agents data from API...');

        let page = 1;
        const pageSize = 100;
        let hasMoreData = true;

        try {
            while (hasMoreData) {
                const apiUrl = oThis.acpAgentsDataUrl(page, pageSize);
                Logger.info(`PopulateACPAgentsMetadataCH::fetchAndPopulateAgentsData::Fetching data from URL: ${apiUrl}`);

                const response = await fetch(apiUrl);
                if (!response.ok) {
                    throw new Error(`Failed to fetch data from API: ${response.statusText}`);
                }

                const res = await response.json();
                const data = res.data;

                if (!data || data.length === 0) {
                    Logger.info('PopulateACPAgentsMetadataCH::fetchAndPopulateAgentsData::No more data from API, exiting loop.');
                    hasMoreData = false;
                    break;
                }

                await oThis.formatAndIngestData(data);
                Logger.info(`PopulateACPAgentsMetadataCH::fetchAndPopulateAgentsData::Page ${page}: Processed ${data.length} records`);
                hasMoreData = data.length === pageSize;
                page += 1;
            }

            Logger.info(`PopulateACPAgentsMetadataCH::fetchAndPopulateAgentsData::Completed processing. Total records: ${oThis.totalRecordsProcessed}`);
        } catch (error: any) {
            Logger.error(`PopulateACPAgentsMetadataCH::fetchAndPopulateAgentsData::Error: ${error}`);
            throw error;
        }
    }

    private async formatAndIngestData(apiData: any[]) {
        const oThis = this;

        Logger.info(`PopulateACPAgentsMetadataCH::formatAndIngestData::Formatting data for ${apiData.length} ACP agents.`);
        try {
            const agentsMetadataObjectArray = oThis.processApiData(apiData);
            const { recordsToUpdate, recordsToInsert, agentsMetadataMap, existingRecordsMap } = await oThis.prepareDataForUpsert(agentsMetadataObjectArray);

            Logger.info(`PopulateACPAgentsMetadataCH::formatAndIngestData::Records to update: ${recordsToUpdate.length}, Records to insert: ${recordsToInsert.length}`);
            Logger.debug(`PopulateACPAgentsMetadataCH::formatAndIngestData::Records to update: ${JSON.stringify(recordsToUpdate)}`);

            await oThis.updateExistingRecords(recordsToUpdate, agentsMetadataMap, existingRecordsMap);
            await oThis.insertNewRecords(recordsToInsert);

            oThis.totalRecordsProcessed += agentsMetadataObjectArray.length;
        } catch (error: any) {
            Logger.error(`PopulateACPAgentsMetadataCH::formatAndIngestData::Error while formatting & ingesting data: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private processApiData(apiData: any[]): any[] {
        const oThis = this;
        const agentsMetadataObjectArray = [];

        for (const agent of apiData) {
            const agentMetadataObject = oThis.createMetadataObject(agent);
            agentsMetadataObjectArray.push(agentMetadataObject);
        }

        return agentsMetadataObjectArray;
    }

    private async prepareDataForUpsert(agentsMetadataObjectArray: any[]): Promise<{
        recordsToUpdate: number[],
        recordsToInsert: any[],
        agentsMetadataMap: Map<number, any>,
        existingRecordsMap: Map<number, any>
    }> {
        const oThis = this;

        const agentsMetadataMap = new Map<number, any>();
        for (const obj of agentsMetadataObjectArray) {
            agentsMetadataMap.set(Number(obj.id), obj);
        }

        const existingRecords = await oThis.acpAgentsMetadataCH.getAllExistingAgents();
        const existingRecordsMap = new Map<number, any>();
        const existingAgentIdsSet = new Set<number>();

        for (const record of existingRecords) {
            const recordId = Number(record.id);
            existingRecordsMap.set(recordId, record);
            existingAgentIdsSet.add(recordId);
        }

        const recordsToUpdate: number[] = [];
        const recordsToInsert: any[] = [];

        for (const [id, metadataObject] of Array.from(agentsMetadataMap.entries())) {
            if (existingAgentIdsSet.has(id)) {
                recordsToUpdate.push(id);
            } else {
                recordsToInsert.push(metadataObject);
            }
        }

        return { recordsToUpdate, recordsToInsert, agentsMetadataMap, existingRecordsMap };
    }

    private async updateExistingRecords(
        recordsToUpdate: number[],
        agentsMetadataMap: Map<number, any>,
        existingRecordsMap: Map<number, any>
    ): Promise<void> {
        const oThis = this;

        if (recordsToUpdate.length === 0) {
            return;
        }

        Logger.info(`PopulateACPAgentsMetadataCH::updateExistingRecords::Checking ${recordsToUpdate.length} existing records for updates`);

        let updateCount = 0;
        for (const id of recordsToUpdate) {
            const newMetadataObject = agentsMetadataMap.get(id);
            const existingRecord = existingRecordsMap.get(id);

            if (newMetadataObject && existingRecord) {
                const updateResult = await oThis.updateRecordIfChanged(id, newMetadataObject, existingRecord);
                if (updateResult) {
                    updateCount++;
                }
            }
        }

        Logger.info(`PopulateACPAgentsMetadataCH::updateExistingRecords::Successfully updated ${updateCount} out of ${recordsToUpdate.length} records (${recordsToUpdate.length - updateCount} skipped - no changes)`);
    }

    private async updateRecordIfChanged(id: number, newMetadataObject: any, existingRecord: any): Promise<boolean> {
        const oThis = this;

        // Extract update values (exclude id from the update)
        const { id: _, ...newUpdateValues } = newMetadataObject;

        // Compare existing record with new values
        let hasChanges = false;
        for (const [key, newValue] of Object.entries(newUpdateValues)) {
            if (existingRecord[key] !== newValue) {
                Logger.debug(`PopulateACPAgentsMetadataCH::updateRecordIfChanged::Updating agent id ${id} - changes detected for key ${key} - existing: ${existingRecord[key]} - new: ${newValue}`);
                hasChanges = true;
                break;
            }
        }

        if (hasChanges) {
            Logger.debug(`PopulateACPAgentsMetadataCH::updateRecordIfChanged::Updating agent id ${id} - changes detected`);
            await oThis.acpAgentsMetadataCH.update(
                { id: id },
                newUpdateValues
            );
            return true;
        } else {
            Logger.debug(`PopulateACPAgentsMetadataCH::updateRecordIfChanged::Skipping agent id ${id} - no changes detected`);
            return false;
        }
    }

    private async insertNewRecords(recordsToInsert: any[]): Promise<void> {
        const oThis = this;

        if (recordsToInsert.length === 0) {
            return;
        }

        Logger.info(`PopulateACPAgentsMetadataCH::insertNewRecords::Bulk inserting ${recordsToInsert.length} new records`);
        await oThis.acpAgentsMetadataCH.bulkCreate(recordsToInsert);
        Logger.info(`PopulateACPAgentsMetadataCH::insertNewRecords::Successfully inserted ${recordsToInsert.length} new records`);
    }

    private createMetadataObject(agent: any): any {
        const oThis = this;
        const agentMetadataObject: any = {
            id: agent.id,
            document_id: agent.documentId,
            name: agent.name,
            description: agent.description,
            wallet_address: agent.walletAddress,
            is_virtual_agent: agent.isVirtualAgent,
            profile_pic: agent.profilePic,
            category: agent.category,
            token_address: agent.tokenAddress,
            owner_address: agent.ownerAddress,
            cluster: agent.cluster,
            twitter_handle: agent.twitterHandle,
            symbol: agent.symbol,
            virtual_agent_id: agent.virtualAgentId,
            created_at: agent.createdAt ? oThis.formatClickhouseDate(agent.createdAt) : null,
            updated_at: agent.updatedAt ? oThis.formatClickhouseDate(agent.updatedAt) : null,
            published_at: agent.publishedAt ? oThis.formatClickhouseDate(agent.publishedAt) : null,
            role: agent.role,
            successful_job_count: agent.successfulJobCount,
            success_rate: agent.successRate,
            unique_buyer_count: agent.uniqueBuyerCount,
            last_active_at: agent.lastActiveAt ? oThis.formatClickhouseDate(agent.lastActiveAt) : null,
            is_self_custody_wallet: agent.isSelfCustodyWallet,
            processing_time: agent.processingTime,
            has_graduated: agent.hasGraduated,
            wallet_balance: agent.walletBalance
        };
        return agentMetadataObject;
    }

    private acpAgentsDataUrl(page: number, pageSize: number = 100): string {
        return `https://acpx.virtuals.io/api/agents?pagination%5Bpage%5D=${page}&pagination%5BpageSize%5D=${pageSize}&sort=name:asc`;
    }

    private formatClickhouseDate(date: string | Date): string {
        return moment(date).format('YYYY-MM-DD HH:mm:ss');
    }

    private prepareResponse(): SuccessResponse {
        Logger.info("PopulateACPAgentsMetadataCH::prepareResponse::Data sync completed, stopping service");
        return ResponseHelper.success({});
    }
}
