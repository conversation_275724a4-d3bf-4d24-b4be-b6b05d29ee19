import Logger from "../../../lib/Logger";
import { SuccessResponse, ErrorResponse } from "../../../lib/Types";
import ResponseHelper from "../../../lib/ResponseHelper";
import VirtualWrapper from "../../../lib/VirtualWrapper/VirtualWrapper";
import Container from "../../../lib/Container";
import CoingeckoConstants from "../../../lib/coingecko/CoingeckoConstants";
import VirtualsConstants from "../../../lib/globalConstant/VirtualsConstants";
import moment from "moment";
import { Op } from "sequelize";
import TokenExtendedDetails from "../../model/TokenExtendedDetails";
import TokenCategoryMap from "../../model/TokenCategoryMap";
import fetch from "node-fetch";
export default class PopulateVirtualTokens {

    private tokenExtendedDetailsModel: TokenExtendedDetails;

    private tokenCategoryMapModel: TokenCategoryMap;

    private chain = { base: 'base', ethereum: 'ethereum' };

    private preservedLastProcessedGenesisTimestamp: number = 0;

    constructor() {
        const oThis = this;
        oThis.tokenExtendedDetailsModel = Container.get().models.tokenExtendedDetails;
        oThis.tokenCategoryMapModel = Container.get().models.tokenCategoryMap;
    }

    public async perform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;

        await oThis.tokenExtendedDetailsModel.createTableIfNotExists();

        await oThis.tokenCategoryMapModel.createTableIfNotExists();

        // Fetching prototype tokens from subgraph and additional token details from virtuals API
        await oThis.fetchAndPopulateTokens(VirtualsConstants.status.PROTOTYPE);

        // Fetching sentient tokens from subgraph and additional token details from virtuals API
        await oThis.fetchAndPopulateTokens(VirtualsConstants.status.SENTIENT);

        // Fetching base genesis tokens from subgraph and additional token details from virtuals API
        await oThis.fetchAndPopulateTokens(VirtualsConstants.status.GENESIS);

        // Fetching ethereum genesis tokens from subgraph and additional token details from virtuals API
        await oThis.fetchAndPopulateTokens(VirtualsConstants.status.GENESIS, oThis.chain.ethereum);

        return oThis.prepareResponse();
    }

    private async getExistingTokenAddresses(statusType: number): Promise<string[]> {
        const oThis = this;
        Logger.info(`PopulateVirtualTokens::getExistingTokenAddresses::Getting existing virtual token addresses...`)
        const existingTokens = await oThis.tokenExtendedDetailsModel.getByStatus(statusType);
        const tokenAddresses: string[] = existingTokens.map(token => token.baseTokenAddress?.toLowerCase());
        Logger.debug(`PopulateVirtualTokens::getExistingTokenAddresses::Type ${statusType} token addresses: ${JSON.stringify(tokenAddresses)}`)
        return tokenAddresses;
    }

    private async fetchAndPopulateTokens(statusType: number, chain = this.chain.base): Promise<void> {
        const oThis = this;
        let batch = 1;
        let offset = 0;
        const limit = 200;
        const chainId = VirtualsConstants.chain[chain.toUpperCase()];
        const tokenStatus = VirtualsConstants.tokenStatusMap[statusType];
        const subgraphEndpoint = VirtualsConstants.subgraphMap[statusType][chain];
        const dateColumn = VirtualsConstants.dateColumnMap[statusType];
        const lastProcessedTimestamp = await oThis.getLastProcessedTimestamp(statusType, dateColumn);
        const existingTokenAddresses = await oThis.getExistingTokenAddresses(statusType);
        const existingTokenAddressesSet = new Set(existingTokenAddresses);

        const displayStatus = VirtualsConstants.invertedStatus[statusType]?.toLowerCase();
        Logger.info(`Processing '${displayStatus}' tokens for '${chain}' chain | Last timestamp: '${lastProcessedTimestamp}' | Existing Tokens: ${existingTokenAddresses?.length} `);
        while (true) {
            Logger.info(`Fetching ${displayStatus} tokens from subgraph... \nEndpoint: ${subgraphEndpoint} \nBatch: ${batch}`);
            const tokens = [];
            const query = statusType == VirtualsConstants.status.GENESIS
                ? oThis.getQueryForGenesis(lastProcessedTimestamp, offset, limit)
                : oThis.getQuery(tokenStatus, dateColumn, lastProcessedTimestamp, offset, limit);
            Logger.info(`Executing Query:: ${query}`);
            const data = await oThis.executeQuery(subgraphEndpoint, query);

            for (let i = 0; i < data.length; i++) {
                const subgraphData = data[i];
                const tokenAddress = subgraphData.id?.toLowerCase();
                Logger.info(`Processing ${displayStatus} token address: '${tokenAddress}'`);

                if (!existingTokenAddressesSet.has(tokenAddress)) {
                    const virtualsData = await VirtualWrapper.fetchTokenDetailsWithAddress(statusType, tokenAddress);
                    const formattedExtendedTokenObject = oThis.getFormattedExtendedTokenObject(subgraphData, virtualsData, statusType);

                    // Always create new prototype tokens
                    if (statusType == VirtualsConstants.status.PROTOTYPE) {
                        tokens.push({ ...formattedExtendedTokenObject, status: VirtualsConstants.status.PROTOTYPE, chain: chainId });
                        continue;
                    }

                    // Always create new genesis tokens
                    if (statusType == VirtualsConstants.status.GENESIS) {
                        tokens.push({ ...formattedExtendedTokenObject, status: VirtualsConstants.status.GENESIS, chain: chainId });
                        continue;
                    }

                    // Update if existing token address is prototype token address
                    if (statusType == VirtualsConstants.status.SENTIENT) {
                        const preTokenAddress = subgraphData.preGraduatedTokenAddress?.toLowerCase();
                        const existingTokenId = await oThis.tokenExtendedDetailsModel.getTokenIdByAddress(preTokenAddress);
                        if (existingTokenId) {
                            Logger.info(`Updating data for existing prototype token address '${preTokenAddress}' | new sentient token address : '${formattedExtendedTokenObject.baseTokenAddress}'`);
                            await oThis.tokenExtendedDetailsModel.update(
                                {
                                    baseTokenAddress: {
                                        [Op.iLike]: preTokenAddress
                                    }
                                },
                                {
                                    baseTokenAddress: formattedExtendedTokenObject.baseTokenAddress,
                                    description: formattedExtendedTokenObject.description,
                                    socials: formattedExtendedTokenObject.socials,
                                    imageUrl: formattedExtendedTokenObject.imageUrl,
                                    updatedAt: formattedExtendedTokenObject.updatedAt,
                                    status: VirtualsConstants.status.SENTIENT,
                                }
                            );
                        } else {
                            tokens.push({ ...formattedExtendedTokenObject, status: VirtualsConstants.status.SENTIENT, chain: chainId });
                        }

                        Logger.info(`Updating token address in current token prices table for existing prototype token address '${preTokenAddress}' | new sentient token address : '${formattedExtendedTokenObject.baseTokenAddress}'`);
                        const currentTokenPricesModel = Container.get().models.tokenPrices;
                        const existingTokenIdInTokenPrices = await currentTokenPricesModel.getTokenIdByAddress(preTokenAddress);
                        if (existingTokenIdInTokenPrices) {
                            await currentTokenPricesModel.update(
                                {
                                    baseAddress: {
                                        [Op.iLike]: preTokenAddress
                                    }
                                },
                                {
                                    baseAddress: formattedExtendedTokenObject.baseTokenAddress
                                }
                            );
                        }
                    }
                }
            }

            await oThis.ingestExtendedTokenDetails(tokens);

            await oThis.ingestCategoryMaps(tokens);

            if (data.length < limit) {
                break;
            }
            offset += limit;
            batch++
        }
    }

    private getQuery(status: string, timestampColumn: string, timestamp: number, offset: number, limit: number) {
        timestampColumn = (timestampColumn == 'updatedAt') ? 'lastUpdatedAt' : timestampColumn;
        return `
            query MyQuery {
                tokens(
                    where: {status: "${status}", ${timestampColumn}_gt: "${timestamp}"}
                    orderBy: ${timestampColumn}
                    orderDirection: asc
                    first: ${limit}
                    skip: ${offset}
                ) {
                    graduatedAt
                    graduatedTxHash
                    name
                    id
                    lastUpdatedAt
                    launchedTxHash
                    status
                    symbol
                    totalSupply
                    devWallet
                    createdAt
                    ${status != 'UNDER_GRADUATED' ? 'preGraduatedTokenAddress' : ''}
                }
            }`;
    }

    private getQueryForGenesis(timestamp: number, offset: number, limit: number) {
        const timestampColumn = 'lastUpdatedAt';
        return `
            query MyQuery {
                tokens(
                    where: {${timestampColumn}_gt: "${timestamp}"}
                    orderBy: ${timestampColumn}
                    orderDirection: asc
                    first: ${limit}
                    skip: ${offset}
                ) {
                    id
                    name
                    symbol
                    totalSupply
                    createdTxHash
                    createdBlockNumber
                    createdAt
                    devWallet
                    lastUpdatedAt
                }
            }`;
    }

    private async executeQuery(endpoint: string, query: string, variables = {}) {
        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    query,
                    variables,
                }),
            });

            if (!response.ok) {
                throw new Error(`Failed to fetch data: ${response.statusText}`);
            }

            const responseBody = await response.json();
            const tokens = responseBody.data?.tokens || [];
            Logger.debug(`Tokens data:: ${JSON.stringify(tokens)}`);
            return tokens;
        } catch (err: any) {
            Logger.error(`Error in fetching tokens: ${err.message}`);
            throw err;
        }
    }

    private async ingestExtendedTokenDetails(tokenData: any[]): Promise<void> {
        const oThis = this;
        try {
            if (tokenData.length > 0) {
                await oThis.tokenExtendedDetailsModel.bulkCreate(tokenData);
                Logger.info(`PopulateVirtualTokens::ingestExtendedTokenDetails::Inserted ${tokenData.length} datasets.`);
            }
        } catch (e) {
            Logger.error(`PopulateVirtualTokens::ingestExtendedTokenDetails::Error: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    private async ingestCategoryMaps(tokenData: any[]) {
        const oThis = this
        let tokenCategoryMapModel = Container.get().models.tokenCategoryMap;
        try {
            let categoryRecords = []
            for (let i = 0; i < tokenData.length; i++) {
                categoryRecords.push({
                    categoryId: VirtualsConstants.virtualsCategoryId,
                    tokenId: tokenData[i].tokenId
                });
            }
            await tokenCategoryMapModel.bulkCreate(categoryRecords)
            Logger.info(`PopulateVirtualTokens::ingestCategoryMaps::Inserted ${categoryRecords.length} datasets.`);
        } catch (e) {
            Logger.error(`PopulateVirtualTokens::ingestCategoryMaps::Error: ${JSON.stringify(e.message)}`);
            throw e;
        }
    }

    private getFormattedExtendedTokenObject(sd: any, vd: any, statusType: number) {
        const tokenName = sd.name;
        let tokenId: string;
        if (statusType == VirtualsConstants.status.PROTOTYPE) {
            tokenId = (`${sd?.id}-prototype`).toLowerCase();
        } else if (statusType == VirtualsConstants.status.GENESIS) {
            tokenId = (tokenName.toLowerCase().replace(/\W+/g, ' ').trim().replace(/\s+/g, '-') + '-genesis').trim();
        } else {
            tokenId = (tokenName.startsWith("fun")
                ? tokenName.toLowerCase().replace("fun", "").replace(/\W+/g, ' ').trim().replace(/\s+/g, '-') + '-from-virtuals'
                : tokenName.toLowerCase().replace(/\W+/g, ' ').trim().replace(/\s+/g, '-') + '-from-virtuals').trim();
        }

        return {
            tempTokenId: tokenId,
            tokenId: tokenId,
            baseTokenAddress: sd?.id || null,
            devWalletAddress: sd?.devWallet || vd?.walletAddress || null,
            createdAt: moment.unix(sd?.createdAt).toISOString(),
            updatedAt: moment.unix(sd?.lastUpdatedAt).toISOString(),
            dataPopulationSource: CoingeckoConstants.dataSources[CoingeckoConstants.geckoTerminal],
            description: vd?.description || null,
            extraData: {
                virtual_id: vd?.id || null,
                virtual_category: vd?.category || null,
            },
            socials: vd?.socials || null,
            imageUrl: vd?.image?.url || null,
            coinGeckoCoinId: null,
        };
    }

    private async getLastProcessedTimestamp(statusType: number, dateColumn: string): Promise<number> {
        const oThis = this;
        if (statusType == VirtualsConstants.status.GENESIS && oThis.preservedLastProcessedGenesisTimestamp) {
            return oThis.preservedLastProcessedGenesisTimestamp;
        } else {
            const lastProcessedTimestamp = await oThis.tokenExtendedDetailsModel.getLastProcessedTimestamp(statusType, dateColumn);
            if (statusType == VirtualsConstants.status.GENESIS) {
                oThis.preservedLastProcessedGenesisTimestamp = lastProcessedTimestamp;
            }
            return lastProcessedTimestamp || 0;
        }
    }

    private prepareResponse(): SuccessResponse {
        Logger.info(`PopulateVirtualTokens::prepareResponse::Service execution completed.`)
        return ResponseHelper.success({});
    }
}
