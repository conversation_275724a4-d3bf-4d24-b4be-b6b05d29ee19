import Logger from "../../../lib/Logger";
import Postgres from "../../../lib/dataStores/destination/Postgres";
import ResponseHelper from "../../../lib/ResponseHelper";
import { ErrorResponse, SuccessResponse } from "../../../lib/Types";
import NetworkBase from "../../../lib/NetworkBase";
import { DataTypes, ModelIndexesOptions, Op } from "sequelize";
import ClickhouseClient from "../../../lib/dataStores/destination/ClickhouseClient";
import moment from "moment-timezone";
import Constant from "../../../config/Constant";

export default class PopulateTopHoldersService extends NetworkBase {

    private topAgentHoldersDataModel: any;

    private clickhouseClient: any;

    private updatedTopAgentHolderIds: number[];

    private virtualTokenAddress: string = '0x0b3e328455c4059eeb9e3f84b5543f74e24e7e1b';

    private constant = {
        POSTGRES_SCHEMA: 'price_oracle',
        HOLDERS_TABLE: 'top_agent_holders',
        TOP_HOLDERS_LIMIT: 25,
        TIMEFRAMES: [0.25, 1, 6, 24], // Timeframe values in hour
        CLICKHOUSE_SCHEMAS: [
            'virtual_token',
            'virtual_ai_agents',
            'virtual_ai_agents_old',
            'virtual_ai_agents_migrated',
            'virtual_ai_agents_prototype',
            'virtuals_genesis_agents',
            'virtuals_ethereum_agents'
        ]
    }

    private clickhouseConfig = {
        host: Constant.clickhouseHost,
        username: Constant.clickhouseUsername,
        password: Constant.clickhousePassword,
        database: null,
        max_open_connections: Constant.clickhouseMaxOpenConnection,
    };

    public constructor(params: {}) {
        super(params);
        const oThis = this;
        oThis.updatedTopAgentHolderIds = [];
    }

    public async perform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        const schemas = oThis.constant.CLICKHOUSE_SCHEMAS;

        await oThis.getModelAndCreateTopAgentHoldersTableIfNotExist();

        for (let i = 0; i < schemas.length; i++) {
            const schema = schemas[i];
            try {
                oThis.clickhouseConfig.database = schema;
                oThis.clickhouseClient = new ClickhouseClient(oThis.clickhouseConfig);

                await oThis.populateTopHolders();

            } catch (error) {
                Logger.error(`PopulateTopHoldersService::perform::Error: ${error.message}`);
                return ResponseHelper.error(error.message);
            }
        }

        if (oThis.updatedTopAgentHolderIds.length > 0) {
            await oThis.handleNonUpdatedTopHolders();
        }

        return oThis.prepareResponse();
    }

    private async populateTopHolders(): Promise<void> {
        const oThis = this;
        let batch = 1;
        let skip = 0;
        const limit = 500;
        const client = oThis.clickhouseClient.getClient();

        while (true) {
            let tokenAddresses = [];
            let tokens = [];

            if (oThis.clickhouseConfig.database == 'virtual_token') {
                tokenAddresses = [oThis.virtualTokenAddress];
            } else {
                let query = `
                        SELECT id, symbol, name
                        FROM tokens FINAL`;

                if (oThis.clickhouseConfig.database === 'virtual_ai_agents') {
                    query += " WHERE status = 'GRADUATED'";
                } else if (oThis.clickhouseConfig.database === 'virtual_ai_agents_prototype') {
                    query += " WHERE status = 'UNDER_GRADUATED'";
                }

                query += `
                        ORDER BY uuid ASC
                        LIMIT ${limit}
                        OFFSET ${skip}`;

                Logger.info(`PopulateTopHoldersService::populateTopHolders::Processing tokens batch '${batch}' from schema '${oThis.clickhouseConfig.database}'. \nQuery: ${query}`);

                const queryResult = await client.query({ query });
                const rows: any = await queryResult.json();
                tokens = rows?.data || [];
                tokenAddresses = tokens.map(token => token.id);
            }


            if (tokenAddresses.length == 0) {
                Logger.info(`PopulateTopHoldersService::populateTopHolders:No tokens to process.`);
                break;
            }

            const existingHoldersBytokenAddress = await oThis.getExistingTokenHoldersByTokenAddresses(tokenAddresses);
            const newHoldersBytokenAddress = await oThis.getTopTokenHoldersByTokenAddresses(tokenAddresses);
            const tokenHoldersDataByTokenAddress = await oThis.getTokenHoldersDataByTokenAddress(tokenAddresses);

            const tokenHoldersToInsert = [];
            const last24Hours = moment().subtract(24, 'hours');

            for (let i = 0; i < tokenAddresses.length; i++) {
                const tokenAddress = tokenAddresses[i];
                const existingHolders = existingHoldersBytokenAddress[tokenAddress] || [];
                const existingHolderAddresses = existingHolders.map(holder => holder.user_address);
                const newHolders = newHoldersBytokenAddress[tokenAddress] || [];
                const newHolderAddresses = newHolders.map(holder => holder.user_address);
                const holdersData = tokenHoldersDataByTokenAddress[tokenAddress];
                let holderBuySellByAddress = {};
                Logger.debug(`Token address: '${tokenAddress}' \nHolders Data: ${JSON.stringify(holdersData)}`);
                if (holdersData) {
                    for (let tIndex = 0; tIndex < holdersData.length; tIndex++) {
                        const hd = holdersData[tIndex];
                        holderBuySellByAddress[hd.user_address] = {
                            total_buy_24h: hd.total_buy_24h,
                            total_sold_24h: hd.total_sold_24h,
                            total_buy_6h: hd.total_buy_6h,
                            total_sold_6h: hd.total_sold_6h,
                            total_buy_1h: hd.total_buy_1h,
                            total_sold_1h: hd.total_sold_1h,
                            total_buy_15m: hd['total_buy_0.25h'],
                            total_sold_15m: hd['total_sold_0.25h']
                        }
                    }
                }
                Logger.info(`Processing token address: '${tokenAddress} :: Holders Existing: ${existingHolders.length}, New: ${newHolders.length} `);

                // Handle new holders insertion and update existing
                for (let i = 0; i < newHolders.length; i++) {
                    const newHolder = newHolders[i];
                    const currentDateTime = moment().format('YYYY-MM-DD HH:mm:ss');
                    const hbs = holderBuySellByAddress[newHolder.user_address];
                    const holderObject = {
                        token_address: tokenAddress,
                        user_address: newHolder.user_address,
                        added_at: currentDateTime,
                        updated_at: currentDateTime,
                        total_buy_24h: hbs?.total_buy_24h || 0,
                        total_sell_24h: hbs?.total_sold_24h || 0,
                        total_buy_6h: hbs?.total_buy_6h || 0,
                        total_sell_6h: hbs?.total_sold_6h || 0,
                        total_buy_1h: hbs?.total_buy_1h || 0,
                        total_sell_1h: hbs?.total_sold_1h || 0,
                        total_buy_15m: hbs?.total_buy_15m || 0,
                        total_sell_15m: hbs?.total_sold_15m || 0
                    };
                    if (!existingHolderAddresses.includes(newHolder.user_address)) {
                        tokenHoldersToInsert.push(holderObject);
                    } else if (holderObject.total_buy_24h != 0 || holderObject.total_sell_24h != 0) {
                        await oThis.update(holderObject)
                    }
                }

                // Handle if existing holder is not come in new holder dataset then mark deleted
                for (let i = 0; i < existingHolders.length; i++) {
                    const existingHolder = existingHolders[i];

                    const currentDateTime = moment().format('YYYY-MM-DD HH:mm:ss');
                    const hbs = holderBuySellByAddress[existingHolder.user_address];
                    const holderObject = {
                        token_address: tokenAddress,
                        user_address: existingHolder.user_address,
                        deleted_at: currentDateTime,
                        updated_at: currentDateTime,
                        total_buy_24h: hbs?.total_buy_24h || 0,
                        total_sell_24h: hbs?.total_sold_24h || 0,
                        total_buy_6h: hbs?.total_buy_6h || 0,
                        total_sell_6h: hbs?.total_sold_6h || 0,
                        total_buy_1h: hbs?.total_buy_1h || 0,
                        total_sell_1h: hbs?.total_sold_1h || 0,
                        total_buy_15m: hbs?.total_buy_15m || 0,
                        total_sell_15m: hbs?.total_sold_15m || 0
                    };

                    if (newHolderAddresses.length > 0) {
                        if (newHolderAddresses.includes(existingHolder.user_address)) {
                            continue;
                        } else {
                            if (!existingHolder.deleted_at) {
                                Logger.info(`Existing token holder '${existingHolder.user_address}' is not present in new holders dataset, Marking deleted...`);
                                await oThis.delete(holderObject);
                                continue;
                            }
                        }
                    }

                    // Handle permanent delete holder record from table if deleted_at is older then 24h
                    if (existingHolder.deleted_at) {
                        if (!moment(existingHolder.deleted_at).isAfter(last24Hours)) {
                            Logger.info(`Existing token holder '${existingHolder.user_address}' is not present in new holders dataset for last 24h, Permanently deleting...`);
                            await oThis.delete(holderObject, false);
                        } else {
                            Logger.info(`Updating soft deleted token holder '${existingHolder.user_address}' because holder was in top 25 holders in last 24h.`);
                            await oThis.update(holderObject)
                        }
                    }
                }
            }

            await oThis.bulkInsert(tokenHoldersToInsert, batch);

            if (oThis.clickhouseConfig.database == 'virtual_token') {
                break;
            }

            if (tokens.length < limit) {
                break;
            }
            skip += limit;
            batch++;
        }
    }

    private async handleNonUpdatedTopHolders() {
        const oThis = this;
        Logger.info(`PopulateTopHoldersService::handleNonUpdatedTopHolders::Updating token holders with no buy/sell transactions in last 24h.`)
        const res = await oThis.topAgentHoldersDataModel.update(
            {
                total_buy_24h: 0,
                total_sell_24h: 0,
                total_buy_6h: 0,
                total_sell_6h: 0,
                total_buy_1h: 0,
                total_sell_1h: 0,
                total_buy_15m: 0,
                total_sell_15m: 0,
                updated_at: moment().format('YYYY-MM-DD HH:mm:ss')
            },
            {
                where: {
                    id: {
                        [Op.notIn]: oThis.updatedTopAgentHolderIds
                    }
                },
            }
        );
        Logger.info(`PopulateTopHoldersService::handleNonUpdatedTopHolders::${res} Records updated successfully.`)
    }

    private async bulkInsert(holders: any[], batch: number): Promise<void> {
        const oThis = this;
        try {
            Logger.info(`PopulateTopHoldersService::bulkInsert::Inserting holders ${JSON.stringify(holders)} | Length: ${holders.length} | Batch: ${batch}`)
            if (holders.length > 0) {
                await oThis.topAgentHoldersDataModel.bulkCreate(holders, { ignoreDuplicates: true });
                Logger.info(`Inserted '${holders.length}' holders successfully in batch ${batch}`);
            }
        } catch (e) {
            Logger.error(`PopulateTopHoldersService::bulkInsert::Error inserting data ${JSON.stringify(e)}`);
            throw e;
        }
    }

    private async update(holder: any): Promise<void> {
        const oThis = this;
        try {
            Logger.debug(`PopulateTopHoldersService::update::Updating existing token holder: ${JSON.stringify(holder)}`)
            const res = await oThis.topAgentHoldersDataModel.update(
                {
                    total_buy_24h: holder.total_buy_24h,
                    total_sell_24h: holder.total_sell_24h,
                    total_buy_6h: holder.total_buy_6h,
                    total_sell_6h: holder.total_sell_6h,
                    total_buy_1h: holder.total_buy_1h,
                    total_sell_1h: holder.total_sell_1h,
                    total_buy_15m: holder.total_buy_15m,
                    total_sell_15m: holder.total_sell_15m,
                    updated_at: holder.updated_at
                },
                {
                    where: {
                        token_address: holder.token_address,
                        user_address: holder.user_address
                    },
                    returning: true,
                }
            );

            const rowsAffected = res[0];
            if (rowsAffected > 0) {
                const updatedRecord = res[1][0];
                const updatedId = updatedRecord.dataValues?.id;
                oThis.updatedTopAgentHolderIds.push(updatedId);
            }
            Logger.debug(`PopulateTopHoldersService::update::Data updated successfully for holder: ${JSON.stringify(holder)}`)
        } catch (e) {
            Logger.error(`PopulateTopHoldersService::update::Error updating data for token address: '${holder.token_address}' and holder address: '${holder.user_address}'`);
            throw e;
        }
    }

    private async delete(holder: any, isSoftDelete = true): Promise<void> {
        const oThis = this;
        if (isSoftDelete) {
            try {
                Logger.info(`PopulateTopHoldersService::delete::Marking holder deleted, deleted_at ${JSON.stringify(holder)} | isSoftDelete: ${isSoftDelete}`)
                await oThis.topAgentHoldersDataModel.update(
                    {
                        deleted_at: holder.deleted_at
                    },
                    {
                        where: {
                            token_address: holder.token_address,
                            user_address: holder.user_address
                        }
                    }
                );
                Logger.debug(`PopulateTopHoldersService::delete::Data updated successfully for holder: ${JSON.stringify(holder)}`)
            } catch (e) {
                Logger.error(`PopulateTopHoldersService::delete::Error updating data for token address: '${holder.token_address}' and holder address: '${holder.user_address}'`);
                throw e;
            }
        } else {
            try {
                Logger.info(`PopulateTopHoldersService::delete::Deleting holder ${JSON.stringify(holder)} | isSoftDelete: ${isSoftDelete}`)
                await oThis.topAgentHoldersDataModel.destroy({
                    where: {
                        token_address: holder.token_address,
                        user_address: holder.user_address
                    }
                });
                Logger.debug(`PopulateTopHoldersService::delete::Data deleted successfully for holder: ${JSON.stringify(holder)}`)
            } catch (e) {
                Logger.error(`PopulateTopHoldersService::delete::Error deleting data for token address: '${holder.token_address}' and holder address: '${holder.user_address}'`);
                throw e;
            }
        }
    }

    private async getTopTokenHoldersByTokenAddresses(addresses: string[]): Promise<Record<string, [{ user_address: string, amount: number }]>> {
        const oThis = this;
        let query: string;
        try {
            const client = oThis.clickhouseClient.getClient();
            Logger.info(`PopulateTopHoldersService::getTopTokenHoldersByTokenAddresses::Fetching top holders from schema '${oThis.clickhouseConfig.database}'.`);
            const formattedAddresses = addresses.map(address => `'${address.trim()}'`).join(', ');

            if (oThis.clickhouseConfig.database == 'virtual_token') {
                query = `SELECT user_address, amount,
                    '${oThis.virtualTokenAddress}' AS token_address
                    FROM token_holders FINAL
                    WHERE is_wallet_address = true
                    ORDER BY amount DESC
                    LIMIT ${oThis.constant.TOP_HOLDERS_LIMIT};`;
            } else {
                query = `SELECT user_address, token_address, amount
                    FROM token_holders FINAL
                    WHERE is_wallet_address = true AND token_address IN (${formattedAddresses})
                    ORDER BY token_address, amount DESC
                    LIMIT ${oThis.constant.TOP_HOLDERS_LIMIT} BY token_address;`;
            }

            Logger.info(`Query :: ${query}`);
            const queryResult = await client.query({ query });
            const rows: any = await queryResult.json();
            Logger.debug(`Rows :: ${JSON.stringify(rows)}`);
            const holders = rows?.data || [];
            const tokenHoldersBytokenAddress: Record<string, [{ user_address: string, amount: number }]> = {};
            for (let i = 0; i < holders.length; i++) {
                const holder = holders[i];
                if (tokenHoldersBytokenAddress[holder.token_address]) {
                    tokenHoldersBytokenAddress[holder.token_address].push({ user_address: holder.user_address, amount: holder.amount });
                } else {
                    tokenHoldersBytokenAddress[holder.token_address] = [{ user_address: holder.user_address, amount: holder.amount }];
                }
            }
            return tokenHoldersBytokenAddress;
        } catch (e) {
            Logger.error(`PopulateTopHoldersService::getTopTokenHoldersByTokenAddresses::Error in getting top holders from schema '${oThis.clickhouseConfig.database}'`);
            throw e;
        }
    }

    private async getExistingTokenHoldersByTokenAddresses(addresses: string[]): Promise<Record<string, [{ user_address: string, added_at: string, deleted_at: string, updated_at: string }]>> {
        const oThis = this;
        try {
            Logger.info(`PopulateTopHoldersService::getExistingTokenHoldersByTokenAddresses::Fetching existing token holders...`);
            const existingHolders = await oThis.topAgentHoldersDataModel.findAll({
                attributes: ["user_address", "token_address", "added_at", "deleted_at", "updated_at"],
                where: {
                    token_address: {
                        [Op.in]: addresses
                    }
                },
                raw: true
            });
            const existingHoldersBytokenAddress: Record<string, [{ user_address: string, added_at: string, deleted_at: string, updated_at: string }]> = {};
            for (let i = 0; i < existingHolders.length; i++) {
                const holder = existingHolders[i];
                if (existingHoldersBytokenAddress[holder.token_address]) {
                    existingHoldersBytokenAddress[holder.token_address].push({ user_address: holder.user_address, added_at: holder.added_at, deleted_at: holder.deleted_at, updated_at: holder.updated_at });
                } else {
                    existingHoldersBytokenAddress[holder.token_address] = [{ user_address: holder.user_address, added_at: holder.added_at, deleted_at: holder.deleted_at, updated_at: holder.updated_at }];
                }
            }
            return existingHoldersBytokenAddress;
        } catch (e) {
            Logger.error(`PopulateTopHoldersService::getExistingTokenHoldersByTokenAddresses::Error in getting existing holders from postgres.`);
            throw e;
        }
    }

    private async getTokenHoldersDataByTokenAddress(tokenAddresses: string[]): Promise<Record<string, any[]>> {
        const oThis = this;
        let query: string;
        let holdersDataByTokenAddress = {};
        try {
            const client = oThis.clickhouseClient.getClient();
            const timeframes = oThis.constant.TIMEFRAMES;
            for (let i = 0; i < timeframes.length; i++) {
                const timeframe = timeframes[i];
                Logger.info(`PopulateTopHoldersService::getTokenHoldersDataByTokenAddress::Fetching token holders buy/sell data for token addresses: '${tokenAddresses}'.`);
                if (oThis.clickhouseConfig.database == 'virtual_token') {
                    query = oThis.generateVirtualTokenQuery(timeframe);
                } else {
                    query = oThis.getQuery(tokenAddresses, timeframe)
                }
                const queryResult = await client.query({ query });
                const result: any = await queryResult.json();
                Logger.info(`query :: ${query} \n\nresult :: ${JSON.stringify(result)}`);
                const data = result?.data || [];

                // Store data for each interval in holdersDataByTokenAddress
                for (let i = 0; i < data.length; i++) {
                    const d = data[i];
                    if (!holdersDataByTokenAddress[d.token_address]) {
                        holdersDataByTokenAddress[d.token_address] = [];
                    }

                    const existingData = holdersDataByTokenAddress[d.token_address].find(hd => hd.user_address === d.user_address);

                    if (existingData) {
                        // Update existing data with the new interval data
                        existingData[`total_buy_${timeframe}h`] = d[`total_buy_${timeframe}h`];
                        existingData[`total_sold_${timeframe}h`] = d[`total_sold_${timeframe}h`];
                    } else {
                        // Add new data entry for this user_address and token_address
                        holdersDataByTokenAddress[d.token_address].push({
                            user_address: d.user_address,
                            [`total_buy_${timeframe}h`]: d[`total_buy_${timeframe}h`],
                            [`total_sold_${timeframe}h`]: d[`total_sold_${timeframe}h`]
                        });
                    }
                }
            }
            return holdersDataByTokenAddress;
        } catch (e) {
            Logger.error(`PopulateTopHoldersService::getTokenHolderBuySell24h::Error in getting holders buy/sell data from schema '${oThis.clickhouseConfig.database}'.`);
            throw e;
        }
    }

    private getQuery(tokenAddresses: string[], timeframe: number): string {
        const oThis = this;
        const formattedAddresses = tokenAddresses.map(address => `'${address.trim()}'`).join(', ');
        const buyAlias = `total_buy_${timeframe}h`;
        const soldAlias = `total_sold_${timeframe}h`;
        return `
            WITH 
                bought_data AS (
                    SELECT to AS user_address, token_address, sum(amount / power(10, 18)) AS buy
                    FROM transfers
                    WHERE time_stamp >= now() - INTERVAL ${timeframe} hour 
                    GROUP BY to, token_address
                ), 
                sold_data AS (
                    SELECT from AS user_address, token_address, sum(amount / power(10, 18)) AS sold
                    FROM transfers
                    WHERE time_stamp >= now() - INTERVAL ${timeframe} hour 
                    GROUP BY from, token_address
                )
            SELECT 
                t.token_address AS token_address, 
                t.user_address AS user_address, 
                round(cast(coalesce(b.buy, 0) as Float64), 1) AS \`${buyAlias}\`, 
                round(cast(coalesce(s.sold, 0) as Float64), 1) AS \`${soldAlias}\`
            FROM 
                (
                    SELECT token_address, user_address, amount / power(10, 18) AS amount 
                    FROM token_holders FINAL
                    WHERE is_wallet_address = true
                    AND user_address != '******************************************'
                ) AS t
            LEFT JOIN bought_data b 
                ON t.token_address = b.token_address 
            AND t.user_address = b.user_address 
            LEFT JOIN sold_data s 
                ON t.token_address = s.token_address 
            AND t.user_address = s.user_address
            WHERE t.token_address IN (${formattedAddresses})
            AND (\`${buyAlias}\` != 0 OR \`${soldAlias}\` != 0)
            ORDER BY t.token_address ASC
        `;
    }

    private generateVirtualTokenQuery(timeframe: number): string {
        const oThis = this;
        const buyAlias = `total_buy_${timeframe}h`;
        const soldAlias = `total_sold_${timeframe}h`;
        return `
                WITH
                    bought_data AS (
                        SELECT '${oThis.virtualTokenAddress}' AS token_address, 
                        to AS user_address,
                        sum(value / power(10, 18)) AS buy
                        FROM transfers
                        WHERE created_at >= now() - INTERVAL ${timeframe} hour
                        GROUP BY to
                    ), 
                    sold_data AS (
                        SELECT '${oThis.virtualTokenAddress}' AS token_address, 
                        from AS user_address,
                        sum(value / power(10, 18)) AS sold
                        FROM transfers
                        WHERE created_at >= now() - INTERVAL ${timeframe} hour
                        GROUP BY from
                    )
                    SELECT 
                    COALESCE(b.token_address, s.token_address) AS token_address,
                    COALESCE(b.user_address, s.user_address) AS user_address,
                    b.buy AS \`${buyAlias}\`,
                    s.sold AS \`${soldAlias}\`
                    FROM sold_data s
                    FULL JOIN bought_data b ON s.user_address = b.user_address
                `;
    }


    private async getModelAndCreateTopAgentHoldersTableIfNotExist(): Promise<void> {
        const oThis = this;
        oThis.topAgentHoldersDataModel = await Postgres.getClient(
            Constant.dappDatabaseConnectionUrl
        ).getDynamicModel(
            oThis.getSchemaForTopAgentHoldersTable(),
            oThis.constant.HOLDERS_TABLE,
            oThis.getIndexesForTopAgentHoldersTable(),
            oThis.constant.POSTGRES_SCHEMA,
        );
        const options: any = { schema: oThis.constant.POSTGRES_SCHEMA };
        options.force = false;
        await oThis.topAgentHoldersDataModel.sync(options);
        Logger.info(`PopulateTopHoldersService::getModelAndCreateTopAgentHoldersTableIfNotExist::Table created Successfully.`);
    }


    private getSchemaForTopAgentHoldersTable() {
        return {
            token_address: {
                type: DataTypes.STRING(80),
                allowNull: false,
            },
            user_address: {
                type: DataTypes.STRING(80),
                allowNull: false,
            },
            added_at: {
                type: DataTypes.DATE,
                allowNull: false
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false
            },
            deleted_at: {
                type: DataTypes.DATE,
                allowNull: true
            },
            total_sell_24h: {
                type: DataTypes.FLOAT,
                allowNull: true,
            },
            total_buy_24h: {
                type: DataTypes.FLOAT,
                allowNull: true,
            },
            total_sell_6h: {
                type: DataTypes.FLOAT,
                allowNull: true,
            },
            total_buy_6h: {
                type: DataTypes.FLOAT,
                allowNull: true,
            },
            total_sell_1h: {
                type: DataTypes.FLOAT,
                allowNull: true,
            },
            total_buy_1h: {
                type: DataTypes.FLOAT,
                allowNull: true,
            },
            total_sell_15m: {
                type: DataTypes.FLOAT,
                allowNull: true,
            },
            total_buy_15m: {
                type: DataTypes.FLOAT,
                allowNull: true,
            }
        }
    }

    private getIndexesForTopAgentHoldersTable(): ModelIndexesOptions[] {
        return [
            {
                unique: true,
                fields: ["token_address", "user_address"],
            },
            {
                unique: false,
                fields: ["token_address"],
            },
            {
                unique: false,
                fields: ["user_address"],
            }
        ];
    }

    private prepareResponse(): SuccessResponse {
        const oThis = this;
        Logger.info(`PopulateTopHoldersService::prepareResponse::Execution completed successfully`);
        return ResponseHelper.success({});
    }
}

