import Postgres from '../../../lib/dataStores/destination/Postgres';
import { QueryTypes } from 'sequelize';
import Logger from "../../../lib/Logger";
import Constant from '../../../config/Constant';
import { ClickHouseConfig, SuccessResponse } from '../../../lib/Types';
import ResponseHelper from '../../../lib/ResponseHelper';
import ClickhouseClient from '../../../lib/dataStores/destination/ClickhouseClient';
import CoingeckoConstants from '../../../lib/coingecko/CoingeckoConstants';
import { ClickHouseDataTypes, ClickHouseIndexes } from '../../../lib/dataStores/destination/ClickHouseConstant';
import moment from 'moment';

export default class PriceOracleMigration {
    private batchSize: number;
    private postgres: Postgres;
    private historicalTokenPricesSchema: string;
    private historicalTokenPricesSourceTable: string;
    private tokenPricesSchema: string;
    private tokenPricesSourceTable: string;
    private chConfig: ClickHouseConfig = {
        host: Constant.clickhouseHost,
        username: Constant.clickhouseUsername,
        password: Constant.clickhousePassword,
        database: Constant.clickhouseDatabasePriceOracle,
        max_open_connections: Constant.clickhouseMaxOpenConnection,
    };
    private clickhouse_client: ClickhouseClient;
    private tableName: string;
    private historicalTokenPricesConfig: { schema: string, sourceTable: string };
    private tokenPricesConfig: { schema: string, sourceTable: string };

    public constructor(params: { tableName: string }) {
        const oThis = this;
        oThis.batchSize = Constant.priceOracleMigrationBatchSize;
        oThis.tableName = params.tableName;
        oThis.postgres = Postgres.getClient(Constant.dappDatabaseConnectionUrl);
        oThis.clickhouse_client = new ClickhouseClient(oThis.chConfig);
        oThis.historicalTokenPricesConfig = Constant.historicalPricesConfig;
        oThis.tokenPricesConfig = Constant.tokenPricesConfig;
        oThis.historicalTokenPricesSchema = oThis.historicalTokenPricesConfig.schema;
        oThis.historicalTokenPricesSourceTable = oThis.historicalTokenPricesConfig.sourceTable;
        oThis.tokenPricesSchema = oThis.tokenPricesConfig.schema;
        oThis.tokenPricesSourceTable = oThis.tokenPricesConfig.sourceTable;
    }

    public async execute(): Promise<void> {
        Logger.info("PriceOracleMigration::execute::Starting execution");
        const oThis = this;
        try {
            if (oThis.tableName === 'historical_token_prices') {
                await oThis.createHistoricalTableIfNotExists();
                await oThis.processHistoricalTokenPricesBatches();
            } else if (oThis.tableName === 'token_prices') {
                await oThis.createTokenPricesTableIfNotExists();
                await oThis.processTokenPricesBatches();
            }
            oThis.prepareResponse();
        } catch (error) {
            Logger.error(`PriceOracleMigration::execute::Error: ${error.message}`);
            throw error;
        }
    }

    private formatClickhouseDate(date: string | Date): string {
        if (date === null || date === undefined) {
            return null;
        }
        return moment(date).format('YYYY-MM-DD HH:mm:ss');
    }

    private formatHistoricalRecords(records: any[]): any[] {
        const oThis = this;
        return records.map(record => ({
            date_time: oThis.formatClickhouseDate(record.date_time),
            token_id: record.token_id,
            token_symbol: record.token_symbol,
            token_name: record.token_name,
            usd_price: record.usd_price,
            total_volume: record.total_volume,
            market_cap: record.market_cap,
        }));
    }

    private formatTokenPricesRecords(records: any[]): any[] {
        const oThis = this;
        return records.map(({ id, ...record }) => ({
            ...record,
            time: oThis.formatClickhouseDate(record.time),
        }));
    }

    private async processHistoricalTokenPricesBatches(): Promise<void> {
        const oThis = this;
        let offset = 0;
        let batchCount = 0;
        while (true) {
            const records = await oThis.prepareHistoricalRecordsBatch(offset);
            const formattedRecords = oThis.formatHistoricalRecords(records);
            if (formattedRecords.length === 0) {
                break; // No more records to process
            }
            await oThis.ingestRecords(formattedRecords, CoingeckoConstants.historicalTokenPricesTableName);
            offset += oThis.batchSize;
            batchCount++;
            Logger.info(`PriceOracleMigration::processHistoricalBatches::Processed batch ${batchCount}`);
        }
        Logger.info(`PriceOracleMigration::processHistoricalBatches::Completed processing all batches`);
    }

    public async processTokenPricesBatches(): Promise<void> {
        const oThis = this;
        let offset = 0;
        let batchCount = 0;
        while (true) {
            const records = await oThis.prepareTokenPricesRecordsBatch(offset);
            const formattedRecords = oThis.formatTokenPricesRecords(records);
            if (formattedRecords.length === 0) {
                break; // No more records to process
            }
            await oThis.ingestRecords(formattedRecords, CoingeckoConstants.tokenPricesTableName);
            offset += oThis.batchSize;
            batchCount++;
            Logger.info(`PriceOracleMigration::processTokenPricesBatches::Processed batch ${batchCount}`);
        }
        Logger.info(`PriceOracleMigration::processTokenPricesBatches::Completed processing all batches`);
    }


    private async prepareHistoricalRecordsBatch(offset: number): Promise<any[]> {
        const oThis = this;
        Logger.info(`PriceOracleMigration::prepareHistoricalRecordsBatch::Preparing records from source table "${oThis.historicalTokenPricesSourceTable}" with offset ${offset}`);
        const data: any = await oThis.postgres.sequelize.query(
            `SELECT date_time, token_id, token_symbol, token_name, usd_price, total_volume, market_cap
             FROM ${oThis.historicalTokenPricesSchema}.${oThis.historicalTokenPricesSourceTable}
             ORDER BY id
             LIMIT :limit OFFSET :offset;`,
            {
                type: QueryTypes.SELECT,
                replacements: { limit: oThis.batchSize, offset: offset }
            }
        );
        Logger.debug(`PriceOracleMigration::prepareHistoricalRecordsBatch::Fetched ${data.length} records`);
        return data;
    }

    private async prepareTokenPricesRecordsBatch(offset: number): Promise<any[]> {
        const oThis = this;
        Logger.info(`PriceOracleMigration::prepareTokenPricesRecordsBatch::Preparing records from source table "${oThis.tokenPricesSourceTable}" with offset ${offset}`);
        const data: any = await oThis.postgres.sequelize.query(
            `SELECT token_id, token_symbol, ethereum_address, polygon_address, fantom_address, xdai_address, binance_chain_address, arbitrum_one_address, moonriver_address, avalanche_address, optimism_address, near_address, moonbeam_address, celo_address, token_name, total_supply, circulating_supply, usd_price, total_volume, market_cap, time, decimal
             FROM ${oThis.tokenPricesSchema}.${oThis.tokenPricesSourceTable}
             ORDER BY token_id
             LIMIT :limit OFFSET :offset;`,
            {
                type: QueryTypes.SELECT,
                replacements: { limit: oThis.batchSize, offset: offset }
            }
        );
        Logger.debug(`PriceOracleMigration::prepareTokenPricesRecordsBatch::Fetched ${data.length} records`);
        return data;
    }


    private async ingestRecords(records: any[], tableName: string): Promise<void> {
        const oThis = this;
        Logger.info(`PriceOracleMigration::ingestRecords::Inserting ${records.length} records into "${tableName}"`);
        if (records.length === 0) {
            Logger.info(`PriceOracleMigration::ingestRecords::No records to insert.`);
            return;
        }
        Logger.info(`PriceOracleMigration::ingestRecords:: Inserting records: ${JSON.stringify(records)}`);

        try {
            await oThis.bulkCreate(records, tableName);
            Logger.info(`PriceOracleMigration::ingestRecords::Successfully inserted records into ClickHouse.`);
        } catch (error) {
            Logger.error(`PriceOracleMigration::ingestRecords::Error inserting records: ${error.message}`);
            throw error;
        }
    }
    public async bulkCreate(values: any[], tableName: string) {
        const oThis = this;
        const client = oThis.clickhouse_client.getClient();
        try {
            await client.insert({
                table: tableName,
                values: values,
                format: "JSONEachRow"
            });
        } catch (error) {
            Logger.error(`PriceOracleMigration::bulkCreate::Error inserting records, Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    public async createHistoricalTableIfNotExists() {
        try {
            const oThis = this;
            const client = oThis.clickhouse_client.getClient();
            const tableCreationQuery = await oThis.clickhouse_client.createMergeTreeTableQuery(
                oThis.chConfig.database,
                CoingeckoConstants.historicalTokenPricesTableName,
                oThis.getHistoricalTokenPricesSchema(),
                oThis.getHistoricalTokenPricesIndexes()
            );
            Logger.info(`PriceOracleMigration::createHistoricalTableIfNotExists::Table creation query: ${tableCreationQuery}`);
            await client.command({
                query: tableCreationQuery
            });
            Logger.info(`PriceOracleMigration::createHistoricalTableIfNotExists::Table created Successfully in Clickhouse.`);
        } catch (e) {
            Logger.error(
                `PriceOracleMigration::createHistoricalTableIfNotExists::Error creating table. Exception ${JSON.stringify(
                    e
                )}`
            );
        }
    }

    public async createTokenPricesTableIfNotExists() {
        try {
            const oThis = this;
            const client = oThis.clickhouse_client.getClient();
            const tableCreationQuery = await oThis.clickhouse_client.createTableQuery(
                oThis.chConfig.database,
                CoingeckoConstants.tokenPricesTableName,
                oThis.getTokenPricesSchema(),
                oThis.getTokenPricesIndexes()
            );
            Logger.info(`PriceOracleMigration::createTokenPricesTableIfNotExists::Table creation query: ${tableCreationQuery}`);
            await client.command({
                query: tableCreationQuery
            });
            Logger.info(`PriceOracleMigration::createTokenPricesTableIfNotExists::Table created Successfully in Clickhouse.`);
        } catch (e) {
            Logger.error(
                `PriceOracleMigration::createTokenPricesTableIfNotExists::Error creating table. Exception ${JSON.stringify(
                    e
                )}`
            );
        }
    }

    private getHistoricalTokenPricesSchema() {
        return {
            date_time: {
                type: ClickHouseDataTypes.DateTime,
                allowNull: false,
            },
            token_id: {
                type: ClickHouseDataTypes.STRING,
                allowNull: false,
            },
            token_symbol: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            token_name: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            usd_price: {
                type: ClickHouseDataTypes.Float64,
                allowNull: true,
            },
            total_volume: {
                type: ClickHouseDataTypes.Float64,
                allowNull: true,
            },
            market_cap: {
                type: ClickHouseDataTypes.Float64,
                allowNull: true,
            },
        };
    }
    private getHistoricalTokenPricesIndexes(): ClickHouseIndexes[] {
        return [
        ];
    }

    private getTokenPricesSchema() {
        return {
            token_id: {
                type: ClickHouseDataTypes.STRING,
                primaryKey: true,
            },
            token_symbol: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            ethereum_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            polygon_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            fantom_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            xdai_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            binance_chain_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            arbitrum_one_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            moonriver_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            avalanche_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            optimism_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            near_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            moonbeam_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            celo_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            base_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            solana_address: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            token_name: {
                type: ClickHouseDataTypes.STRING,
                allowNull: true,
            },
            total_supply: {
                type: ClickHouseDataTypes.Float64,
                allowNull: true,
            },
            circulating_supply: {
                type: ClickHouseDataTypes.Float64,
                allowNull: true,
            },
            usd_price: {
                type: ClickHouseDataTypes.Float64,
                allowNull: true,
            },
            total_volume: {
                type: ClickHouseDataTypes.Float64,
                allowNull: true,
            },
            market_cap: {
                type: ClickHouseDataTypes.Float64,
                allowNull: true,
            },
            time: {
                type: ClickHouseDataTypes.DateTime,
                allowNull: true,
            },
            decimal: {
                type: ClickHouseDataTypes.UInt8,
                allowNull: true,
            }
        };
    }

    private getTokenPricesIndexes(): ClickHouseIndexes[] {
        return [
            {
                fields: ["token_id"],
                type: 'minmax',
                name: "idx_token_prices_token_id"
            },
            {
                fields: ["time"],
                type: 'minmax',
                name: "idx_token_prices_time"
            }
        ];
    }

    private prepareResponse(): SuccessResponse {
        Logger.info(`\x1b[35m${`_____________________SERVICE EXECUTION COMPLETED_____________________`}\x1b[0m`);
        return ResponseHelper.success({});
    }
}
