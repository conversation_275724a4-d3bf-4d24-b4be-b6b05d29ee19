import Logger from "../../../lib/Logger";
import Postgres from "../../../lib/dataStores/destination/Postgres";
import ResponseHelper from "../../../lib/ResponseHelper";
import { ErrorResponse, SuccessResponse } from "../../../lib/Types";
import NetworkBase from "../../../lib/NetworkBase";
import { DataTypes, ModelIndexesOptions, Op, Sequelize } from "sequelize";
import ClickhouseClient from "../../../lib/dataStores/destination/ClickhouseClient";
import Container from "../../../lib/Container";
import Constant from "../../../config/Constant";
import CoingeckoConstants from "../../../lib/coingecko/CoingeckoConstants";
import Utils from "../../../lib/Utils";
import DevBundleTrackerModel from "../../model/DevBundleTrackerModel";

export default class PopulateFirstTokenBuyers extends NetworkBase {

    private firstBuyersDataModel: any;

    private virtualTokenAddress: string;

    private dataToInsert: any[];

    private firstBuyersConfig: any;

    private devBundleTrackerModel: DevBundleTrackerModel;

    private firstBuyersQueryType: number;

    private clickhouseClients: Map<string, any>;

    private clickhouseConfig = {
        host: Constant.clickhouseHost,
        username: Constant.clickhouseUsername,
        password: Constant.clickhousePassword,
        database: null,
        max_open_connections: Constant.clickhouseMaxOpenConnection,
    };

    public constructor(params: {}) {
        super(params);
        const oThis = this;
        oThis.virtualTokenAddress = CoingeckoConstants.virtualTokenAddress;
        oThis.dataToInsert = [];
        oThis.firstBuyersConfig = {
            POSTGRES_SCHEMA: Constant.virtualAgentsInsightsSchema,
            BUYERS_TABLE: CoingeckoConstants.firstBuyersTableName,
            FIRST_BUYERS_LIMIT: CoingeckoConstants.firstBuyersQuerylimit,
            CLICKHOUSE_SCHEMAS: CoingeckoConstants.virtualClickhouseSchemas
        };
        oThis.devBundleTrackerModel = Container.get().models.devBundleTrackerModel;
        oThis.firstBuyersQueryType = Constant.DevBundleQueryTypes.TOKEN_FIRST_BUYERS_QUERY;
        oThis.clickhouseClients = new Map();
    }

    private async getClickhouseClient(schema: string) {
        const oThis = this;
        if (!oThis.clickhouseClients.has(schema)) {
            oThis.clickhouseConfig.database = schema;
            oThis.clickhouseClients.set(schema, new ClickhouseClient(oThis.clickhouseConfig).getClient());
        }
        return oThis.clickhouseClients.get(schema);
    }

    public async perform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        const schemas = oThis.firstBuyersConfig.CLICKHOUSE_SCHEMAS;

        await oThis.getModelAndCreateFirstBuyersTableIfNotExist();
        await oThis.devBundleTrackerModel.createTableIfNotExists();

        for (let i = 0; i < schemas.length; i++) {
            const schema = schemas[i];
            try {

                await oThis.populateFirstBuyersData(schema);

            } catch (error) {
                Logger.error(`PopulateFirstTokenBuyers::perform::Error: ${error.message}`);
                return ResponseHelper.error(error.message);
            }
        }

        await oThis.updateBuyersDetailsFromSchema(schemas.indexOf('virtual_ai_agents'));
        await oThis.updateBuyersDetailsFromSchema(schemas.indexOf('virtual_ai_agents_migrated'));
        await oThis.updateBuyersDetailsFromSchema(schemas.indexOf('virtuals_genesis_agents'));
        await oThis.updateBuyersDetailsFromSchema(schemas.indexOf('virtuals_ethereum_agents'));
        await oThis.updateBuyersDetailsFromSchema(schemas.indexOf('virtual_ai_agents_old'));
        // await oThis.updateBuyersDetailsFromSchema(schemas.indexOf('virtual_ai_agents_prototype'));

        return oThis.prepareResponse();
    }

    private async populateFirstBuyersData(schemaName: string) {
        const oThis = this;
        let batch = 1;
        let skip = 0;
        const limit = CoingeckoConstants.tokenQueryLimit;
        const client = await oThis.getClickhouseClient(schemaName);
        let lastTokenUUID: number;
        let schemaId: number = Constant.DevBundleSchemaIds[oThis.clickhouseConfig.database.toUpperCase()];
        const lastProcessedId = await oThis.devBundleTrackerModel.getLastProcessedIds(oThis.firstBuyersQueryType, [schemaId]);
        Logger.info(`PopulateFirstTokenBuyers::populateFirstBuyersData::Last processed token uuid for schema: ${oThis.clickhouseConfig.database} = ${lastProcessedId[schemaId]}`);
        const skipStatusCheckSchemas: any[] = ['virtual_ai_agents_prototype', 'virtual_ai_agents_migrated', 'virtuals_genesis_agents', 'virtuals_ethereum_agents'];
        const addStatusCondition: boolean = !skipStatusCheckSchemas.includes(schemaName);

        while (true) {
            let tokenAddresses = [];
            let tokens = [];

            if (oThis.clickhouseConfig.database == 'virtual_token') {
                tokenAddresses = [oThis.virtualTokenAddress];
            } else {
                let query = `SELECT uuid, id, symbol, name FROM ${schemaName}.tokens FINAL`;
                query += `
                        WHERE uuid > ${lastProcessedId[schemaId]}
                        ${addStatusCondition ? "AND status='GRADUATED'" : ''}
                        ORDER BY uuid ASC
                        LIMIT ${limit}
                        OFFSET ${skip}`;

                Logger.info(`PopulateFirstTokenBuyers::populateFirstBuyersData::Processing tokens batch '${batch}' from schema '${oThis.clickhouseConfig.database}'. \nQuery: ${query}`);

                const queryResult = await client.query({ query });
                const rows: any = await queryResult.json();
                tokens = rows?.data || [];
                lastTokenUUID = tokens ? tokens[tokens.length - 1]?.uuid : lastProcessedId[schemaId];
                tokenAddresses = tokens.map(token => token.id);
                await Utils.sleep(2000);
            }
            if (tokenAddresses.length == 0) {
                break;
            }
            // for (let i = 0; i < tokenAddresses.length; i++) {
            //     const token_address: string = tokenAddresses[i];
            //     const buyersQuery: string = token_address === oThis.virtualTokenAddress ? oThis.getBuyersDetailsQueryForVirtualToken() : oThis.getBuyersDetailsQuery(token_address, schemaName);
            //     const sellersQuery: string = token_address === oThis.virtualTokenAddress ? oThis.getSellersDetailsQueryForVirtualToken() : oThis.getSellersDetailsQuery(token_address, schemaName);
            //
            //     Logger.debug(`PopulateFirstTokenBuyers::populateFirstBuyersData::BuyersDataQuery: ${buyersQuery}`);
            //     Logger.debug(`PopulateFirstTokenBuyers::populateFirstBuyersData::SellersDataQuery: ${sellersQuery}`);
            //
            //     const [buyersResult, sellersResult] = await Promise.all([
            //         client.query({ query: buyersQuery }),
            //         client.query({ query: sellersQuery }),
            //     ]);
            //
            //     const [buyersData, sellersData] = await Promise.all([
            //         buyersResult.json(),
            //         sellersResult.json(),
            //     ]);
            //
            //     const buyers: any[] = buyersData?.data || [];
            //     const sellers: any[] = sellersData?.data || [];
            //     Logger.info(`PopulateFirstTokenBuyers::populateFirstBuyersData::Buyers data length: ${buyers.length}`);
            //     Logger.info(`PopulateFirstTokenBuyers::populateFirstBuyersData::Sellers data length: ${sellers.length}`);
            //     oThis.processFirstBuyersDataForToken(buyers, sellers);
            // }

            await oThis.createFirstBuyersDataForTokens(tokenAddresses, schemaName, client);
            if (oThis.dataToInsert.length > 0) {
                await oThis.bulkInsert(oThis.dataToInsert, schemaId, lastTokenUUID);
                oThis.dataToInsert = [];
            } else {
                Logger.info(`PopulateFirstTokenBuyers::populateFirstBuyersData::No Data Available for batch: ${batch}, skipping to next iteration...`);
            }

            if (oThis.clickhouseConfig.database == 'virtual_token') {
                break;
            }

            if (tokens.length < limit) {
                break;
            }
            skip += limit;
            batch++;
        }
    }

    private async createFirstBuyersDataForTokens(tokenAddresses: string[], schemaName: string, client: any) {
        const oThis = this;
        const token_addresses: string = tokenAddresses.map(address => `'${address}'`).join(',');
        const buyersQuery: string = schemaName === 'virtual_token' ? oThis.getBuyersDetailsQueryForVirtualToken() : oThis.getBuyersDetailsQuery(token_addresses, schemaName);
        const sellersQuery: string = schemaName === 'virtual_token' ? oThis.getSellersDetailsQueryForVirtualToken() : oThis.getSellersDetailsQuery(token_addresses, schemaName);

        Logger.debug(`PopulateFirstTokenBuyers::populateFirstBuyersData::BuyersDataQuery: ${buyersQuery}`);
        Logger.debug(`PopulateFirstTokenBuyers::populateFirstBuyersData::SellersDataQuery: ${sellersQuery}`);

        const [buyersResult, sellersResult] = await Promise.all([
            client.query({ query: buyersQuery }),
            client.query({ query: sellersQuery }),
        ]);

        const [buyersData, sellersData] = await Promise.all([
            buyersResult.json(),
            sellersResult.json(),
        ]);

        const buyers: any[] = buyersData?.data || [];
        const sellers: any[] = sellersData?.data || [];

        // Segregate buyers by token address
        const buyersByToken: Record<string, any[]> = {};
        for (const buyer of buyers) {
            const tokenAddress = buyer.token_address;
            if (!buyersByToken[tokenAddress]) {
                buyersByToken[tokenAddress] = [];
            }
            buyersByToken[tokenAddress].push(buyer);
        }

        // Segregate sellers by token address
        const sellersByToken: Record<string, any[]> = {};
        for (const seller of sellers) {
            const tokenAddress = seller.token_address;
            if (!sellersByToken[tokenAddress]) {
                sellersByToken[tokenAddress] = [];
            }
            sellersByToken[tokenAddress].push(seller);
        }

        // Process data token-wise
        for (const tokenAddress of tokenAddresses) {
            const tokenBuyers = buyersByToken[tokenAddress] || [];
            const tokenSellers = sellersByToken[tokenAddress] || [];

            Logger.info(`PopulateFirstTokenBuyers::populateFirstBuyersData::Processing token ${tokenAddress}`);
            Logger.info(`PopulateFirstTokenBuyers::populateFirstBuyersData::Buyers data length: ${tokenBuyers.length}`);
            Logger.info(`PopulateFirstTokenBuyers::populateFirstBuyersData::Sellers data length: ${tokenSellers.length}`);

            oThis.processFirstBuyersDataForToken(tokenBuyers, tokenSellers);
        }
    }

    private async updateBuyersDetailsFromSchema(schemaIndex: number) {
        const oThis = this;
        const tokenLimit = CoingeckoConstants.tokenQueryLimit * 2;
        let offset = 0;
        let batch = 1;

        const schema = oThis.firstBuyersConfig.CLICKHOUSE_SCHEMAS[schemaIndex];
        const client = await oThis.getClickhouseClient(schema);

        while (true) {
            // 1. Get tokens in batch
            const query = `
                SELECT uuid, id, symbol, name
                FROM ${schema}.tokens FINAL
                ${oThis.getWhereConditionForTokenTable(schema)}
                ORDER BY uuid ASC
                LIMIT ${tokenLimit} OFFSET ${offset}
            `;
            Logger.debug(`PopulateFirstTokenBuyers::updateBuyersDetailsFromSchema::Processing tokens batch ${batch} from schema '${schema}', query: ${query}`);

            const queryResult = await client.query({ query });
            const rows: any = await queryResult.json();
            const tokens = rows?.data || [];
            const tokenAddresses = tokens.map((t: any) => t.id);

            if (tokenAddresses.length === 0) {
                break;
            }

            await Utils.sleep(2000);

            try {
                // 2. Get all buyers for these tokens in one query
                const buyers = await oThis.firstBuyersDataModel.findAll({
                    attributes: ['token_address', 'wallet_address', 'initial_bought'],
                    where: {
                        token_address: {
                            [Op.in]: tokenAddresses
                        }
                    },
                    raw: true
                });

                if (buyers.length === 0) {
                    Logger.info(`PopulateFirstTokenBuyers::updateBuyersDetailsFromSchema::No buyers found for batch ${batch}, skipping to next iteration...`);
                    if (tokens.length < tokenLimit) {
                        break;
                    }
                    offset += tokenLimit;
                    batch++;
                    continue;
                }

                // Create maps for efficient lookups
                const buyersByToken = new Map<string, Set<string>>();
                const buyersData = new Map<string, any>();

                for (const buyer of buyers) {
                    const tokenAddress = buyer.token_address;
                    const walletAddress = buyer.wallet_address.toLowerCase();

                    if (!buyersByToken.has(tokenAddress)) {
                        buyersByToken.set(tokenAddress, new Set());
                    }
                    buyersByToken.get(tokenAddress)!.add(walletAddress);

                    const key = `${tokenAddress}_${walletAddress}`;
                    buyersData.set(key, buyer);
                }

                Logger.debug(`PopulateFirstTokenBuyers::updateBuyersDetailsFromSchema::Token holders by token for batch ${batch}: ${JSON.stringify(buyersByToken)}`);
                Logger.debug(`PopulateFirstTokenBuyers::updateBuyersDetailsFromSchema::Token holders data for batch ${batch}: ${JSON.stringify(buyersData)}`);

                // 3. Get holders data only for specific token-wallet combinations
                const holderQueries = [];
                for (const [tokenAddress, walletAddresses] of buyersByToken.entries()) {
                    const walletList = Array.from(walletAddresses).map(addr => `'${addr}'`).join(',');
                    holderQueries.push(`
                        SELECT token_address, user_address, amount, last_updated_at
                        FROM ${schema}.token_holders
                        WHERE token_address = '${tokenAddress}'
                        AND user_address IN (${walletList})
                        AND last_updated_at >= (now() - INTERVAL 6 HOUR)
                    `);
                }

                const holderQuery = holderQueries.join(' UNION ALL ');
                Logger.debug(`PopulateFirstTokenBuyers::updateBuyersDetailsFromSchema::Token holders query for batch ${batch}: ${holderQuery}`);

                const holderResult = await client.query({ query: holderQuery });
                const holderRows: any = await holderResult.json();
                const holders = holderRows?.data || [];

                if (holders.length === 0) {
                    Logger.info(`PopulateFirstTokenBuyers::updateBuyersDetailsFromSchema::No holders found for batch ${batch}, skipping to next iteration...`);
                    if (tokens.length < tokenLimit) {
                        break;
                    }
                    offset += tokenLimit;
                    batch++;
                    continue;
                }

                // 4. Prepare bulk updates
                const bulkUpdates = [];
                for (const holder of holders) {
                    const key = `${holder.token_address}_${holder.user_address.toLowerCase()}`;
                    const buyer = buyersData.get(key);

                    if (!buyer) continue;

                    const amount = Number(holder.amount);
                    const status = buyer.initial_bought > amount
                        ? CoingeckoConstants.tokenBuyerStatus.SOLD
                        : buyer.initial_bought < amount
                            ? CoingeckoConstants.tokenBuyerStatus.BOUGHT
                            : CoingeckoConstants.tokenBuyerStatus.HOLD;

                    bulkUpdates.push({
                        token_address: holder.token_address,
                        wallet_address: holder.user_address.toLowerCase(),
                        current_holdings: amount,
                        status: status,
                        updated_at: typeof (holder.last_updated_at) === 'number'
                            ? new Date(holder.last_updated_at * 1000).toISOString()
                            : holder.last_updated_at
                    });
                }

                // 5. Execute bulk update
                if (bulkUpdates.length > 0) {
                    let successCount = 0;
                    let errorCount = 0;

                    for (const update of bulkUpdates) {
                        try {
                            await oThis.firstBuyersDataModel.update(
                                {
                                    current_holdings: update.current_holdings,
                                    status: update.status,
                                    updated_at: update.updated_at
                                },
                                {
                                    where: {
                                        token_address: update.token_address,
                                        wallet_address: update.wallet_address
                                    }
                                }
                            );
                            successCount++;
                        } catch (error) {
                            errorCount++;
                            Logger.error(`PopulateFirstTokenBuyers::updateBuyersDetailsFromSchema::Error updating token ${update.token_address} for wallet ${update.wallet_address}: ${error.message}`);
                        }
                    }
                    Logger.info(`PopulateFirstTokenBuyers::updateBuyersDetailsFromSchema::Batch ${batch} complete - Successfully updated: ${successCount}, Failed: ${errorCount}`);
                }

            } catch (e) {
                Logger.error(`PopulateFirstTokenBuyers::updateBuyersDetailsFromSchema::Error while updating values for batch ${batch}: ${e}`);
            }

            if (tokens.length < tokenLimit) {
                Logger.info(`PopulateFirstTokenBuyers::updateBuyersDetailsFromSchema::No more tokens available to process.`);
                break;
            }
            batch++;
            offset += tokenLimit;
        }
    }

    private getWhereConditionForTokenTable(schemaName: string): string {
        const oThis = this;
        let whereCondition = '';
        switch (schemaName) {
            case 'virtual_ai_agents': {
                whereCondition = `WHERE status = 'GRADUATED'`;
                break;
            }
            case 'virtual_ai_agents_old': {
                whereCondition = `WHERE status = 'GRADUATED'`;
                break;
            }
            case 'virtual_ai_agents_prototype': {
                whereCondition = `WHERE status = 'UNDER_GRADUATED'`;
                break;
            }
            default: {
                break;
            }
        }
        return whereCondition
    }

    private processFirstBuyersDataForToken(buyersData: any[], sellers: any[]) {
        const oThis = this;
        const sellersDataByWallet: Map<string, any> = new Map();
        for (const seller of sellers) {
            if (seller.seller_wallet_address) {
                sellersDataByWallet.set(seller.seller_wallet_address.toLowerCase(), seller);
            }
        }
        for (let i = 0; i < buyersData.length; i++) {
            try {
                const buyer = buyersData[i];
                const walletAddress = buyer.wallet_address?.toLowerCase();
                let formattedBuyersData = oThis.getFormattedBuyerData(buyer);
                let isSniper = false;

                if (walletAddress && sellersDataByWallet.has(walletAddress)) {
                    const seller = sellersDataByWallet.get(walletAddress);
                    Logger.info(`PopulateFirstTokenBuyers::processFirstBuyersDataForToken:: Sell Information for wallet: ${walletAddress} & token: ${buyer.token_address}, sellerInfo: ${JSON.stringify(seller)}`);
                    const boughtAt = new Date(buyer.first_bought_at).getTime();
                    const soldAt = new Date(seller.first_sold_at).getTime();
                    const timeDiffInMinutes = (soldAt - boughtAt) / (1000 * 60);
                    const initialBought = parseFloat(buyer.initial_bought);
                    const initialSold = parseFloat(seller.initial_sold);

                    if (timeDiffInMinutes < CoingeckoConstants.sniperTimeLimitInMinutes && initialSold >= CoingeckoConstants.sniperThresholdPercentage * initialBought) {
                        Logger.info(`PopulateFirstTokenBuyers::processFirstBuyersDataForToken::Sniper Detected!, walletAddress: ${walletAddress}, token address: ${buyer.token_address}.`);
                        isSniper = true;
                    }
                }

                formattedBuyersData.is_sniper = isSniper;
                oThis.dataToInsert.push(formattedBuyersData);
            } catch (e) {
                Logger.error(`PopulateFirstTokenBuyers::processFirstBuyersDataForToken:: Error while processing first buyers data, Error: ${JSON.stringify(e)}`);
            }
        }
        Logger.debug(`PopulateFirstTokenBuyers::processFirstBuyersDataForToken::Buyers data: ${JSON.stringify(oThis.dataToInsert)}`);
        Logger.info(`PopulateFirstTokenBuyers::processFirstBuyersDataForToken::Buyers data update length: ${oThis.dataToInsert.length}`);
    }

    private getFormattedBuyerData(buyer: any) {
        const dataObject: any = {
            token_address: buyer?.token_address,
            wallet_address: buyer?.wallet_address,
            initial_bought: buyer?.initial_bought,
            current_holdings: buyer?.current_holdings,
            first_bought_at: buyer?.first_bought_at,
            updated_at: typeof (buyer.last_updated_at) === 'number' ? new Date(buyer.last_updated_at * 1000).toISOString() : buyer.last_updated_at,
            status: buyer.initial_bought > buyer.current_holdings ? CoingeckoConstants.tokenBuyerStatus['SOLD'] : buyer.initial_bought < buyer.current_holdings ? CoingeckoConstants.tokenBuyerStatus['BOUGHT'] : CoingeckoConstants.tokenBuyerStatus['HOLD'],
        }
        return dataObject;
    }

    private getBuyersDetailsQuery(token_addresses: string, schemaName: string): string {
        // const query: string = `
        //     SELECT
        //         argMin(t.id, t.time_stamp) AS id,
        //         argMin(t.from, t.time_stamp) AS from,
        //         th.user_address AS wallet_address,
        //         argMin(t.to, t.time_stamp) AS to,
        //         t.token_address,
        //         argMin(t.amount, t.time_stamp) AS initial_bought,
        //         argMin(t.time_stamp, t.time_stamp) AS first_bought_at,
        //         argMin(t.tx_hash, t.time_stamp) AS tx_hash,
        //         argMax(th.amount, th.last_updated_at) AS current_holdings,
        //         argMax(th.last_updated_at, th.last_updated_at) AS last_updated_at
        //     FROM ${schemaName}.transfers t
        //     INNER JOIN ${schemaName}.token_holders th
        //         ON t.to = th.user_address
        //         AND t.token_address = th.token_address
        //     WHERE t.token_address = '${token_address}'
        //     AND th.is_wallet_address = 1
        //     GROUP BY th.user_address, t.token_address
        //     ORDER BY first_bought_at ASC
        //     LIMIT ${this.firstBuyersConfig.FIRST_BUYERS_LIMIT};
        // `;

        const query: string = `
        WITH ranked_transfers AS (
            SELECT
                argMin(t.id, t.time_stamp) AS id,
                argMin(t.from, t.time_stamp) AS from,
                th.user_address AS wallet_address,
                argMin(t.to, t.time_stamp) AS to,
                t.token_address,
                argMin(t.amount, t.time_stamp) AS initial_bought,
                argMin(t.time_stamp, t.time_stamp) AS first_bought_at,
                argMin(t.tx_hash, t.time_stamp) AS tx_hash,
                argMax(th.amount, th.last_updated_at) AS current_holdings,
                argMax(th.last_updated_at, th.last_updated_at) AS last_updated_at,
                ROW_NUMBER() OVER (PARTITION BY t.token_address ORDER BY argMin(t.time_stamp, t.time_stamp) ASC) AS rn
            FROM ${schemaName}.transfers t
            INNER JOIN ${schemaName}.token_holders th
                ON t.to = th.user_address
                AND t.token_address = th.token_address
            WHERE t.token_address IN (${token_addresses})
              AND th.is_wallet_address = 1
            GROUP BY th.user_address, t.token_address
        )
        SELECT *
        FROM ranked_transfers
        WHERE rn <= ${this.firstBuyersConfig.FIRST_BUYERS_LIMIT}
        ORDER BY token_address, first_bought_at;
        `
        return query;
    }

    private getSellersDetailsQuery(token_addresses: string, schemaName: string): string {
        // return `
        //     SELECT
        //         argMin(t.id, t.time_stamp) AS id,
        //         argMin(t.from, t.time_stamp) AS seller_wallet_address,
        //         argMin(t.to, t.time_stamp) AS to,
        //         t.token_address,
        //         argMin(t.amount, t.time_stamp) AS initial_sold,
        //         argMin(t.time_stamp, t.time_stamp) AS first_sold_at,
        //         argMin(t.tx_hash, t.time_stamp) AS tx_hash
        //     FROM ${schemaName}.transfers t
        //     INNER JOIN ${schemaName}.token_holders th
        //         ON t.from = th.user_address
        //         AND t.token_address = th.token_address
        //         AND th.is_wallet_address = 1
        //     WHERE t.token_address = '${token_address}'
        //     GROUP BY t.from, t.token_address
        //     ORDER BY first_sold_at ASC
        //     LIMIT ${this.firstBuyersConfig.FIRST_BUYERS_LIMIT};
        // `;
        return `
        WITH ranked_sales AS (
            SELECT
                argMin(t.id, t.time_stamp) AS id,
                argMin(t.from, t.time_stamp) AS seller_wallet_address,
                argMin(t.to, t.time_stamp) AS to,
                t.token_address,
                argMin(t.amount, t.time_stamp) AS initial_sold,
                argMin(t.time_stamp, t.time_stamp) AS first_sold_at,
                argMin(t.tx_hash, t.time_stamp) AS tx_hash,
                ROW_NUMBER() OVER (PARTITION BY t.token_address ORDER BY argMin(t.time_stamp, t.time_stamp) ASC) AS rn
            FROM ${schemaName}.transfers t
            INNER JOIN ${schemaName}.token_holders th
                ON t.from = th.user_address
                AND t.token_address = th.token_address
                AND th.is_wallet_address = 1
            WHERE t.token_address IN (${token_addresses})
            GROUP BY t.from, t.token_address
        )
        SELECT *
        FROM ranked_sales
        WHERE rn <= ${this.firstBuyersConfig.FIRST_BUYERS_LIMIT}
        ORDER BY token_address, first_sold_at;
        `
    }

    private getBuyersDetailsQueryForVirtualToken(): string {
        return `
            SELECT
                argMin(t.id, t.created_at) AS id,
                '${this.virtualTokenAddress}' AS token_address,
                argMin(t.from, t.created_at) AS from,
                th.user_address AS wallet_address,
                argMin(t.to, t.created_at) AS to,
                argMin(t.value, t.created_at) AS initial_bought,
                argMin(t.created_at, t.created_at) AS first_bought_at,
                argMin(t.tx_hash, t.created_at) AS tx_hash,
                argMax(th.amount, th.last_updated_at) AS current_holdings,
                argMax(th.last_updated_at, th.last_updated_at) AS last_updated_at
            FROM virtual_token.transfers t
            INNER JOIN virtual_token.token_holders th
                ON t.to = th.user_address
            WHERE th.is_wallet_address = 1
            GROUP BY th.user_address
            ORDER BY first_bought_at ASC
            LIMIT ${this.firstBuyersConfig.FIRST_BUYERS_LIMIT};
        `;
    }

    private getSellersDetailsQueryForVirtualToken(): string {
        return `
            SELECT
                argMin(t.id, t.created_at) AS id,
                '${this.virtualTokenAddress}' AS token_address,
                argMin(t.from, t.created_at) AS seller_wallet_address,
                argMin(t.to, t.created_at) AS to,
                argMin(t.value, t.created_at) AS initial_sold,
                argMin(t.created_at, t.created_at) AS first_sold_at,
                argMin(t.tx_hash, t.created_at) AS tx_hash
            FROM virtual_token.transfers t
            INNER JOIN virtual_token.token_holders th
                ON t.from = th.user_address
                AND th.is_wallet_address = 1
            GROUP BY t.from
            ORDER BY first_sold_at ASC
            LIMIT ${this.firstBuyersConfig.FIRST_BUYERS_LIMIT};
        `;
    }

    private async bulkInsert(buyers: any[], clickhouseSchema: number, lastTokenUUID: number): Promise<void> {
        const oThis = this;
        try {
            Logger.info(`PopulateFirstTokenBuyers::bulkInsert::Inserting buyers ${JSON.stringify(buyers)} | Length: ${buyers.length}.`)
            await oThis.firstBuyersDataModel.bulkCreate(buyers, {
                updateOnDuplicate: [
                    'current_holdings',
                    'status',
                    'updated_at'
                ]
            });
            Logger.info(`PopulateFirstTokenBuyers::bulkInsert::Inserted '${buyers.length}' buyers successfully.`);
            await Utils.sleep(2000);
            if (Object.values(Constant.DevBundleSchemaIds).includes(clickhouseSchema)) {
                const isTrackerExists: boolean = await oThis.devBundleTrackerModel.doesTrackerExist(clickhouseSchema, oThis.firstBuyersQueryType);
                const now = new Date().toISOString();
                if (isTrackerExists) {
                    await oThis.devBundleTrackerModel.updateTracker(clickhouseSchema, oThis.firstBuyersQueryType, {
                        lastRunId: lastTokenUUID,
                        lastRunAt: now
                    });
                    Logger.info(`PopulateFirstTokenBuyers::bulkInsert::Successfully updated tracker entry for schema: ${clickhouseSchema}, queryType: ${oThis.firstBuyersQueryType}`);
                } else {
                    await oThis.devBundleTrackerModel.create({
                        schema: clickhouseSchema,
                        queryType: oThis.firstBuyersQueryType,
                        lastRunId: lastTokenUUID,
                        lastRunAt: now,
                        createdAt: now,
                        updatedAt: now
                    });
                }
                Logger.info(`PopulateFirstTokenBuyers::bulkInsert::Successfully created new tracker entry for schema: ${clickhouseSchema}, queryType: ${oThis.firstBuyersQueryType}`);
                oThis.dataToInsert = [];
            }
        } catch (e) {
            Logger.error(`PopulateFirstTokenBuyers::bulkInsert::Error inserting data ${JSON.stringify(e)}`);
            oThis.dataToInsert = [];
            throw e;
        }
    }

    private async getModelAndCreateFirstBuyersTableIfNotExist(): Promise<void> {
        const oThis = this;
        oThis.firstBuyersDataModel = await Postgres.getClient(
            Constant.dappDatabaseConnectionUrl
        ).getDynamicModel(
            oThis.getSchemaForFirstBuyersTable(),
            oThis.firstBuyersConfig.BUYERS_TABLE,
            oThis.getIndexesForFirstBuyersTable(),
            oThis.firstBuyersConfig.POSTGRES_SCHEMA,
        );
        const options: any = { schema: oThis.firstBuyersConfig.POSTGRES_SCHEMA };
        options.force = false;
        await oThis.firstBuyersDataModel.sync(options);
        Logger.info(`PopulateFirstTokenBuyers::getModelAndCreateFirstBuyersTableIfNotExist::Table created Successfully.`);
    }

    private getSchemaForFirstBuyersTable() {
        return {
            token_address: {
                type: DataTypes.STRING(80),
                allowNull: false,
                primaryKey: true
            },
            wallet_address: {
                type: DataTypes.STRING(80),
                allowNull: false,
                primaryKey: true
            },
            initial_bought: {
                type: DataTypes.FLOAT,
                allowNull: true
            },
            current_holdings: {
                type: DataTypes.FLOAT,
                allowNull: true
            },
            is_sniper: {
                type: DataTypes.BOOLEAN,
                defaultValue: false,
                allowNull: true
            },
            status: {
                type: DataTypes.SMALLINT,
                allowNull: true,
            },
            first_bought_at: {
                type: DataTypes.DATE,
                allowNull: false
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false
            }
        }
    }

    private getIndexesForFirstBuyersTable(): ModelIndexesOptions[] {
        return [
            {
                unique: false,
                fields: ["status"],
            }
        ];
    }

    private prepareResponse(): SuccessResponse {
        Logger.info(`PopulateFirstTokenBuyers::prepareResponse::Execution completed successfully`);
        return ResponseHelper.success({});
    }
}
