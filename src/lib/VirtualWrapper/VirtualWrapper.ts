import fetch from "node-fetch";
import Logger from "../Logger";
import VirtualsConstants from "../globalConstant/VirtualsConstants";
import Utils from "../Utils";

export class VirtualWrapper {

    public async fetchTokenDetailsWithAddress(statusType: number, address: string) {
        try {
            const endpoint = statusType == VirtualsConstants.status.PROTOTYPE
                ? VirtualsConstants.prototypeTokenAPI(address)
                : VirtualsConstants.sentientTokenAPI(address);
            const response = await fetch(endpoint);
            const result = await response.json();
            return result?.data[0] || null;
        } catch (e) {
            Logger.error(`VirtualWrapper::fetchTokenDetailsWithAddress::Error: ${JSON.stringify(e)}`)
        }
    }

    public async fetchDataFromGeckoTerminal(network: string, tokenAddresses: string) {
        let retries: number = 2;
        network = network == 'ethereum' ? 'eth' : network;
        for (let attempt = 0; attempt < retries; attempt++) {
            const response = await fetch(VirtualsConstants.geckoTerminalAPI(network, tokenAddresses));
            const result = await response.json();

            if (result.status === '429' && attempt < retries) {
                Logger.debug(`VirtualWrapper::fetchDataFromGeckoTerminal:: Rate limit hit, retrying again, attempt: ${attempt + 1}, attempt limit: ${retries}...`);
                await Utils.sleep(60000); // Sleep for 1 min
                continue;
            }
            return result.data?.map((item: any) => item.attributes) || [];
        }
        Logger.info(`VirtualWrapper::fetchDataFromGeckoTerminal::Data not available for addresses: ${JSON.stringify(tokenAddresses)}`);
        return [];
    }
}
export default new VirtualWrapper();
