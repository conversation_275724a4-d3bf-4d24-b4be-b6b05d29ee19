import Logger from "../Logger";
import fetch from "node-fetch"

export class Dex<PERSON><PERSON><PERSON>Helper {

    public async getTokenMarketDataByAddresses(chainId: string, tokenAddresses: string[]): Promise<any[]> {
        const addresses: string = tokenAddresses.join(',');
        const url: string = `${DexScreenerHelper.tokenDataUrl()}${chainId}/${addresses}`;
        try {
            Logger.debug(`DexScreenerHelper::getTokenMarketDataByAddresses::Fetching token data from dex screener.`);
            const response: any = await fetch(url);
            const res: any = await response.json();
            return res;
        } catch (error: any) {
            Logger.error(`DexScreenerHelper::getTokenMarketDataByAddresses::Error while fetching data from dexscreener, error: ${JSON.stringify(error.message)}`);
            return [];
        }
    }

    private static tokenDataUrl(): string {
        return `https://api.dexscreener.com/tokens/v1/`;
    }
}
