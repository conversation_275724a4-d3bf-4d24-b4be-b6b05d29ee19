import fetch from 'node-fetch';
import CoingeckoConstants from '../CoingeckoConstants';
import { CoingeckoGetTokenDetailsResponse } from '../../../Types';
import Logger from "../../../lib/Logger";

export default class Wrapper {

    public async getHistoricalTokenPricesData(tokenId: string, date: string) {
        const response = await fetch(`https://api.coingecko.com/api/v3/coins/${tokenId}/history?date=${date}`, {
            method: 'GET',
            headers: {
                "Content-Type": "application/json",
                "x-cg-demo-api-key": CoingeckoConstants.getNextAPIKey()
            },
        })
        if (response.status && response.status == 200) {
            let result = await response.json() as any;
            return result;
        }

        if (response.status && response.status == 429) {
            Logger.error(`Coingecko API rate limit exceeded. Status Code: ${response.status}, Message: ${response.text}`);
            throw new Error(`Rate limit exceeded. Status: ${response.status}, Message: ${response.text}`);
        }

        if (response.status && response.status >= 500) {
            Logger.error(`Server error encountered. Status Code: ${response.status}, Message: ${response.text}`);
            throw new Error(`Server error. Status: ${response.status}, Message: ${response.text}`);
        }
        return null;
    }

    public async getTopCoinsData(pageNumber: number) {
        let tokens: { id: string, symbol: string, name: string }[] = [];
        let queryObject: Record<string, any> = {
            "vs_currency": CoingeckoConstants.vsCurrency,
            "per_page": CoingeckoConstants.perPage,
            "page": pageNumber,
            "order": "market_cap_desc",
        }
        const response = await fetch('https://api.coingecko.com/api/v3/coins/markets?' + new URLSearchParams(queryObject), {
            method: 'GET',
            headers: {
                "Content-Type": "application/json",
                "x-cg-demo-api-key": CoingeckoConstants.getNextAPIKey()
            },
        })
        let result = await response.json() as any;
        if (result.status?.error_code == 429) {
            throw new Error(`Coingecko API rate limit exceeded'`);
        }
        result.map((tokenData: CoingeckoGetTokenDetailsResponse) => {
            const tokenId = tokenData.id;
            tokens.push({ id: tokenData.id, symbol: tokenData.symbol, name: tokenData.name });
        });
        return tokens;
    }

    public async getHistoricalData(tokenId: string, from: number, to: number) {
        let queryObject: Record<string, any> = {
            "vs_currency": CoingeckoConstants.vsCurrency,
            "from": from,
            "to": to
        }
        const response = await fetch(`https://api.coingecko.com/api/v3/coins/${tokenId}/market_chart/range?` + new URLSearchParams(queryObject), {
            method: 'GET',
            headers: {
                "Content-Type": "application/json",
                "x-cg-demo-api-key": CoingeckoConstants.getNextAPIKey()
            },
        });
        const result = await response.json() as any;
        Logger.debug(`Wrapper::getHistoricalData::result: ${JSON.stringify(result)}`);
        if (result.status?.error_code == 429) {
            throw new Error(`Coingecko API rate limit exceeded'`);
        }
        return result;
    }

    public async getCoinData(tokenId: string): Promise<any> {
        const freeApiUrl = `${CoingeckoConstants.coinGeckoBaseURL}/api/v3/coins/${tokenId}`;
        const apiKey = CoingeckoConstants.getNextAPIKey();
        try {
            Logger.info(`Wrapper::getCoinData::Fetching token details for tokenId ${tokenId} with key '${apiKey}'`);
            const freeResponse = await fetch(freeApiUrl, {
                method: 'GET',
                headers: {
                    "accept": "application/json",
                    "x-cg-demo-api-key": apiKey
                }
            });

            if (freeResponse.status === 429) {
                Logger.warn(`Wrapper::getCoinData::Free API rate limit exceeded (429). Falling back to Pro API.`);
            } else {
                Logger.info(`Wrapper::getCoinData::Data fetched using Free API`);
                const tokenData = await freeResponse.json() as any;
                const token: { id: string, symbol: string, name: string, platforms: any } = { id: tokenId, symbol: tokenData?.symbol, name: tokenData?.name, platforms: tokenData?.platforms };
                return token;
            }
        } catch (error) {
            Logger.warn(`Wrapper::getCoinData::Free API error: ${(error as Error).message}. Falling back to Pro API.`);
        }

        // Fallback to Pro API
        const proApiUrl = CoingeckoConstants.tokenProCurrentData(tokenId);
        const proResponse = await fetch(proApiUrl, {
            method: 'GET',
            headers: {
                "Content-Type": "application/json",
                "x-cg-pro-api-key": CoingeckoConstants.proApiKeys
            }
        });

        if (proResponse.status === 429) {
            Logger.error(`Wrapper::getCoinData::Pro API rate limit exceeded (429).`);
            throw new Error(`Wrapper::getCoinData::Pro API rate limit exceeded for tokenId ${tokenId}`);
        }

        Logger.info(`Wrapper::getCoinData::Data fetched using Pro API`);
        const tokenData = await proResponse.json() as any;
        const token: { id: string, symbol: string, name: string, platforms: any } = { id: tokenId, symbol: tokenData?.symbol, name: tokenData?.name, platforms: tokenData?.platforms };
        return token;
    }

    public async getMarketDataByTokenIds(tokenIds: string[]): Promise<any[]> {
        const oThis = this;
        Logger.info(`CoingeckoWrapper::getMarketDataByTokenIds::Fetching token details for tokenIds: ${tokenIds} | TokenIds Length: ${tokenIds.length}`);
        const formattedTokenIds = encodeURIComponent(tokenIds.join(','));
        const freeApiUrl = `${CoingeckoConstants.coinGeckoBaseURL}/api/v3/coins/markets?vs_currency=usd&ids=${formattedTokenIds}&per_page=${CoingeckoConstants.coingeckoBatchSize}`;
        const freeResponse = await oThis.callFreeAPI(freeApiUrl);
        Logger.debug(`CoingeckoWrapper::getMarketDataByTokenIds::freeResponse: ${JSON.stringify(freeResponse)}`);
        if (freeResponse) {
            return freeResponse;
        }

        // Fallback to Pro API
        const proApiUrl = `${CoingeckoConstants.proCoinGeckoBaseURL}/api/v3/coins/markets?vs_currency=usd&ids=${formattedTokenIds}&per_page=${CoingeckoConstants.coingeckoBatchSize}`;
        const proResponse = await oThis.callProAPI(proApiUrl);
        return proResponse;
    }

    private async callFreeAPI(freeApiUrl: string) {
        const apiKey = CoingeckoConstants.getNextAPIKey();
        try {
            Logger.info(`Wrapper::getCoinData::Fetching token details with key '${apiKey}'`);
            const freeResponse = await fetch(freeApiUrl, {
                method: 'GET',
                headers: {
                    "accept": "application/json",
                    "x-cg-demo-api-key": apiKey
                }
            });

            if (freeResponse.status === 429) {
                Logger.warn(`CoingeckoWrapper::getMarketDataByTokenIds::Free API rate limit exceeded (429). Falling back to Pro API.`);
                return null;
            } else {
                Logger.info(`CoingeckoWrapper::getMarketDataByTokenIds::Data fetched using Free API`);
                return await freeResponse.json() as any;
            }
        } catch (error) {
            Logger.warn(`CoingeckoWrapper::getMarketDataByTokenIds::Free API error: ${(error as Error).message}. Falling back to Pro API.`);
            return null;
        }
    }

    private async callProAPI(proApiUrl: string) {
        try {
            const proResponse = await fetch(proApiUrl, {
                method: 'GET',
                headers: {
                    "Content-Type": "application/json",
                    "x-cg-pro-api-key": CoingeckoConstants.proApiKeys
                }
            });

            if (proResponse.status === 429) {
                Logger.error(`CoingeckoWrapper::getMarketDataByTokenIds::Coingecko Pro API rate limit exceeded.`);
                throw new Error(`CoingeckoWrapper::getMarketDataByTokenIds::Coingecko Pro API rate limit exceeded.`);
            }

            Logger.info(`CoingeckoWrapper::getMarketDataByTokenIds::Data fetched using Pro API`);
            return await proResponse.json() as any;
        } catch (error) {
            Logger.warn(`CoingeckoWrapper::getMarketDataByTokenIds::Pro API error: ${(error as Error).message}.`);
            return [];
        }
    }
}
