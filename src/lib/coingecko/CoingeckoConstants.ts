import moment from "moment";

class CoingeckoConstants {

  get allContracts(): string {
    return 'https://api.coingecko.com/api/v3/coins/list?';
  }

  get getCoinsData(): string {
    return 'https://api.coingecko.com/api/v3/coins/markets?';
  }

  get getProCoinsData(): string {
    return 'https://pro-api.coingecko.com/api/v3/coins/markets?';
  }

  get tokenCategories(): string {
    return 'https://api.coingecko.com/api/v3/coins/categories/list';
  }

  public tokenCurrentData(tokenId: string): string {
    return `https://api.coingecko.com/api/v3/coins/${tokenId}`;
  }

  public tokenProCurrentData(tokenId: string): string {
    return `https://pro-api.coingecko.com/api/v3/coins/${tokenId}`;
  }

  get vsCurrency(): string {
    return 'usd';
  }

  get perPage(): number {
    return 250;
  }

  get topTokensLimit(): number {
    return 1000;
  }

  get solanaEcosystemTokensLimit(): number {
    return 1000;
  }

  get solanaMemeTokensLimit(): number {
    return 1000;
  }

  get baseEcosystemTokensLimit(): number {
    return 1000;
  }

  get getTopTokenPages(): number {
    return this.topTokensLimit / this.perPage;
  }

  get getSolanaEcosystemTokenPages(): number {
    return this.solanaEcosystemTokensLimit / this.perPage;
  }

  get getSolanaMemeTokenPages(): number {
    return this.solanaMemeTokensLimit / this.perPage;
  }

  get getBaseEcosystemTokenPages(): number {
    return this.baseEcosystemTokensLimit / this.perPage;
  }

  get fromDayTimestamp(): number { // Public API users are limited to querying historical data within the past 365 days
    const currentDate = moment();
    const targetDate = currentDate.subtract(364, 'days');
    return targetDate.unix(); // Returns timestamp in seconds
  }

  get bulkDaysRange(): number {
    return 90;
  }

  get ethereumPlatform(): string {
    return "ethereum";
  }

  get polygonPlatform(): string {
    return "polygon-pos";
  }

  get xdaiPlatform(): string {
    return "xdai";
  }

  get binanceSmartChainPlatform(): string {
    return "binance-smart-chain";
  }

  get fantomPlatform(): string {
    return "fantom";
  }

  get arbitrumOnePlatform(): string {
    return 'arbitrum-one';
  }

  get moonriverPlatform(): string {
    return 'moonriver';
  }

  get avalanchePlatform(): string {
    return 'avalanche';
  }

  get optimismPlatform(): string {
    return 'optimistic-ethereum';
  }

  get nearPlatform(): string {
    return 'near';
  }

  get moonbeamPlatform(): string {
    return 'moonbeam';
  }

  get celoPlatform(): string {
    return 'celo';
  }

  get solanaPlatform(): string {
    return 'solana';
  }

  get hourlyFrequency(): string {
    return "hourly";
  }

  get dailyFrequency(): string {
    return "daily";
  }

  get schemaName(): string {
    return "price_oracle";
  }

  get historicalTokenPricesTableName(): string {
    return "historical_token_prices";
  }

  get tokenPricesTableName(): string {
    return "token_prices";
  }

  get tokenCategoriesTableName(): string {
    return "token_categories";
  }

  get tokenCategoryMapTableName(): string {
    return "token_category_map";
  }

  get sleepTime(): number {
    return 3000; // milliseconds
  }

  get secondsInADay(): number {
    return 86400; // seconds in a day
  }

  get apiKeys(): string[] {
    return [
      // 'CG-WCkpfx8myA2GDaskrGdRCWWw', // Keshav
      // 'CG-HE31aSPa9xfmEZpBRH5XpkjM', // Mannan
      // 'CG-mQASRBJqdrLytnvpCTJRzpmA', // Choubey
      // 'CG-BSxbdNqJ5QDZuYDN7r6TFjwh', // Ankur
      // 'CG-epSot9t1MZBnCQThqKdiBejt', // Ankur
      // 'CG-w7ZZ1unT3AsMWzzBRvFQSoLK', // Gupta
      // 'CG-YBoVaviKg7PzmR4uBkG1STeV', // Harsh
      // 'CG-KHh3qy2Lk9v1FE46kGcHXcUm', // Hitesh
      // 'CG-yZUTDMccZCeRmHEdCVknNJ6J', // vikash.2488
      'CG-RoofzzMEy9dtwPhfVJE4TJjx', // choubs.dev
      'CG-jeBFmDxYyTzeZBmpQedUA1Cs', // connect.choubey
      'CG-NfG1FQGkLHfzhrqiZkCTGsCe', // harsh.dapplooker
      'CG-K42oZyqnjsNkfdV8kQTAMGq9', // Harsh Rajput
      'CG-J6Rk9dxnwjz1iNu6yBkAK99v', // Ankur dapp
      'CG-B9Fw7qXkjEmW9e1WSXKzmp1b', // Ankur dapp-2
      'CG-M3PJBkEWXkEiVDT92BL5Rw4t', // Ankur dapp-3
      'CG-YNPyCgc3CyYhNXHiojwuZYQn', // Ankur dapp-4
      'CG-wY9XAeMmf6wV8gfDq5GmsxUD', // Keshav
      'CG-mnyzzyCN87sLcGmMCsDJzuDu', // Mannan
      // 'CG-xzue336ssGoCxFAGZW4cUK3M' // Hitesh
    ];
  }

  get specialCaseApiKey(): string {
    return 'CG-R2ASDfwFNZnvkpbNSm78guVd';
  }

  get proApiKeys(): string {
    return 'CG-BmDEC3PWyVW7kWUJHQvcnvAc'; // Indexer key
    // return 'CG-mhgq81q2ZJfsqY3Y6Fpjv5fR'; // Loky key
  }

  private currentAPIIndex: number = 0;

  // round-robin mechanism: function that rotates through the keys in a circular manner
  public getNextAPIKey() { //
    const apiKey = this.apiKeys[this.currentAPIIndex];
    this.currentAPIIndex = (this.currentAPIIndex + 1) % this.apiKeys.length;
    return apiKey;
  }

  get frequencySixHour(): number {
    return 6;
  }

  get scraperAPIKey(): string {
    return '********************************';
  }

  get whitelistedCategories(): { category: string, getPages: () => number }[] {
    return [
      // {
      //   category: 'hedera-ecosystem',
      //   getPages: () => 1
      // },
      {
        category: 'virtuals-protocol-ecosystem',
        getPages: () => 5
      },
      {
        category: 'base-ecosystem',
        getPages: () => this.getBaseEcosystemTokenPages
      },
      {
        category: 'solana-ecosystem',
        getPages: () => this.getSolanaEcosystemTokenPages
      },
      {
        category: 'solana-meme-coins',
        getPages: () => this.getSolanaMemeTokenPages
      }
    ];
  }

  get solanaCategories(): { category: string, getPages: () => number }[] {
    return [
      {
        category: 'solana-ecosystem',
        getPages: () => this.getSolanaEcosystemTokenPages
      },
      {
        category: 'solana-meme-coins',
        getPages: () => this.getSolanaMemeTokenPages
      }
    ];
  }

  get networkBase(): string {
    return 'base';
  }

  get geckoTerminalBatchSize(): number {
    return 30;
  }

  get coingeckoBatchSize(): number {
    return 250;
  }

  get geckoTerminal(): string {
    return "GECKOTERMINAL"
  }

  get coingecko(): string {
    return "COINGECKO"
  }

  get dataSources(): Record<string, number> {
    return {
      [this.geckoTerminal]: 1,
      [this.coingecko]: 2
    }
  }

  get proCoinGeckoBaseURL(): string {
    return "https://pro-api.coingecko.com"
  }

  get coinGeckoBaseURL(): string {
    return "https://api.coingecko.com"
  }

  get tokenBuyerStatus(): Record<string, number> {
    return {
      "HOLD": 0,
      "BOUGHT": 1,
      "SOLD": 2
    }
  }

  public get tokenBuyerStatusInverted(): Record<number, string> {
    return {
      0: "HOLD",
      1: "BOUGHT",
      2: "SOLD"
    }
  }

  get virtualTokenAddress(): string {
    return '******************************************';
  }

  get firstBuyersTableName(): string {
    return 'first_buyers';
  }

  get virtualClickhouseSchemas(): string[] {
    return [
      'virtual_token',
      'virtual_ai_agents',
      'virtual_ai_agents_old',
      'virtual_ai_agents_migrated',
      'virtual_ai_agents_prototype',
      'virtuals_genesis_agents',
      'virtuals_ethereum_agents'
    ];
  }

  get firstBuyersQuerylimit(): number {
    return 100;
  }

  get tokenQueryLimit(): number {
    return 10;
  }

  get sniperTimeLimitInMinutes(): number {
    return 5;
  }

  get sniperThresholdPercentage(): number {
    return 0.9;
  }

  get firstBuyersUpdateQueryLimit(): number {
    return 500;
  }
}

export default new CoingeckoConstants();
