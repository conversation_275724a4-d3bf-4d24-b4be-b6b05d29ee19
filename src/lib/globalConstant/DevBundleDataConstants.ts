import Constant from '../../config/Constant';

class DevBundleDataConstants {

    public get devBundleDataTableName(): string {
        return "dev_bundle_data";
    }

    public get devBundleWalletType(): Record<string, number> {
        return {
            DEV_WALLET: 0,
            SPREAD_WALLET: 1,
            LEVEL_2_SPREAD_WALLET: 2,
        }
    }

    public get batchSize(): number {
        return 50;
    }

    public get resultLimit(): number {
        return 2000;
    }

    public get transactionBatchSize(): number {
        return 2000;
    }

    public get maxOutflowThresholdForDevWallets(): number {
        return 1_000_000;
    }

    public get defaultTotalSupply(): number {
        return 1_000_000_000;
    }

    public get rugStatus(): Record<string, number> {
        return {
            SAFE: 0,
            CAUTION: 1,
            HIGH_RISK: 2,
            RUGGED: 3,
        }
    }

    public get rugStatusThresholdRanges(): Record<string, { min: number, max?: number }> {
        return {
            SAFE: { min: 0, max: 250_000_000 },             // 0-250M: SAFE
            CAUTION: { min: 250_000_001, max: 500_000_000 }, // >250M-500M: CAUTION
            HIGH_RISK: { min: 500_000_001, max: 760_000_000 }, // >500M-760M: HIGH_RISK
            RUGGED: { min: 760_000_001 }  // >760M: RUGGED
        }
    }

    public devWalletBundleDataQuery(page: number, virtual_ai_agents_time: string, virtual_ai_agents_old_time: string, virtual_ai_agents_prototype_time: string, virtuals_genesis_agents_time: string, virtuals_ethereum_agents_time: string): string {
        return `
        WITH cte
        AS (
            SELECT "id" AS "token_address",
                   "dev_wallet",
                   "uuid",
                   "created_at",
                   'virtual_ai_agents' as "schema"
            FROM "virtual_ai_agents"."tokens"
            WHERE "status" = 'GRADUATED' ${virtual_ai_agents_time ? `AND "created_at" >= parseDateTimeBestEffort('${virtual_ai_agents_time}')` : ''}

            UNION ALL

            SELECT "id" AS "token_address",
                   "dev_wallet",
                   "uuid",
                   "created_at",
                   'virtual_ai_agents_old' as "schema"
            FROM "virtual_ai_agents_old"."tokens"
            WHERE "status" = 'GRADUATED' ${virtual_ai_agents_old_time ? `AND "created_at" >= parseDateTimeBestEffort('${virtual_ai_agents_old_time}')` : ''}

            --UNION ALL
            --
            --SELECT "id" AS "token_address",
                   --"dev_wallet",
                   --"uuid",
                   --"created_at",
                   --'virtual_ai_agents_prototype' as "schema"
            --FROM "virtual_ai_agents_prototype"."tokens"
            --WHERE "status" = 'UNDER_GRADUATED' ${virtual_ai_agents_prototype_time ? `AND "created_at" >= parseDateTimeBestEffort('${virtual_ai_agents_prototype_time}')` : ''}

            UNION ALL

            SELECT "id" AS "token_address",
                   "dev_wallet",
                   "uuid",
                   "created_at",
                   'virtuals_genesis_agents' as "schema"
            FROM "virtuals_genesis_agents"."tokens" FINAL
            ${virtuals_genesis_agents_time ? `WHERE "created_at" >= parseDateTimeBestEffort('${virtuals_genesis_agents_time}')` : ''}

            UNION ALL

            SELECT "id" AS "token_address",
                   "dev_wallet",
                   "uuid",
                   "created_at",
                   'virtuals_ethereum_agents' as "schema"
            FROM "virtuals_ethereum_agents"."tokens" FINAL
            ${virtuals_ethereum_agents_time ? `WHERE "created_at" >= parseDateTimeBestEffort('${virtuals_ethereum_agents_time}')` : ''}
        )
        SELECT *
        FROM cte
        ORDER BY "token_address", "dev_wallet", "uuid", "schema"
        LIMIT ${this.resultLimit} OFFSET ${page * this.resultLimit}
        `;
    }

    public spreadWalletBundleDataQuery(page: number, virtual_ai_agents_time: string, virtual_ai_agents_old_time: string, virtual_ai_agents_prototype_time: string, virtual_ai_agents_migrated_time: string, virtuals_genesis_agents_time: string, virtuals_ethereum_agents_time: string): string {
        return `
        WITH cte AS (
            SELECT DISTINCT t."to" AS "spread_wallet_address",
                t."amount" / power(10, 18) AS "amount",
                d."dev_wallet" AS "dev_wallet",
                t."token_address" AS "token_address",
                t."time_stamp" AS "created_at",
                'virtual_ai_agents' AS "schema"
            FROM "virtual_ai_agents"."transfers" t
            INNER JOIN "virtual_ai_agents"."tokens" d ON t."token_address" = d."id"
                AND t."from" = d."dev_wallet"
            INNER JOIN "virtual_ai_agents"."token_holders" va ON t."to" = va."user_address"
            WHERE va."is_wallet_address" = TRUE
            AND t."to" NOT IN ('******************************************', '******************************************')
            AND ("time_stamp" >= parseDateTimeBestEffort('${virtual_ai_agents_time || '1970-01-01T00:00:00Z'}')
                AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute))))

            UNION ALL

            SELECT DISTINCT t."to" AS "spread_wallet_address",
                t."amount" / power(10, 18) AS "amount",
                d."dev_wallet" AS "dev_wallet",
                t."token_address" AS "token_address",
                t."time_stamp" AS "created_at",
                'virtual_ai_agents_old' AS "schema"
            FROM "virtual_ai_agents_old"."transfers" t
            INNER JOIN "virtual_ai_agents_old"."tokens" d ON t."token_address" = d."id"
                AND t."from" = d."dev_wallet"
            INNER JOIN "virtual_ai_agents_old"."token_holders" va ON t."to" = va."user_address"
            WHERE va."is_wallet_address" = TRUE
            AND t."to" NOT IN ('******************************************', '******************************************')
            AND ("time_stamp" >= parseDateTimeBestEffort('${virtual_ai_agents_old_time || '1970-01-01T00:00:00Z'}')
                AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute))))

            -- UNION ALL
            --
            -- SELECT DISTINCT t."to" AS "spread_wallet_address",
            --     t."amount" / power(10, 18) AS "amount",
            --     d."dev_wallet" AS "dev_wallet",
            --     t."token_address" AS "token_address",
            --     t."time_stamp" AS "created_at",
            --     'virtual_ai_agents_prototype' AS "schema"
            -- FROM "virtual_ai_agents_prototype"."transfers" t
            -- INNER JOIN "virtual_ai_agents_prototype"."tokens" d ON t."token_address" = d."id"
            --     AND t."from" = d."dev_wallet"
            -- INNER JOIN "virtual_ai_agents_prototype"."token_holders" va ON t."to" = va."user_address"
            -- WHERE va."is_wallet_address" = TRUE
            -- AND t."to" NOT IN ('******************************************', '******************************************')
            -- AND ("time_stamp" >= parseDateTimeBestEffort('${virtual_ai_agents_prototype_time || '1970-01-01T00:00:00Z'}')
            --     AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute))))

            UNION ALL

            SELECT DISTINCT t."to" AS "spread_wallet_address",
                t."amount" / power(10, 18) AS "amount",
                d."dev_wallet" AS "dev_wallet",
                t."token_address" AS "token_address",
                t."time_stamp" AS "created_at",
                'virtuals_genesis_agents' AS "schema"
            FROM "virtuals_genesis_agents"."transfers" t
            INNER JOIN "virtuals_genesis_agents"."tokens" d FINAL ON t."token_address" = d."id"
                AND t."from" = d."dev_wallet"
            INNER JOIN "virtuals_genesis_agents"."token_holders" va FINAL ON t."to" = va."user_address"
            WHERE va."is_wallet_address" = TRUE
            AND t."to" NOT IN ('******************************************', '******************************************')
            AND ("time_stamp" >= parseDateTimeBestEffort('${virtuals_genesis_agents_time || '1970-01-01T00:00:00Z'}')
                AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute))))

            UNION ALL

            SELECT DISTINCT t."to" AS "spread_wallet_address",
                t."amount" / power(10, 18) AS "amount",
                d."dev_wallet" AS "dev_wallet",
                t."token_address" AS "token_address",
                t."time_stamp" AS "created_at",
                'virtuals_ethereum_agents' AS "schema"
            FROM "virtuals_ethereum_agents"."transfers" t
            INNER JOIN "virtuals_ethereum_agents"."tokens" d FINAL ON t."token_address" = d."id"
                AND t."from" = d."dev_wallet"
            INNER JOIN "virtuals_ethereum_agents"."token_holders" va FINAL ON t."to" = va."user_address"
            WHERE va."is_wallet_address" = TRUE
            AND t."to" NOT IN ('******************************************', '******************************************')
            AND ("time_stamp" >= parseDateTimeBestEffort('${virtuals_ethereum_agents_time || '1970-01-01T00:00:00Z'}')
                AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute))))

            UNION ALL

            (WITH tokens
            AS ( 
            SELECT '******************************************' AS tokenAddress, '******************************************' AS dev_wallet
            UNION ALL
            SELECT '******************************************', '******************************************'
            UNION ALL
            SELECT '******************************************', '******************************************'
            UNION ALL
            SELECT '******************************************', '******************************************'
            UNION ALL
            SELECT '******************************************', '******************************************'
            UNION ALL
            SELECT '******************************************', '******************************************'
            UNION ALL
            SELECT '******************************************', '******************************************'
            UNION ALL
            SELECT '******************************************', '******************************************'
            UNION ALL
            SELECT '0xca4c2e10037ac1af9f501ecb11a710776c87d2d5', '******************************************'
            UNION ALL
            SELECT '******************************************', '******************************************'
            UNION ALL
            SELECT '******************************************', '******************************************'
            UNION ALL
            SELECT '******************************************', '******************************************'
            )
            SELECT DISTINCT t."to" AS "spread_wallet_address",
                t."amount" / power(10, 18) AS "amount",
                d.dev_wallet AS "dev_wallet",
                t."token_address" AS "token_address",
                t."time_stamp" AS "created_at",
                'virtual_ai_agents_migrated' AS "schema"
            FROM "virtual_ai_agents_migrated"."transfers" t
            INNER JOIN tokens d ON t."token_address" = d.tokenAddress
                AND t."from" = d.dev_wallet
            INNER JOIN "virtual_ai_agents_migrated"."token_holders" va ON t."to" = va."user_address"
            WHERE va."is_wallet_address" = TRUE
            AND t."to" NOT IN ('******************************************', '******************************************')
            AND ("time_stamp" >= parseDateTimeBestEffort('${virtual_ai_agents_migrated_time || '1970-01-01T00:00:00.000Z'}')
                AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute))))
                )
        )
        SELECT DISTINCT spread_wallet_address, amount, dev_wallet, token_address, created_at, schema
        FROM cte
        ORDER BY "token_address", "dev_wallet", "spread_wallet_address"
        LIMIT ${this.resultLimit} OFFSET ${page * this.resultLimit}
        `;
    }

    public walletTransactionsQuery(
        walletAddresses: string[],
        lastProcessedIds: Record<number, number>,
        limit: number = this.transactionBatchSize,
        offset: number = 0
    ): string {
        const walletAddressesString = walletAddresses.map((address) => `'${address.toLowerCase()}'`).join(',');

        const virtualAiAgentsUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS]}`
            : `1=1`;

        const virtualAiAgentsOldUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD]}`
            : `1=1`;

        const virtualAiAgentsPrototypeUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE]}`
            : `1=1`;

        const virtualAiAgentsMigratedUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED]}`
            : `1=1`;

        const virtualsGenesisAgentsUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS]}`
            : `1=1`;

        const virtualsEthereumAgentsUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS]}`
            : `1=1`;

        return `
        WITH cte AS (
            SELECT "from" AS "wallet_address",
                "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS} as "schema"
            FROM "virtual_ai_agents"."transfers" t
            WHERE ${virtualAiAgentsUuidCondition}
            AND t."from" IN (${walletAddressesString})

            UNION ALL

            SELECT "from" AS "wallet_address",
                "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD} as "schema"
            FROM "virtual_ai_agents_old"."transfers" t
            WHERE ${virtualAiAgentsOldUuidCondition}
            AND t."from" IN (${walletAddressesString})

            --UNION ALL
            --
            --SELECT "from" AS "wallet_address",
                --"to",
                --"token_address",
                --"amount" / power(10, 18) AS "amount",
                --"id",
                --"uuid",
                --${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE} as "schema"
            --FROM "virtual_ai_agents_prototype"."transfers" t
            --WHERE ${virtualAiAgentsPrototypeUuidCondition}
            --AND t."from" IN (${walletAddressesString})

            UNION ALL

            SELECT "from" AS "wallet_address",
                "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED} as "schema"
            FROM "virtual_ai_agents_migrated"."transfers" t
            WHERE ${virtualAiAgentsMigratedUuidCondition}
            AND t."from" IN (${walletAddressesString})

            UNION ALL

            SELECT "from" AS "wallet_address",
                "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS} as "schema"
            FROM "virtuals_genesis_agents"."transfers" t
            WHERE ${virtualsGenesisAgentsUuidCondition}
            AND t."from" IN (${walletAddressesString})

            UNION ALL

            SELECT "from" AS "wallet_address",
                "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS} as "schema"
            FROM "virtuals_ethereum_agents"."transfers" t
            WHERE ${virtualsEthereumAgentsUuidCondition}
            AND t."from" IN (${walletAddressesString})
        ),
        token_holder_data AS (
            SELECT DISTINCT "user_address",
                "is_wallet_address"
            FROM (
                SELECT "user_address",
                    "is_wallet_address"
                FROM "virtual_ai_agents"."token_holders"

                UNION ALL

                SELECT "user_address",
                    "is_wallet_address"
                FROM "virtual_ai_agents_old"."token_holders"

                --UNION ALL
                --
                --SELECT "user_address",
                --    "is_wallet_address"
                --FROM "virtual_ai_agents_prototype"."token_holders"

                UNION ALL

                SELECT "user_address",
                    "is_wallet_address"
                FROM "virtual_ai_agents_migrated"."token_holders"

                UNION ALL

                SELECT "user_address",
                    "is_wallet_address"
                FROM "virtuals_genesis_agents"."token_holders" FINAL

                UNION ALL

                SELECT "user_address",
                    "is_wallet_address"
                FROM "virtuals_ethereum_agents"."token_holders" FINAL
            ) a
        )
        SELECT DISTINCT c."wallet_address",
                        c."to",
                        c."token_address",
                        c."amount",
                        t."is_wallet_address",
                        c."id",
                        c."uuid",
                        c."schema"
        FROM cte c
        LEFT JOIN token_holder_data t ON c."to" = t."user_address"
        ORDER BY c."uuid", c."schema"
        LIMIT ${limit}
        OFFSET ${offset}
        `;
    }

    public walletBalancesQuery(walletAddresses: string[], virtual_ai_agents_time: string, virtual_ai_agents_old_time: string, virtual_ai_agents_prototype_time: string, virtual_ai_agents_migrated_time: string, virtuals_genesis_agents_time: string, virtuals_ethereum_agents_time: string): string {
        const walletAddressesString = walletAddresses.map((address) => `'${address.toLowerCase()}'`).join(',');
        return `
        WITH cte
        AS (
            SELECT "user_address"
                ,"token_address"
                ,"amount" / power(10, 18) AS "amount"
            FROM "virtual_ai_agents"."token_holders" FINAL
            WHERE "user_address" NOT IN (
                    '******************************************'
                    ,'******************************************'
                    )
                AND "amount" > 0
                AND "last_updated_at" >= parseDateTimeBestEffort('${virtual_ai_agents_time || '1970-01-01T00:00:00Z'}')
                AND "last_updated_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))

            UNION ALL

            SELECT "user_address"
                ,"token_address"
                ,"amount" / power(10, 18) AS "amount"
            FROM "virtual_ai_agents_old"."token_holders" FINAL
            WHERE "user_address" NOT IN (
                    '******************************************'
                    ,'******************************************'
                    )
                AND "amount" > 0
                AND "last_updated_at" >= parseDateTimeBestEffort('${virtual_ai_agents_old_time || '1970-01-01T00:00:00Z'}')
                AND "last_updated_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))

            --UNION ALL
            --
            --SELECT "user_address"
            --    ,"token_address"
            --    ,"amount" / power(10, 18) AS "amount"
            --FROM "virtual_ai_agents_prototype"."token_holders" FINAL
            --WHERE "user_address" NOT IN (
            --        '******************************************'
            --        ,'******************************************'
            --        )
            --    AND "amount" > 0
            --    AND "last_updated_at" >= parseDateTimeBestEffort('${virtual_ai_agents_prototype_time || '1970-01-01T00:00:00Z'}')
            --    AND "last_updated_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))

            UNION ALL

            SELECT "user_address"
                ,"token_address"
                ,"amount" / power(10, 18) AS "amount"
            FROM "virtual_ai_agents_migrated"."token_holders" FINAL
            WHERE "user_address" NOT IN (
                    '******************************************'
                    ,'******************************************'
                    )
                AND "amount" > 0
                AND "last_updated_at" >= parseDateTimeBestEffort('${virtual_ai_agents_migrated_time || '1970-01-01T00:00:00Z'}')
                AND "last_updated_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))

            UNION ALL

            SELECT "user_address"
                ,"token_address"
                ,"amount" / power(10, 18) AS "amount"
            FROM "virtuals_genesis_agents"."token_holders" FINAL
            WHERE "user_address" NOT IN (
                    '******************************************'
                    ,'******************************************'
                    )
                AND "amount" > 0
                AND "last_updated_at" >= parseDateTimeBestEffort('${virtuals_genesis_agents_time || '1970-01-01T00:00:00Z'}')
                AND "last_updated_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))

            UNION ALL

            SELECT "user_address"
                ,"token_address"
                ,"amount" / power(10, 18) AS "amount"
            FROM "virtuals_ethereum_agents"."token_holders" FINAL
            WHERE "user_address" NOT IN (
                    '******************************************'
                    ,'******************************************'
                    )
                AND "amount" > 0
                AND "last_updated_at" >= parseDateTimeBestEffort('${virtuals_ethereum_agents_time || '1970-01-01T00:00:00Z'}')
                AND "last_updated_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
        )
        SELECT *
        FROM cte
        WHERE "user_address" IN (${walletAddressesString})
        ORDER BY "token_address", "user_address"
        `;
    }

    public incomingTransactionsQuery(
        walletAddresses: string[],
        lastProcessedIds: Record<number, number>,
        limit: number = this.transactionBatchSize,
        offset: number = 0
    ): string {
        const walletAddressesString = walletAddresses.map((address) => `'${address.toLowerCase()}'`).join(',');

        const virtualAiAgentsUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS]}`
            : `1=1`;

        const virtualAiAgentsOldUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD]}`
            : `1=1`;

        const virtualAiAgentsPrototypeUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE]}`
            : `1=1`;

        const virtualAiAgentsMigratedUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED]}`
            : `1=1`;

        const virtualsGenesisAgentsUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS]}`
            : `1=1`;

        const virtualsEthereumAgentsUuidCondition = lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS]
            ? `t."uuid" > ${lastProcessedIds[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS]}`
            : `1=1`;

        return `
        WITH cte AS (
            SELECT "to" AS "wallet_address",
                "from",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS} as "schema"
            FROM "virtual_ai_agents"."transfers" t
            WHERE ${virtualAiAgentsUuidCondition}
            AND t."to" IN (${walletAddressesString})

            UNION ALL

            SELECT "to" AS "wallet_address",
                "from",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD} as "schema"
            FROM "virtual_ai_agents_old"."transfers" t
            WHERE ${virtualAiAgentsOldUuidCondition}
            AND t."to" IN (${walletAddressesString})

            -- UNION ALL
            -- 
            -- SELECT "to" AS "wallet_address",
            --     "from",
            --     "token_address",
            --     "amount" / power(10, 18) AS "amount",
            --     "id",
            --     "uuid",
            --     ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE} as "schema"
            -- FROM "virtual_ai_agents_prototype"."transfers" t
            -- WHERE ${virtualAiAgentsPrototypeUuidCondition}
            -- AND t."to" IN (${walletAddressesString})

            UNION ALL

            SELECT "to" AS "wallet_address",
                "from",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED} as "schema"
            FROM "virtual_ai_agents_migrated"."transfers" t
            WHERE ${virtualAiAgentsMigratedUuidCondition}
            AND t."to" IN (${walletAddressesString})

            UNION ALL

            SELECT "to" AS "wallet_address",
                "from",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS} as "schema"
            FROM "virtuals_genesis_agents"."transfers" t
            WHERE ${virtualsGenesisAgentsUuidCondition}
            AND t."to" IN (${walletAddressesString})

            UNION ALL

            SELECT "to" AS "wallet_address",
                "from",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS} as "schema"
            FROM "virtuals_ethereum_agents"."transfers" t
            WHERE ${virtualsEthereumAgentsUuidCondition}
            AND t."to" IN (${walletAddressesString})
        ),
        token_holder_data AS (
            SELECT DISTINCT "user_address",
                "is_wallet_address"
            FROM (
                SELECT "user_address",
                    "is_wallet_address"
                FROM "virtual_ai_agents"."token_holders"

                UNION ALL

                SELECT "user_address",
                    "is_wallet_address"
                FROM "virtual_ai_agents_old"."token_holders"

                -- UNION ALL
                -- 
                -- SELECT "user_address",
                --     "is_wallet_address"
                -- FROM "virtual_ai_agents_prototype"."token_holders"
                
                UNION ALL
                
                SELECT "user_address",
                    "is_wallet_address"
                FROM "virtual_ai_agents_migrated"."token_holders"

                UNION ALL

                SELECT "user_address",
                    "is_wallet_address"
                FROM "virtuals_genesis_agents"."token_holders" FINAL

                UNION ALL

                SELECT "user_address",
                    "is_wallet_address"
                FROM "virtuals_ethereum_agents"."token_holders" FINAL
            ) a
        )
        SELECT DISTINCT c."wallet_address",
                        c."from",
                        c."token_address",
                        c."amount",
                        t."is_wallet_address",
                        c."id",
                        c."uuid",
                        c."schema"
        FROM cte c
        LEFT JOIN token_holder_data t ON c."from" = t."user_address"
        ORDER BY c."uuid", c."schema"
        LIMIT ${limit}
        OFFSET ${offset}
        `;
    }

    public get migratedTokenAndDevAddresses(): Record<string, string>[] {
        return [
            {
                tokenAddress: '******************************************',
                devAddress: '******************************************'
            },
            {
                tokenAddress: '******************************************',
                devAddress: '******************************************'
            },
            {
                tokenAddress: '******************************************',
                devAddress: '******************************************'
            },
            {
                tokenAddress: '******************************************',
                devAddress: '******************************************'
            },
            {
                tokenAddress: '******************************************',
                devAddress: '******************************************'
            },
            {
                tokenAddress: '******************************************',
                devAddress: '******************************************'
            },
            {
                tokenAddress: '******************************************',
                devAddress: '******************************************'
            },
            {
                tokenAddress: '******************************************',
                devAddress: '******************************************'
            },
            {
                tokenAddress: '0xca4c2e10037ac1af9f501ecb11a710776c87d2d5',
                devAddress: '******************************************'
            },
            {
                tokenAddress: '******************************************',
                devAddress: '******************************************'
            },
            {
                tokenAddress: '******************************************',
                devAddress: '******************************************'
            },
            {
                tokenAddress: '******************************************',
                devAddress: '******************************************'
            }
        ]
    }

    public get devStakingAndVestingContracts(): string[] {
        return [
            "0xc1c548f980669615772dadcbfebc29937c29481a",
            "0xf4937657ed8b3f3cb379eed47b8818ee947beb1e", // SablierV2 : Sablier V2 Lockup Tranched NFT
            "0x2cde9919e81b20b4b33dd562a48a84b54c48f00c", // Hedgey Finance Vesting
            "0xa82685520c463a752d5319e6616e4e5fd0215e33", // UNCX Network: Token Vesting
            "0x231278edd38b00b07fbd52120cef685b9baebcc1", // UNCX Lockers: UniSwap V3 LP
            "0xeab6971415f4b0bd0a14880bc7ba5a984e299269", // StakingEye
            "0x1d6bb701eececd53966402064ce1c5b9eddc780", // VaderAIStaking
            "******************************************", // Staking Rewards
            "******************************************", // SablierLockup
            "******************************************", // Vesting
            "******************************************"    // Vesting
        ]
    }

    public devStakingAndVestingOnetimerQuery(
        tokenAddress: string,
        walletAddresses: string[],
        limit: number = this.transactionBatchSize,
        offset: number = 0,
        lastProcessedDevBundleOutflow: Record<number, number>
    ): string {
        const walletAddressesString = walletAddresses.map((address) => `'${address.toLowerCase()}'`).join(',');

        const devStakingAndVestingContractsString = this.devStakingAndVestingContracts.map((contract) => `'${contract}'`).join(',');

        const virtualAiAgentsUuidCondition = lastProcessedDevBundleOutflow[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS]
            ? `t."uuid" <= ${lastProcessedDevBundleOutflow[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS]}`
            : `1=1`;

        const virtualAiAgentsOldUuidCondition = lastProcessedDevBundleOutflow[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD]
            ? `t."uuid" <= ${lastProcessedDevBundleOutflow[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD]}`
            : `1=1`;

        const virtualAiAgentsMigratedUuidCondition = lastProcessedDevBundleOutflow[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED]
            ? `t."uuid" <= ${lastProcessedDevBundleOutflow[Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED]}`
            : `1=1`;

        const virtualsGenesisAgentsUuidCondition = lastProcessedDevBundleOutflow[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS]
            ? `t."uuid" <= ${lastProcessedDevBundleOutflow[Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS]}`
            : `1=1`;

        const virtualsEthereumAgentsUuidCondition = lastProcessedDevBundleOutflow[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS]
            ? `t."uuid" <= ${lastProcessedDevBundleOutflow[Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS]}`
            : `1=1`;

        return `
        WITH cte AS (
            SELECT "from" AS "wallet_address",
                lower("to") AS "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS} as "schema"
            FROM "virtual_ai_agents"."transfers" t
            WHERE t."to" IN (${devStakingAndVestingContractsString})
            AND t."from" IN (${walletAddressesString})
            AND t."token_address" = '${tokenAddress}'
            AND ${virtualAiAgentsUuidCondition}

            UNION ALL

            SELECT "from" AS "wallet_address",
                lower("to") AS "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD} as "schema"
            FROM "virtual_ai_agents_old"."transfers" t
            WHERE t."to" IN (${devStakingAndVestingContractsString})
            AND t."from" IN (${walletAddressesString})
            AND t."token_address" = '${tokenAddress}'
            AND ${virtualAiAgentsOldUuidCondition}

            UNION ALL

            SELECT "from" AS "wallet_address",
                lower("to") AS "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED} as "schema"
            FROM "virtual_ai_agents_migrated"."transfers" t
            WHERE t."to" IN (${devStakingAndVestingContractsString})
            AND t."from" IN (${walletAddressesString})
            AND t."token_address" = '${tokenAddress}'
            AND ${virtualAiAgentsMigratedUuidCondition}

            UNION ALL

            SELECT "from" AS "wallet_address",
                lower("to") AS "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS} as "schema"
            FROM "virtuals_genesis_agents"."transfers" t
            WHERE t."to" IN (${devStakingAndVestingContractsString})
            AND t."from" IN (${walletAddressesString})
            AND t."token_address" = '${tokenAddress}'
            AND ${virtualsGenesisAgentsUuidCondition}

            UNION ALL

            SELECT "from" AS "wallet_address",
                lower("to") AS "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "uuid",
                ${Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS} as "schema"
            FROM "virtuals_ethereum_agents"."transfers" t
            WHERE t."to" IN (${devStakingAndVestingContractsString})
            AND t."from" IN (${walletAddressesString})
            AND t."token_address" = '${tokenAddress}'
            AND ${virtualsEthereumAgentsUuidCondition}
        )
        SELECT DISTINCT c."wallet_address",
                        c."to",
                        c."token_address",
                        c."amount",
                        c."id",
                        c."uuid",
                        c."schema"
        FROM cte c
        ORDER BY c."uuid", c."schema"
        LIMIT ${limit}
        OFFSET ${offset}
        `;
    }
}

export default new DevBundleDataConstants();
