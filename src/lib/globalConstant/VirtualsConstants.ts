import Utils from "../Utils";

class VirtualsConstants {

    public get status(): Record<string, number> {
        return {
            PROTOTYPE: 1,
            SENTIENT: 2,
            GENESIS: 3
        }
    }

    public get chain(): Record<string, number> {
        return {
            BASE: 1,
            SOLANA: 2,
            ETHEREUM: 3
        }
    }

    public get chainNameMap(): Record<number, string> {
        return {
            [this.chain.BASE]: 'base',
            [this.chain.ETHEREUM]: 'ethereum'
        }
    }

    private statusType: Record<number, string>;
    public get invertedStatus(): Record<number, string> {
        const oThis = this;
        oThis.statusType = oThis.statusType || Utils.invert(oThis.status);
        return oThis.statusType;
    }

    public get tokenStatusMap(): Record<string, string> {
        return {
            [this.status.PROTOTYPE]: this.underGraduated,
            [this.status.SENTIENT]: this.graduated,
        }
    }

    public get subgraphMap(): Record<number, Record<string, string>> {
        return {
            [this.status.PROTOTYPE]: {
                base: this.prototypeBaseTokenSubgraph
            },
            [this.status.SENTIENT]: {
                base: this.sentientBaseTokenSubgraph
            },
            [this.status.GENESIS]: {
                base: this.genesisBaseTokenSubgraph,
                ethereum: this.genesisEthereumTokenSubgraph
            }
        }
    }

    public get dateColumnMap(): Record<number, string> {
        return {
            [this.status.PROTOTYPE]: 'createdAt',
            [this.status.SENTIENT]: 'updatedAt',
            [this.status.GENESIS]: 'updatedAt'
        }
    }

    public get underGraduated(): string {
        return 'UNDER_GRADUATED';
    }

    public get graduated(): string {
        return 'GRADUATED';
    }

    public prototypeTokenAPI(preTokenAddress: string): string {
        return `https://api.virtuals.io/api/virtuals?filters[preToken]=${preTokenAddress}`;
    }

    public sentientTokenAPI(tokenAddress: string): string {
        return `https://api.virtuals.io/api/virtuals?filters[tokenAddress]=${tokenAddress}`;
    }

    public geckoTerminalAPI(network: string, tokenAddresses: string) {
        return `https://api.geckoterminal.com/api/v2/networks/${network}/tokens/multi/${tokenAddresses}`;
    }

    get prototypeBaseTokenSubgraph(): string {
        return 'https://graph-api.dapplooker.com/subgraphs/name/virtuals-agent-prototype-v3';
    }

    get sentientBaseTokenSubgraph(): string {
        return 'https://graph-api.dapplooker.com/subgraphs/name/virtuals-agent-after-nov24-v4';
    }

    get genesisBaseTokenSubgraph(): string {
        return 'https://graph-api.dapplooker.com/subgraphs/name/genesis-agent-v4';
    }

    get genesisEthereumTokenSubgraph(): string {
        return 'https://graph-api.dapplooker.com/subgraphs/name/ethereum-virtual-agents';
    }

    get virtualsCategoryId(): string {
        return 'virtuals-protocol-ecosystem';
    }
}

export default new VirtualsConstants();
