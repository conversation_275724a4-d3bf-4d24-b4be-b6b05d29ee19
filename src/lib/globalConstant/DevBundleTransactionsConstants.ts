import Constant from "../../config/Constant";

class DevBundleTransactionsConstants {
    public get devBundleTransactionsTableName(): string {
        return "dev_bundle_transactions";
    }

    public get transactionType(): Record<string, number> {
        return {
            IN: 0,
            OUT: 1
        };
    }

    public get tokenBatchSize(): number {
        return 10;
    }

    public get resultLimit(): number {
        return 2000;
    }

    public get hoursToSubtract(): number {
        return 1;
    }

    private subtractTimeFromTimestamp(timestamp: string): string {
        const date = new Date(timestamp);
        date.setHours(date.getHours() - this.hoursToSubtract);
        return date.toISOString();
    }

    public devBundleTransactionsQuery(
        page: number,
        tokenAddress: string,
        walletAddresses: string[],
        virtual_ai_agentsTimeStamp: string,
        virtual_ai_agents_oldTimeStamp: string,
        virtual_ai_agents_migratedTimeStamp: string,
        virtual_ai_agents_prototypeTimeStamp: string,
        virtual_tokenTimeStamp: string,
        virtuals_genesis_agentsTimeStamp: string,
        virtuals_ethereum_agentsTimeStamp: string
    ): string {
        const virtualAiAgentsTimestamp =
            virtual_ai_agentsTimeStamp > new Date(0).toISOString()
                ? this.subtractTimeFromTimestamp(virtual_ai_agentsTimeStamp)
                : virtual_ai_agentsTimeStamp;

        const virtualAiAgentsOldTimestamp =
            virtual_ai_agents_oldTimeStamp > new Date(0).toISOString()
                ? this.subtractTimeFromTimestamp(virtual_ai_agents_oldTimeStamp)
                : virtual_ai_agents_oldTimeStamp;

        const virtualAiAgentsMigratedTimestamp =
            virtual_ai_agents_migratedTimeStamp > new Date(0).toISOString()
                ? this.subtractTimeFromTimestamp(virtual_ai_agents_migratedTimeStamp)
                : virtual_ai_agents_migratedTimeStamp;

        const virtualAiAgentsPrototypeTimestamp =
            virtual_ai_agents_prototypeTimeStamp > new Date(0).toISOString()
                ? this.subtractTimeFromTimestamp(virtual_ai_agents_prototypeTimeStamp)
                : virtual_ai_agents_prototypeTimeStamp;

        const virtualsGenesisAgentsTimestamp =
            virtuals_genesis_agentsTimeStamp > new Date(0).toISOString()
                ? this.subtractTimeFromTimestamp(virtuals_genesis_agentsTimeStamp)
                : virtuals_genesis_agentsTimeStamp;

        const virtualsEthereumAgentsTimestamp =
            virtuals_ethereum_agentsTimeStamp > new Date(0).toISOString()
                ? this.subtractTimeFromTimestamp(virtuals_ethereum_agentsTimeStamp)
                : virtuals_ethereum_agentsTimeStamp;

        const virtualTokenTimestamp =
            virtual_tokenTimeStamp > new Date(0).toISOString() ? this.subtractTimeFromTimestamp(virtual_tokenTimeStamp) : virtual_tokenTimeStamp;

        return `
        WITH all_data AS (
            SELECT "from",
                "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "tx_hash",
                "time_stamp",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS} as "schema"
            FROM "virtual_ai_agents"."transfers"
            WHERE "token_address" = '${tokenAddress}'
            AND "time_stamp" >= parseDateTimeBestEffort('${virtualAiAgentsTimestamp}')
            AND ("from" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}) OR "to" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}))

            UNION ALL

            SELECT "from",
                "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "tx_hash",
                "time_stamp",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD} as "schema"
            FROM "virtual_ai_agents_old"."transfers"
            WHERE "token_address" = '${tokenAddress}'
            AND "time_stamp" >= parseDateTimeBestEffort('${virtualAiAgentsOldTimestamp}')
            AND ("from" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}) OR "to" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}))

            UNION ALL

            SELECT "from",
                "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "tx_hash",
                "time_stamp",
                ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED} as "schema"
            FROM "virtual_ai_agents_migrated"."transfers"
            WHERE "token_address" = '${tokenAddress}'
            AND "time_stamp" >= parseDateTimeBestEffort('${virtualAiAgentsMigratedTimestamp}')
            AND ("from" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}) OR "to" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}))

            -- UNION ALL
            -- 
            -- SELECT "from",
            --     "to",
            --     "token_address",
            --     "amount" / power(10, 18) AS "amount",
            --     "id",
            --     "tx_hash",
            --     "time_stamp",
            --     ${Constant.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE} as "schema"
            -- FROM "virtual_ai_agents_prototype"."transfers"
            -- WHERE "token_address" = '${tokenAddress}'
            -- AND "time_stamp" >= parseDateTimeBestEffort('${virtualAiAgentsPrototypeTimestamp}')
            -- AND ("from" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}) OR "to" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}))

            UNION ALL

            SELECT "from",
                "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "tx_hash",
                "time_stamp",
                ${Constant.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS} as "schema"
            FROM "virtuals_genesis_agents"."transfers"
            WHERE "token_address" = '${tokenAddress}'
            AND "time_stamp" >= parseDateTimeBestEffort('${virtualsGenesisAgentsTimestamp}')
            AND ("from" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}) OR "to" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}))

            UNION ALL

            SELECT "from",
                "to",
                "token_address",
                "amount" / power(10, 18) AS "amount",
                "id",
                "tx_hash",
                "time_stamp",
                ${Constant.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS} as "schema"
            FROM "virtuals_ethereum_agents"."transfers"
            WHERE "token_address" = '${tokenAddress}'
            AND "time_stamp" >= parseDateTimeBestEffort('${virtualsEthereumAgentsTimestamp}')
            AND ("from" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}) OR "to" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}))

            UNION ALL

            SELECT "from",
                "to",
                '******************************************' AS "token_address",
                "value" / power(10, 18) AS "amount",
                "id",
                "tx_hash",
                "created_at" AS "time_stamp",
                ${Constant.DevBundleSchemaIds.VIRTUAL_TOKEN} as "schema"
            FROM "virtual_token"."transfers"
            WHERE "created_at" >= parseDateTimeBestEffort('${virtualTokenTimestamp}')
            AND ("from" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}) OR "to" IN (${walletAddresses.map((address) => `'${address}'`).join(",")}))
        )
        SELECT *
        FROM all_data
        LIMIT ${this.resultLimit} OFFSET ${page * this.resultLimit}
        `;
    }
}

export default new DevBundleTransactionsConstants();
