import Logger from "../Logger";

class IngestorConstant {

  private keyIndex: number;

  public constructor() {
    this.keyIndex = 0;
  }

  public get batchSize(): number {
    return 1000;
  }

  // 5000 is max iteration
  public get skipLimit(): number {
    return 5000;
  }

  public get skipLoopIteration(): number {
    return this.skipLimit / this.batchSize;
  }

  // max skips * 10 times
  public get highestLoopIteration(): number {
    return this.skipLoopIteration * 100;
  }

  public get joystreamSkipLoopIteration(): number {
    return 20;
  }

  // Created at block number sugraph endpoints
  public get createdAtAsBlockNumberSubgraphEndpoints(): string[] {
    return [
      "https://api.thegraph.com/subgraphs/name/aavegotchi/aavegotchi-core-matic",
    ]
  }

  // Check subgraph created as is block number
  public isCreatedAtAsBlockNumberSubgraphEndpoint(subGraphEndPoint: string) {
    const oThis = this;
    return (oThis.createdAtAsBlockNumberSubgraphEndpoints.includes(subGraphEndPoint));
  }

  // Created at date time sugraph endpoints
  public get createdAtAsDateTimeSubgraphEndpoint(): string[] {
    return [
      "https://graph.mintbase.xyz/mainnet",
      "https://query.joyutils.org/graphql",
      "https://joystreamstats.live/graphql",
      "https://query.joystream.org/graphql",
      "https://hydra.joystream.org/graphql",
    ]
  }

  // Check subgraph created as is date time
  public isCreatedAtAsDateTimeSubgraphEndpoint(subGraphEndPoint: string) {
    const oThis = this;

    return (oThis.createdAtAsDateTimeSubgraphEndpoint.includes(subGraphEndPoint));
  }

  // 20 minutes
  public get ingestorProcessingThresholdInSeconds(): number {
    return 25 * 60;
  }

  public get rawDataFields(): string[] {
    return [
      "raw",
      "raw_data",
      "Raw",
      "RowData"
    ];
  }

  public get addressFields(): string[] {
    return [
      "from",
      "to",
      "address",
      "from_address",
      "to_address",
      "contract_address",
      "wallet_address",
      "user_address",
      "user",
      "destination",
      "sender",
      "receiver"
    ];
  }

  public get timestampColumnsToFormat(): string[] {
    return [
      'createdat',
      'updatedat',
      'blocktime',
      'daystart',
      'dayend',
      'lastdelegatedat',
      'lastundelegatedat',
      'date',
      'firsttimeboughtat',
      'lasttimeboughtat',
      'lasttransactionfrom',
      'lasttransactionto',
      "timestamp",
      "blocktimestamp",
      "mintedattimestamp",
      "inittimestamp",
      "daystarttimestamp",
      "preparedtimestamp",
      "hourstarttimestamp",
      "lastupdatetimestamp",
      "lastupdatedtimestamp",
      "startepoch",
      "endepoch",
      "start_epoch",
      "end_epoch",
      "epochstart",
      "epochend",
      "created_at",
      "removed_at"
    ];
  }

  // NOTE: Only add timestamp value field here, Datetime field like createdAt may produce bugs
  public get orderedTimestampFields(): string[] {
    return [
      "day",
      "date",
      "timestamp",
      "blockTimestamp",
      "blockTime",
      "mintedAtTimestamp",
      "initTimestamp",
      "dayStartTimestamp",
      "preparedTimestamp",
      "hourStartTimestamp",
      "lastUpdateTimestamp",
      "lastUpdatedTimestamp",
      "lastTransactionTo",
      "dayStart",
      "start_epoch",
      "end_epoch",
      "epochStart",
      "addedAt",
      "lastUpdateTime",
      "lastUpdatedAt"
    ];
  }

  public get orderedBlockNumberFields(): string[] {
    return [
      "blockNumber",
      "blockHeight",
      "accrualBlockNumber",
      "block",
      "lastPriceBlockNumber",
      "maturityBlock",
      "dayStartBlockNumber",
      "createdBlock" // Added for Subquery
    ];
  }

  public get dayDataFields(): string[] {
    return [
      "date",
      "day",
      "dayTimestamp",
      "dailyUsageMetrics",
    ];
  }

  // Returns the number of seconds in two days.
  public get twoDaysSeconds(): number {
    return 60 /*seconds/minute*/ * 60 /*minutes/hour*/ * 24 /*hours/day*/ * 2 /*days*/;
  }

  // Length of milliseconds: 13 digit timestamp represent time in milliseconds
  public get msTimestampLength(): number {
    return 13;
  }

  public get secTimestampLength(): number {
    return 10;
  }

  public isDappLookerGraphNode(graphNode: string): boolean {
    return this.graphNodeVsStatusEndpoint[graphNode].isDappLookerGraphNode;
  }

  public extractGraphNodeFromEndpoint(endpoint: string) {
    return endpoint.split("/").slice(0, 3).join("/");
  }

  public extractSubgraphNameFromEndpoint(subgraphEndpoint: string) {
    return subgraphEndpoint.split("/").slice(-2).join("/");
  }

  // It is Mandatory to add DappLooker hosted custom graph node here to get subgraph sync status
  public get graphNodeVsStatusEndpoint(): {} {
    return {
      'https://graph-api.dapplooker.com': { isDappLookerGraphNode: true, statusEndpoint: 'https://graph-api.dapplooker.com/status/graphql' },
      'https://api.thegraph.com': { isDappLookerGraphNode: false, statusEndpoint: 'https://api.thegraph.com/index-node/graphql' },
      'https://gateway.thegraph.com': { isDappLookerGraphNode: false, statusEndpoint: 'https://api.thegraph.com/index-node/graphql' }
    }
  }

  public get defaultGraphQLTimeoutMS(): number {
    return 20000; // 20 seconds
  }

  public get schemaExtraWhereConditionMapping() {
    return {
      'graph_analytics_arbitrum': {
        'DelegatorDailyData': 'totalUnrealizedRewards_not: "0"',
        'DelegatedStakeDailyData': 'unrealizedRewards_not: "0"',
      },
      'graph_network_mainnet_analytics': {
        'DelegatorDailyData': 'totalUnrealizedRewards_not: "0"',
        'DelegatedStakeDailyData': 'unrealizedRewards_not: "0"',
      },
    };
  }

  public get customFieldSubgraphs() {
    return [
      'https://api.thegraph.com/subgraphs/name/graphprotocol/graph-network-mainnet',
      'https://api.thegraph.com/subgraphs/name/graphprotocol/graph-network-arbitrum'
    ];
  }

  public get nonIdFieldSubgraphs() {
    return [
      'https://graph.mintbase.xyz/mainnet'
    ];
  }

  public get studioApiKeys() {
    return [
      "c9747725693862a3c28deda7a5eaace4",
      "a250db8b487c19c833bdea7814a70b2d",
      "a7fff511948eaefcbeccafd6e0dda5ed",
      "0729990c3d8ef733b771bb4b314718ef",
      "f4c7ef496933d51401762243306af01f",
      "0a45563d257d1515182d620345bc1551",
      "c890bb05c5eed93e7169bd3a2eb71d34",
      "81eccf9f437a8755f32e28cb51f76480"

    ];
  }

  public get BlockDaemonApiKeys(): Array<string> {
    return [
      // 'zpka_0ee4eebd7bf34963a3134b6b8d1734d3_12ff7127', // Abdul Huq
      // 'zpka_87ca420257ec4d5d8532709260f51e74_7dfc1981', // Vikash Choubey
      // 'zpka_c60489c48505430dbd40686cd6d0ac1c_349746de', // Harsh Gupta
      // 'zpka_8a5e766438b742959e43cd522751e3cf_1b80388a', // Mannan
      // 'zpka_4e12073cb90541268ae8d8fcb50a691e_0b8f4611', // Keshav
      // 'zpka_2a5d9c3989f94c53827d732f5c4f3b4e_28a90803', // Hitesh
      // 'zpka_90610ebf831b43a7b2d1254ac3ea3d15_50f428b6', // Abdul Huq
      'zpka_ba4bf35df64142eea4880318606e4106_319f4099' // Vikash Choubey - Fee trail
      // 'zpka_a4821cfcd4694ae5b1c23881507f6a55_2df51450', // Hitesh
      // 'zpka_cfa1c43573004fd0b44ddd8f2b78c4e0_2f8bbf8c', // Abhay Sir
      // 'zpka_792b76026c9f4b94a05b61b996258e48_45632eb9', // Abhay Sir
      // 'zpka_09e8973fee314b86b4c02630c141d7aa_7e030ba4', // Abhay Sir
      // 'zpka_8b68ccaebaf643308e71981997bf1066_51dfd467', // Gupta
      // 'zpka_f4df4c559c274ffab5e04311ecb63908_4b105f2a' // Vikash Sir
    ];
  }

  public replaceApiKeyInUrl(url: string): string {
    const currentKey = this.studioApiKeys[this.keyIndex];
    this.keyIndex = (this.keyIndex + 1) % this.studioApiKeys.length;
    return url.replace(/\/api\/([^\/]+)\//, `/api/${currentKey}/`);
  }

  public get virtualTokenDappIds(): number[] {
    return [
      120967,
      120970,
      120971,
      120972,
      120973,
      120990,
      120995,
      120996,
      120993
    ];
  }
}

export default new IngestorConstant();
