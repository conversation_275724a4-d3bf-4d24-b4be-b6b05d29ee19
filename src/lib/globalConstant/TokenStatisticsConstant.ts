class TokenStatisticsConstants {

    public get tokenStatisticsTableName(): string {
        return "token_statistics";
    }

    public get resultLimit(): number {
        return 2000;
    }

    public tokenBurnRecordsQuery(page: number, virtual_ai_agents_time?: string, virtual_ai_agents_old_time?: string, virtual_ai_agents_prototype_time?: string, virtual_ai_agents_migrated_time?: string, virtuals_genesis_agents_time?: string, virtuals_ethereum_agents_time?: string): string {
        return `
        WITH cte
        AS (
            SELECT DISTINCT "id" AS "token_address"
                ,t."amount"
                ,CASE
                    WHEN t."last_burned_at" = '0000-00-00 00:00:00'
                        THEN NULL
                    ELSE t."last_burned_at"
                    END AS "last_burned_at"
            FROM "virtual_ai_agents"."tokens" tt
            LEFT JOIN (
                SELECT "token_address"
                    ,sum("amount" / power(10, 18)) AS "amount"
                    ,min("time_stamp") AS "last_burned_at"
                FROM "virtual_ai_agents"."transfers"
                WHERE "to" IN (
                        '******************************************'
                        )
                AND "time_stamp" >= parseDateTimeBestEffort('${virtual_ai_agents_time || '1970-01-01T00:00:00Z'}')
                AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                GROUP BY "token_address"
                ) t ON tt."id" = t."token_address"
            WHERE tt."status" = 'GRADUATED'
            AND t."amount" IS NOT NULL

            UNION ALL

            SELECT DISTINCT "id" AS "token_address"
                ,t."amount"
                ,CASE
                    WHEN t."last_burned_at" = '0000-00-00 00:00:00'
                        THEN NULL
                    ELSE t."last_burned_at"
                    END AS "last_burned_at"
            FROM "virtual_ai_agents_old"."tokens" tt
            LEFT JOIN (
                SELECT "token_address"
                    ,sum("amount" / power(10, 18)) AS "amount"
                    ,min("time_stamp") AS "last_burned_at"
                FROM "virtual_ai_agents_old"."transfers"
                WHERE "to" IN (
                        '******************************************'
                        )
                AND "time_stamp" >= parseDateTimeBestEffort('${virtual_ai_agents_old_time || '1970-01-01T00:00:00Z'}')
                AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                GROUP BY "token_address"
                ) t ON tt."id" = t."token_address"
            WHERE tt."status" = 'GRADUATED'
            AND t."amount" IS NOT NULL

            UNION ALL

            SELECT DISTINCT "id" AS "token_address"
                ,t."amount"
                ,CASE
                    WHEN t."last_burned_at" = '0000-00-00 00:00:00'
                        THEN NULL
                    ELSE t."last_burned_at"
                    END AS "last_burned_at"
            FROM "virtual_ai_agents_migrated"."tokens" tt
            LEFT JOIN (
                SELECT "token_address"
                    ,sum("amount" / power(10, 18)) AS "amount"
                    ,min("time_stamp") AS "last_burned_at"
                FROM "virtual_ai_agents_migrated"."transfers"
                WHERE "to" IN (
                        '******************************************'
                        )
                AND "time_stamp" >= parseDateTimeBestEffort('${virtual_ai_agents_migrated_time || '1970-01-01T00:00:00Z'}')
                AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                GROUP BY "token_address"
                ) t ON tt."id" = t."token_address"
            WHERE t."amount" IS NOT NULL

            UNION ALL

            SELECT DISTINCT "id" AS "token_address"
                ,t."amount"
                ,CASE
                    WHEN t."last_burned_at" = '0000-00-00 00:00:00'
                        THEN NULL
                    ELSE t."last_burned_at"
                    END AS "last_burned_at"
            FROM "virtual_ai_agents_prototype"."tokens" tt
            LEFT JOIN (
                SELECT "token_address"
                    ,sum("amount" / power(10, 18)) AS "amount"
                    ,min("time_stamp") AS "last_burned_at"
                FROM "virtual_ai_agents_prototype"."transfers"
                WHERE "to" IN (
                        '******************************************'
                        )
                AND "time_stamp" >= parseDateTimeBestEffort('${virtual_ai_agents_prototype_time || '1970-01-01T00:00:00Z'}')
                AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                GROUP BY "token_address"
                ) t ON tt."id" = t."token_address"
            WHERE tt."post_graduated_token_address" IS NULL
            AND t."amount" IS NOT NULL

            UNION ALL

            SELECT DISTINCT "id" AS "token_address"
                ,t."amount"
                ,CASE
                    WHEN t."last_burned_at" = '0000-00-00 00:00:00'
                        THEN NULL
                    ELSE t."last_burned_at"
                    END AS "last_burned_at"
            FROM "virtuals_genesis_agents"."tokens" tt FINAL
            LEFT JOIN (
                SELECT "token_address"
                    ,sum("amount" / power(10, 18)) AS "amount"
                    ,min("time_stamp") AS "last_burned_at"
                FROM "virtuals_genesis_agents"."transfers"
                WHERE "to" IN (
                        '******************************************'
                        )
                AND "time_stamp" >= parseDateTimeBestEffort('${virtuals_genesis_agents_time || '1970-01-01T00:00:00Z'}')
                AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                GROUP BY "token_address"
                ) t ON tt."id" = t."token_address"
            WHERE t."amount" IS NOT NULL

            UNION ALL

            SELECT DISTINCT "id" AS "token_address"
                ,t."amount"
                ,CASE
                    WHEN t."last_burned_at" = '0000-00-00 00:00:00'
                        THEN NULL
                    ELSE t."last_burned_at"
                    END AS "last_burned_at"
            FROM "virtuals_ethereum_agents"."tokens" tt FINAL
            LEFT JOIN (
                SELECT "token_address"
                    ,sum("amount" / power(10, 18)) AS "amount"
                    ,min("time_stamp") AS "last_burned_at"
                FROM "virtuals_ethereum_agents"."transfers"
                WHERE "to" IN (
                        '******************************************'
                        )
                AND "time_stamp" >= parseDateTimeBestEffort('${virtuals_ethereum_agents_time || '1970-01-01T00:00:00Z'}')
                AND "time_stamp" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                GROUP BY "token_address"
                ) t ON tt."id" = t."token_address"
            WHERE t."amount" IS NOT NULL
        )
        SELECT *
        FROM cte
        ORDER BY "token_address" ASC
        LIMIT ${this.resultLimit} OFFSET ${page * this.resultLimit}
        `;
    }

    public tokenDevInitialSupplyQuery(page: number, virtual_ai_agents_time?: string, virtual_ai_agents_old_time?: string, virtual_ai_agents_prototype_time?: string, virtual_ai_agents_migrated_time?: string, virtuals_genesis_agents_time?: string, virtuals_ethereum_agents_time?: string): string {
        return `
        WITH cte
        AS (
            -- New agents query
            WITH first_tx AS (
                    SELECT tr."to"
                        ,tr."token_address"
                        ,tr."amount" / POWER(10, 18) AS "amount"
                        ,tr."time_stamp"
                        ,ROW_NUMBER() OVER (
                            PARTITION BY tr."to"
                            ,tr."token_address" ORDER BY tr."time_stamp" ASC
                            ) AS rn
                    FROM "virtual_ai_agents"."transfers" tr
                    JOIN "virtual_ai_agents"."tokens" t ON tr."to" = t."dev_wallet"
                        AND tr."token_address" = t."id"
                        AND t."created_at" >= parseDateTimeBestEffort('${virtual_ai_agents_time || '1970-01-01T00:00:00Z'}')
                        AND t."created_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                    )
            SELECT t."id" AS "token_address"
                ,t."dev_wallet" AS "dev_wallet"
                ,f."amount" AS "amount"
            FROM "virtual_ai_agents"."tokens" t FINAL
            INNER JOIN first_tx f ON t."id" = f."token_address"
            WHERE t."status" = 'GRADUATED'
                AND f."rn" = 1

            UNION ALL

            -- Old agents query
            WITH first_tx AS (
                    SELECT tr."to"
                        ,tr."token_address"
                        ,tr."amount" / POWER(10, 18) AS "amount"
                        ,tr."time_stamp"
                        ,ROW_NUMBER() OVER (
                            PARTITION BY tr."to"
                            ,tr."token_address" ORDER BY tr."time_stamp" ASC
                            ) AS rn
                    FROM "virtual_ai_agents_old"."transfers" tr
                    JOIN "virtual_ai_agents_old"."tokens" t ON tr."to" = t."dev_wallet"
                        AND tr."token_address" = t."id"
                        AND t."created_at" >= parseDateTimeBestEffort('${virtual_ai_agents_old_time || '1970-01-01T00:00:00Z'}')
                        AND t."created_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                    )
            SELECT t."id" AS "token_address"
                ,t."dev_wallet" AS "dev_wallet"
                ,f."amount" AS "amount"
            FROM "virtual_ai_agents_old"."tokens" t FINAL
            INNER JOIN first_tx f ON t."id" = f."token_address"
            WHERE t."status" = 'GRADUATED'
                AND f."rn" = 1

            UNION ALL

            -- Migrated agents query
            WITH first_tx AS (
                    SELECT tr."to"
                        ,tr."token_address"
                        ,tr."amount" / POWER(10, 18) AS "amount"
                        ,tr."time_stamp"
                        ,ROW_NUMBER() OVER (
                            PARTITION BY tr."to"
                            ,tr."token_address" ORDER BY tr."time_stamp" ASC
                            ) AS rn
                    FROM "virtual_ai_agents_migrated"."transfers" tr
                    JOIN "virtual_ai_agents_migrated"."tokens" t ON tr."to" = t."dev_wallet"
                        AND tr."token_address" = t."id"
                        AND t."created_at" >= parseDateTimeBestEffort('${virtual_ai_agents_migrated_time || '1970-01-01T00:00:00Z'}')
                        AND t."created_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                    )
            SELECT t."id" AS "token_address"
                ,t."dev_wallet" AS "dev_wallet"
                ,f."amount" AS "amount"
            FROM "virtual_ai_agents_migrated"."tokens" t FINAL
            INNER JOIN first_tx f ON t."id" = f."token_address"
            WHERE f."rn" = 1

            UNION ALL

            -- Prototype agents query
            WITH first_tx AS (
                    SELECT tr."to"
                        ,tr."token_address"
                        ,tr."amount" / POWER(10, 18) AS "amount"
                        ,tr."time_stamp"
                        ,ROW_NUMBER() OVER (
                            PARTITION BY tr."to"
                            ,tr."token_address" ORDER BY tr."time_stamp" ASC
                            ) AS rn
                    FROM "virtual_ai_agents_prototype"."transfers" tr
                    JOIN "virtual_ai_agents_prototype"."tokens" t ON tr."to" = t."dev_wallet"
                        AND tr."token_address" = t."id"
                        AND t."created_at" >= parseDateTimeBestEffort('${virtual_ai_agents_prototype_time || '1970-01-01T00:00:00Z'}')
                        AND t."created_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                    )
            SELECT t."id" AS "token_address"
                ,t."dev_wallet" AS "dev_wallet"
                ,f."amount" AS "amount"
            FROM "virtual_ai_agents_prototype"."tokens" t FINAL
            INNER JOIN first_tx f ON t."id" = f."token_address"
            WHERE t."status" = 'UNDER_GRADUATED'
                AND f."rn" = 1

            UNION ALL

            -- Genesis agents query
            WITH first_tx AS (
                    SELECT tr."to"
                        ,tr."token_address"
                        ,tr."amount" / POWER(10, 18) AS "amount"
                        ,tr."time_stamp"
                        ,ROW_NUMBER() OVER (
                            PARTITION BY tr."to"
                            ,tr."token_address" ORDER BY tr."time_stamp" ASC
                            ) AS rn
                    FROM "virtuals_genesis_agents"."transfers" tr
                    JOIN "virtuals_genesis_agents"."tokens" t FINAL ON tr."to" = t."dev_wallet"
                        AND tr."token_address" = t."id"
                        AND t."created_at" >= parseDateTimeBestEffort('${virtuals_genesis_agents_time || '1970-01-01T00:00:00Z'}')
                        AND t."created_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                    )
            SELECT t."id" AS "token_address"
                ,t."dev_wallet" AS "dev_wallet"
                ,f."amount" AS "amount"
            FROM "virtuals_genesis_agents"."tokens" t FINAL
            INNER JOIN first_tx f ON t."id" = f."token_address"
            WHERE f."rn" = 1

            UNION ALL

            -- Ethereum agents query
            WITH first_tx AS (
                SELECT tr."to"
                    ,tr."token_address"
                    ,tr."amount" / POWER(10, 18) AS "amount"
                    ,tr."time_stamp"
                    ,ROW_NUMBER() OVER (
                        PARTITION BY tr."to"
                        ,tr."token_address" ORDER BY tr."time_stamp" ASC
                        ) AS rn
                    FROM "virtuals_ethereum_agents"."transfers" tr
                    JOIN "virtuals_ethereum_agents"."tokens" t FINAL ON tr."to" = t."dev_wallet"
                        AND tr."token_address" = t."id"
                        AND t."created_at" >= parseDateTimeBestEffort('${virtuals_ethereum_agents_time || '1970-01-01T00:00:00Z'}')
                        AND t."created_at" < toStartOfMinute(toDateTime((CAST(now() AS timestamp) + INTERVAL 1 minute)))
                    )
            SELECT t."id" AS "token_address"
                ,t."dev_wallet" AS "dev_wallet"
                ,f."amount" AS "amount"
            FROM "virtuals_ethereum_agents"."tokens" t FINAL
            INNER JOIN first_tx f ON t."id" = f."token_address"
            WHERE f."rn" = 1
            )
        SELECT *
        FROM cte
        ORDER BY "token_address", "dev_wallet"
        LIMIT ${this.resultLimit} OFFSET ${page * this.resultLimit}
        `
    }
}

export default new TokenStatisticsConstants();
