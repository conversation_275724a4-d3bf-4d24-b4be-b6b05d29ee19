class GenesisParticipantsAnalysisConstant {
    public get genesisParticipantsAnalysisTableName(): string {
        return "genesis_participants_analysis";
    }

    public get resultLimit(): number {
        return 2000;
    }

    public get userTokenPairBatchSize(): number {
        return 500;
    }

    public genesisParticipantsQuery(genesisUpdatedBlock: number, participantCreatedBlock: number, offset: number = 0): string {
        return `
        SELECT
            vgap.user AS userAddress,
            vgap.genesis AS genesisId,
            vgag.associated_token_address AS tokenAddress,
            vgag.updated_block_number AS genesisUpdatedBlock,
            vgag.uuid AS genesisUuid,
            vgap.pointsCommitted as pointsCommitted,
            vgap.virtualsCommitted as virtualsCommitted,
            vgap.participantCreatedBlock as participantCreatedBlock,
            vgap.participantUuid as participantUuid
        FROM (
            SELECT id, associated_token_address, updated_block_number, uuid
            FROM virtuals_genesis_agents.geneses
            WHERE status = 'SUCCESS'
            AND updated_block_number >= ${genesisUpdatedBlock}
        ) vgag
        INNER JOIN (
            SELECT
                user,
                genesis,
                SUM(point) AS pointsCommitted,
                SUM(virtuals) AS virtualsCommitted,
                MAX(created_block_number) AS participantCreatedBlock,
                argMax(uuid, created_block_number) AS participantUuid
            FROM virtuals_genesis_agents.participateds
            WHERE created_block_number >= ${participantCreatedBlock}
            GROUP BY user, genesis
        ) vgap
        ON vgag.id = vgap.genesis
        ORDER BY
            participantUuid ASC,
            genesisUuid ASC
        LIMIT ${this.resultLimit}
        OFFSET ${offset}
        `
    }

    public virtualsRefundedQuery(refundClaimedUuid: number, offset: number = 0): string {
        return `
        SELECT uuid, user as userAddress, genesis as genesisId, amount as refundAmount
        FROM virtuals_genesis_agents.refund_claimeds
        WHERE uuid > ${refundClaimedUuid}
        ORDER BY uuid ASC
        LIMIT ${this.resultLimit}
        OFFSET ${offset}
        `
    }

    public initialTokenAllocationQuery(agentTokenClaimedsUuid: number, offset: number = 0): string {
        return `
        SELECT
            genesis AS genesisId,
            user AS userAddress,
            amount AS claimedAmount,
            created_at AS claimedAt,
            uuid
        FROM virtuals_genesis_agents.agent_token_claimeds
        WHERE uuid > ${agentTokenClaimedsUuid}
        ORDER BY uuid ASC
        LIMIT ${this.resultLimit}
        OFFSET ${offset}
        `
    }

    public v2InitialTokenAllocationQuery(agentTokenClaimedsUuid: number, offset: number = 0): string {
        return `
        SELECT
            token_address AS tokenAddress,
            account AS userAddress,
            amount AS claimedAmount,
            created_at AS claimedAt,
            uuid
        FROM claim_event_v2.agent_token_claimed_updateds
        WHERE uuid > ${agentTokenClaimedsUuid}
        ORDER BY uuid ASC
        LIMIT ${this.resultLimit}
        OFFSET ${offset}
        `
    }

    public userTokenBalanceQuery(userTokenPairs: { userAddress: string, tokenAddress: string }[], lastUpdatedAt: number): string {
        const tupleValues = userTokenPairs.map(pair =>
            `('${pair.userAddress}', '${pair.tokenAddress}')`
        ).join(", ");

        return `
        SELECT
            user_address as userAddress,
            token_address as tokenAddress,
            amount as tokenBalance,
            last_updated_at as lastUpdatedAt,
            uuid
        FROM virtuals_genesis_agents.token_holders FINAL
        WHERE last_updated_at >= ${lastUpdatedAt}
        AND last_updated_at < toUnixTimestamp(toStartOfMinute(now() + INTERVAL 1 minute))
        AND (user_address, token_address) IN (${tupleValues})
        ORDER BY last_updated_at ASC, uuid ASC
        `
    }

    public userStakingDetailsQuery(userTokenPairs: { userAddress: string, tokenAddress: string }[], lastUpdatedAt: number): string {
        const tupleValues = userTokenPairs.map(pair =>
            `('${pair.userAddress}', '${pair.tokenAddress}')`
        ).join(", ");

        return `
        SELECT 
            user as userAddress,
            token as tokenAddress,
            current_stake_amount as currentStakeAmount,
            last_updated_at as lastUpdatedAt,
            first_stake_at as firstStakeAt,
            uuid
        FROM virtuals_staking.token_stake_by_users FINAL
        WHERE last_updated_at >= ${lastUpdatedAt}
        AND last_updated_at < toUnixTimestamp(toStartOfMinute(now() + INTERVAL 1 minute))
        AND (user, token) IN (${tupleValues})
        ORDER BY last_updated_at ASC, uuid ASC
        `
    }

    // ETHEREUM QUERIES
    public ethereumGenesisParticipantsQuery(genesisCreatedBlock: number, participantCreatedBlock: number, offset: number = 0): string {
        return `
        SELECT
            vgap.user AS userAddress,
            veat.id AS tokenAddress,
            gigm.genesisId AS genesisId,
            veat.created_block_number AS genesisCreatedBlock,
            veat.uuid AS genesisUuid,
            vgap.pointsCommitted as pointsCommitted,
            vgap.virtualsCommitted as virtualsCommitted,
            vgap.participantCreatedBlock as participantCreatedBlock,
            vgap.participantUuid as participantUuid
        FROM (
            SELECT id, created_block_number, uuid
            FROM virtuals_ethereum_agents.tokens FINAL
            WHERE created_block_number >= ${genesisCreatedBlock}
        ) veat
        INNER JOIN (
            SELECT CAST(genesis_id AS String) AS genesisId, token_address
            FROM genesis_insights.genesis_metadata
            WHERE chain = 3 AND status = 2
        ) gigm ON veat.id = gigm.token_address
        INNER JOIN (
            SELECT
                user,
                genesis,
                SUM(point) AS pointsCommitted,
                SUM(virtuals) AS virtualsCommitted,
                MAX(created_block_number) AS participantCreatedBlock,
                argMax(uuid, created_block_number) AS participantUuid
            FROM virtuals_genesis_agents.participateds
            WHERE created_block_number >= ${participantCreatedBlock}
            GROUP BY user, genesis
        ) vgap ON gigm.genesisId = vgap.genesis
        ORDER BY
        participantUuid ASC,
        genesisUuid ASC
        LIMIT ${this.resultLimit}
        OFFSET ${offset}
        `
    }

    public ethereumInitialTokenAllocationQuery(agentTokenClaimedsUuid: number, offset: number = 0): string {
        return `
        SELECT
            token_address AS tokenAddress,
            account AS userAddress,
            amount AS claimedAmount,
            created_at AS claimedAt,
            uuid
        FROM virtuals_ethereum_agents.agent_token_claimeds
        WHERE uuid > ${agentTokenClaimedsUuid}
        ORDER BY uuid ASC
        LIMIT ${this.resultLimit}
        OFFSET ${offset}
        `
    }

    public ethereumUserTokenBalanceQuery(userTokenPairs: { userAddress: string, tokenAddress: string }[], lastUpdatedAt: number): string {
        const tupleValues = userTokenPairs.map(pair =>
            `('${pair.userAddress}', '${pair.tokenAddress}')`
        ).join(", ");

        return `
        SELECT
            user_address as userAddress,
            token_address as tokenAddress,
            amount as tokenBalance,
            last_updated_at as lastUpdatedAt,
            uuid
        FROM virtuals_ethereum_agents.token_holders FINAL
        WHERE last_updated_at >= ${lastUpdatedAt}
        AND last_updated_at < toUnixTimestamp(toStartOfMinute(now() + INTERVAL 1 minute))
        AND (user_address, token_address) IN (${tupleValues})
        ORDER BY last_updated_at ASC, uuid ASC
        `
    }

    public ethereumUserStakingDetailsQuery(userTokenPairs: { userAddress: string, tokenAddress: string }[], lastUpdatedAt: number): string {
        const tupleValues = userTokenPairs.map(pair =>
            `('${pair.userAddress}', '${pair.tokenAddress}')`
        ).join(", ");

        return `
        SELECT 
            user as userAddress,
            token as tokenAddress,
            current_stake_amount as currentStakeAmount,
            last_updated_at as lastUpdatedAt,
            first_stake_at as firstStakeAt,
            uuid
        FROM virtuals_ethereum_agents.token_stake_by_users FINAL
        WHERE last_updated_at >= ${lastUpdatedAt}
        AND last_updated_at < toUnixTimestamp(toStartOfMinute(now() + INTERVAL 1 minute))
        AND (user, token) IN (${tupleValues})
        ORDER BY last_updated_at ASC, uuid ASC
        `
    }
}

export default new GenesisParticipantsAnalysisConstant();
