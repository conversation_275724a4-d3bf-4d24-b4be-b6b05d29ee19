import ErrorLog from "../app/model/ErrorLog";
import IngestorProcessorModel from "../app/model/IngestorProcessor";
import SubgraphMetricModel from "../app/model/SubgraphMetric";
import SubgraphTrackerModel from "../app/model/Subgraphtracker";
import TokenPrices from "../app/model/TokenPrices";
import TokenPricesHistorical from "../app/model/TokenPricesHistorical";
import UserDappModel from "../app/model/UserDapp";
import UserUsageStatsModel from "../app/model/UserUsageStats";
import UserOffChainProject, { Status as OffchainProjectStatus, Type as OffchainProjectType } from "../app/model/UserOffChainProject";
import DappSchemaDetailsModel from "../app/model/DappSchemaDetails";
import HistoricalTokenPrices from "../app/model/HistoricalTokenPrices";
import IndexedNetworkModel from "../app/model/IndexedNetwork";
import TokenExtendedDetails from "../app/model/TokenExtendedDetails";
import TokenCategoryMap from "../app/model/TokenCategoryMap";
import ExtendedTokensHistoricalData from "../app/model/ExtendedTokensHistoricalData";
import DevBundleDataModel from "../app/model/DevBundleDataModel";
import TokenStatisticsModel from "../app/model/TokenStatisticsModel";
import DevBundleTrackerModel from "../app/model/DevBundleTrackerModel";
import DevBundleTransactionsModel from "../app/model/DevBundleTransactionsBase";
import DevWalletFundingTransactions from "../app/model/DevWalletFundingTransactions";
import AgentGraduationAddressMap from "../app/model/AgentGraduationAddressMap";
import GenesisParticipantsAnalysis from "../app/model/GenesisParticipantsAnalysis";
import GenesisParticipantsAggregation from "../app/model/GenesisParticipantsAggregation";

export interface IngestorConfigDefination {
    sourceDataStore: string;
    destinationDataStore: string;
    subGraphEndPoint: string;
    dbInfo: {
        schema: string;
        connectionUrl: string;
    };
    entityAction?: {};
    excludeEntities?: string[];
}

export interface SubscriptionEntityInterface {
    subscriptionQueryName: string;
    subscriptionQuery: string;

    getSubscriptionQueryVariables(): Promise<Record<string, any>>;

    responseHandler(responseData: any): Promise<void>;
}

export interface IngestorCLIParametersInterface {
    workerId: number;
    isPriority: number;
    isProcessNewDapps: number;
    ingestorConfigPath: string;
}

export interface ClickHouseConfig {
    host: string;
    request_timeout?: number;
    compression?: {
        response?: boolean;
        request?: boolean;
    };
    username: string;
    password: string;
    application?: string;
    database?: string;
    session_id?: string;
    keep_alive?: {
        enabled?: boolean;
        retry_on_expired_socket?: boolean;
        socket_ttl?: number;
    };
    additional_headers?: Record<string, string>;
    max_open_connections: number;
}

/** ************Model Record Interface Start************* */
export interface IngestorProcessorModelAttributes {
    id?: number;
    userDappId: number;
    config: IngestorConfigDefination;
    status: number;
    lastRunTimestamp: number;
    workerId?: number;
    priority: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface SubgraphMetricAttributes {
    [key: number]: number;

    [key: symbol]: symbol;

    id?: number;
    userDappId: number;
    graphId: string;
    currentBlock?: number;
    totalBlocks?: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface ErrorLogModelAttributes {
    [key: number]: number;

    [key: symbol]: symbol;

    id: number | undefined;
    appType: number;
    severity: number;
    status: number;
    machineIp: string;
    data: string;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface SubgraphTrackerModelAttributes {
    [key: number]: number;

    [key: symbol]: symbol;

    id: number | undefined;
    subgraphEndpointInGraph: string;
    subgraphNameInGraph: string;
    userDappId?: number;
    accessToken: string;
    status: number;
    properties: number;
    subgraphIpfsHash?: string;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface CurrentTokenModelAttributes {
    [key: number]: number;

    [key: symbol]: symbol;

    tokenId: string;
    tokenName: string;
    tokenSymbol: string;
    polygonAddress?: string;
    fantomAddress?: string;
    ethereumAddress?: string;
    xdaiAddress?: string;
    binanceChainAddress?: string;
    arbitrumOneAddress?: string;
    moonriverAddress?: string;
    avalancheAddress?: string;
    optimismAddress?: string;
    nearAddress?: string;
    moonbeamAddress?: string;
    celoAddress?: string;
    solanaAddress?: string;
    totalSupply?: string;
    circulatingSupply?: number;
    usdPrice?: number;
    totalVolume?: number;
    marketCap?: number;
    time?: number;
    decimal?: number;
}

export interface TokenPricesHistoricalModelAttribute {
    [key: number]: number;

    [key: symbol]: symbol;

    tokenAddress: string;
    tokenId: string;
    tokenName: string;
    tokenSymbol: string;
    usdPrice: number;
    totalVolume: number;
    marketCap: number;
    time: string;
}

export interface TokenHistoricalDetails {
    tokenId: string;
    price: number;
    totalVolume: number;
    marketCap: number;
    time: number;
}

export interface CollatorAttributes {
    address: string;
    selfStakeAmount: number;
    totalStakeAmount: number;
    totalBackingAmount: number;
    activeNominatorsCount: number;
    waitingNominatorsCount: number;
    lowestNominationStakeAmount: number;
    highestNominationStakeAmount: number;
    deposit?: number;
    web?: string;
    email?: string;
    twitter?: string;
    apy?: number;
    status: string;
}

export interface CollatorNaminatorAttributes {
    collatorAddress?: string;
    nominatorAddress?: string;
    stakedAmountByNominator: number;
    isTopNominator: Boolean;
}

export interface CoreDataAttributes {
    id: number;
    totalStakedAmount: string;
    collatorCommission: number;
    activeCollators: number;
    currentRoundNumber: number;
    currentRoundFirstBlockNumber: number;
    roundLength: number;
}

export interface CollatorRewardAttributes {
    collatorAddress: string;
    rewardAmount: number;
    blockNumber: number;
    blockTimestamp: string;
}

export interface CollatorNominatorRewardAttributes {
    nominatorAddress: string;
    rewardAmount: number;
    blockNumber: number;
    blockTimestamp: string;
}

export interface NominatorAttributes {
    nominatorAddress: string;
    totalStakedAmount: number;
    totalStakedValidators: number;
}

export interface BitcannaValidatorAttributes {
    validatorAddress: string;
    moniker: string;
    operatorAddress: string;
    selfDelegateAddress: string;
    selfDelegationAmount: number;
    totalDelegators: number;
    totalDelegationAmount: number;
    totalRewardAmountDistributed: number;
    status: string;
}

export interface BitcannaDelegationsAttributes {
    validatorAddress: string;
    delegatorAddress: string;
    amount: number;
    height: number;
}

export interface BitcannaDelegationRewardsAttributes {
    validatorAddress: string;
    delegatorAddress: string;
    amount: number;
    height: number;
}

export interface UserDappModelAttributes {
    id: number | undefined;
    userId: number;
    name: string;
    website: string;
    dashboardIdentifier?: string;
    logoS3Identifier: string;
    status: number;
    dashboardStatus: number;
    failureReason?: number;
    properties: number;
    permalink: string;
    protocolId: number;
    networkId: number;
    title?: string;
    position: number;
    analyzerGroupId?: number;
    analyzerCollectionId?: number;
    analyzerDatabaseId?: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface UserOffchainProjectModelAttributes {
    [key: string]: any;

    [key: number]: any;

    [key: symbol]: any;

    id: number | undefined;
    userId: number;
    userDappId: number;
    projectType: OffchainProjectType;
    dataSourceUrl: string;
    frequency: number;
    status: OffchainProjectStatus;
    lastRunTimestamp: Date;
    extraData: any;
    createdAt?: Date;
    updatedAt?: Date;
    retryCount?: number | null;
}

export interface DappSchemaDetailsModelAttribute {
    [key: string]: any;

    [key: number]: any;

    [key: symbol]: any;

    id?: number;
    userDappId: number;
    name: string;
    schemaName: string;
}

export interface HistoricalTokenPriceModelAttributes {
    [key: string]: any;

    [key: number]: any;

    [key: symbol]: any;

    dateTime: string;
    tokenId: string;
    tokenSymbol: string;
    tokenName: string;
    usdPrice: number;
    totalVolume: number;
    marketCap: number;
}

export interface DevBundleDataModelAttributes {
    [key: string]: any;

    [key: number]: any;

    [key: symbol]: any;

    tokenAddress: string;
    devWalletAddress: string;
    devWalletType: number;
    parentWallet?: string;
    totalOutflowAmount?: number;
    outflowToNonDevWallet?: number;
    soldAmount?: number;
    totalInflowAmount?: number;
    inflowFromAllWallets?: number;
    inflowFromDevWallet?: number;
    buyAmount?: number;
    stakedOrVestedAmount?: number;
    balance?: number;
    holdingPercentage?: number;
    rugStatus?: number;
    createdAt?: string;
    updatedAt?: string;
}

export interface TokenStatisticsModelAttributes {
    [key: string]: any;

    [key: number]: any;

    [key: symbol]: any;

    tokenAddress: string;
    tokenBurnAmount: number;
    lastBurnedAt: number;
    devInitialSupply: number;
    createdAt: string;
    updatedAt: string;
}

export interface DevBundleTrackerModelAttributes {
    [key: string]: any;

    [key: number]: any;

    [key: symbol]: any;

    schema: number;
    queryType: number;
    lastRunAt: string;
    lastRunId?: number;
    updatedAt?: string;
    createdAt?: string;
}

export interface DevBundleTransactionsModelAttributes {
    [key: string]: any;

    [key: number]: any;

    [key: symbol]: any;

    tokenAddress: string;
    devWalletAddress: string;
    transactionType: number;
    from: string;
    to: string;
    amount: number;
    date: string;
    txHash: string;
    createdAt?: string;
    updatedAt?: string;
}

export interface GenesisTokenData {
    genesis_id: number;
    token: string;
    name: string;
    genesis_address: string;
    token_address: string | null;
    status: number;
    start_date: string | null;
    end_date: string | null;
    updated_at: string;
}

export interface GenesisParticipantsAnalysisModelAttributes {
    [key: string]: any;

    [key: number]: any;

    [key: symbol]: any;

    userAddress: string;
    tokenAddress: string;
    genesisId: number;
    pointsCommitted: number;
    virtualsCommitted: number;
    virtualsRefunded: number;
    initialTokenAllocation: number;
    initialAllocationClaimedAt: string;
    currentTokenBalance: number;
    stakedTokenAmount: number;
    firstStakedAt: string;
    chain: number;
    updatedAt?: string;
}

export interface GenesisParticipantsAggregationModelAttributes {
    [key: string]: any;

    [key: number]: any;

    [key: symbol]: any;

    genesisId: number;
    tokenAddress: string;
    chain: number;
    totalParticipants: number;
    soldAll: number;
    soldPartially: number;
    holdingInitialAllocation: number;
    boughtMore: number;
    stakedAmount: number;
    noInitialAllocation: number;
    initialSupply: number;
    currentSupply: number;
    totalStakedAmount: number;
    top25ParticipantsHolding: number;
    updatedAt: string;
}

/** ************Model Record Interface End************* */

/** ************Formatted models interface Start************** */

export interface FormattedSubgraphMetricAttributes {
    id: number;
    userDappId: number;
    graphId: string;
    currentBlock: number;
    totalBlocks: number;
    createdAt: Date;
    updatedAt: Date;
}

export interface FormattedErrorLogModelAttributes {
    id: number;
    appType: string;
    severity: string;
    status: string;
    machineIp: string;
    data: string;
    createdAt: Date;
    updatedAt: Date;
}

export interface FormattedIngestorProcessorAttributes {
    id: number;
    userDappId: number;
    status: string;
    lastRunTimestamp: number;
    workerId: number;
    priority: number;
    createdAt?: Date;
    updatedAt?: Date;
    config?: IngestorConfigDefination;
}

export interface FormattedSubgraphTrackerAttributes {
    id: number;
    subgraphEndpointInGraph: string;
    subgraphNameInGraph: string;
    userDappId: number;
    status: string;
    properties: number;
    subgraphIpfsHash?: string;
    isExternalSubgraph: boolean;
    externalSubgraphEndpoint: string;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface FormattedTokenPrices {
    [key: number]: number;

    [key: symbol]: symbol;

    tokenId: string;
    tokenName: string;
    tokenSymbol: string;
    polygonAddress?: string;
    fantomAddress?: string;
    ethereumAddress?: string;
    xdaiAddress?: string;
    binanceChainAddress?: string;
    arbitrumOneAddress?: string;
    moonriverAddress?: string;
    avalancheAddress?: string;
    optimismAddress?: string;
    nearAddress?: string;
    moonbeamAddress?: string;
    celoAddress?: string;
    totalSupply?: string;
    circulatingSupply?: number;
    usdPrice?: number;
    totalVolume?: number;
    marketCap?: number;
    time?: number;
    decimal?: number;
}

/** ************Formatted models interface End************** */

/** ************Response Class Interface Start************* */
export interface SuccessResponse {
    success: boolean;
    data: any;
}

export interface ErrorResponse {
    success: boolean;
    errorData: Record<string, string>;
    debugOptions: any;
}

export interface ErrorConfig {
    parameter: string;
    message: string;
}

/** ************Response Class Interface END************* */

/** ************Cache params Interface Start************* */

export interface ObjectsByUserDappIdsCacheParams {
    userDappIds: number[];
}

export interface SubgraphSchemaBySubgraphEndpointsCacheParams {
    subgraphEndpoints: string[];
}

export interface SubgraphMetricByUserDappIdsCacheParams {
    userDappIds: number[];
}

/** ************Cache params Interface End************* */

/** ************Container Class Interface Start************* */

export interface Models {
    errorLog: ErrorLog;
    ingestorProcessor: IngestorProcessorModel;
    subgraphMetric: SubgraphMetricModel;
    subgraphTracker: SubgraphTrackerModel;
    tokenPrices: TokenPrices;
    tokenPricesHistorical: TokenPricesHistorical;
    userDapp: UserDappModel;
    userUsageStats: UserUsageStatsModel;
    userOffChainProject: UserOffChainProject;
    dappSchemaDetails: DappSchemaDetailsModel;
    historicalTokenPrices: HistoricalTokenPrices;
    indexedNetwork: IndexedNetworkModel;
    tokenExtendedDetails: TokenExtendedDetails;
    tokenCategoryMap: TokenCategoryMap;
    extendedTokensHistoricalData: ExtendedTokensHistoricalData;
    devBundleDataModel: DevBundleDataModel;
    tokenStatisticsModel: TokenStatisticsModel;
    devBundleTrackerModel: DevBundleTrackerModel;
    devBundleTransactionsBaseModel: DevBundleTransactionsModel;
    devWalletFundingTransactionsModel: DevWalletFundingTransactions;
    agentGraduationAddressMapModel: AgentGraduationAddressMap;
    genesisParticipantsAnalysisModel: GenesisParticipantsAnalysis;
    genesisParticipantsAggregationModel: GenesisParticipantsAggregation;
}

export interface ContainerInterface {
    models: Models;
}

export interface DappDetailForLog {
    userDappId: number;
    subGraphEndPoint: string;
    workerId: number;
}

/** ************Container Class Interface Start************* */
