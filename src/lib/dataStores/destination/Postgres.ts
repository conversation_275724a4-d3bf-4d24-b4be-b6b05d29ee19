import { InitOptions, Sequelize, ModelIndexesOptions } from "sequelize";
import fs from "fs";
import Logger from "../../Logger";
import Constant from "../../../config/Constant";
import ErrorLogModel from "../../../app/model/ErrorLog";
import IngestorProcessorModel from "../../../app/model/IngestorProcessor";
import SubgraphMetricModel from "../../../app/model/SubgraphMetric";
import SubgraphTracker from "../../../app/model/Subgraphtracker";
import { Models } from "../../Types";
import TokenPrices from "../../../app/model/TokenPrices";
import TokenPricesHistorical from "../../../app/model/TokenPricesHistorical";
import UserDappModel from "../../../app/model/UserDapp";
import UserUsageStatsModel from "../../../app/model/UserUsageStats";
import UserOffchainProject from "../../../app/model/UserOffChainProject";
import DappSchemaDetailsModel from "../../../app/model/DappSchemaDetails";
import HistoricalTokenPrices from "../../../app/model/HistoricalTokenPrices";
import IndexedNetworkModel from "../../../app/model/IndexedNetwork";
import TokenExtendedDetails from "../../../app/model/TokenExtendedDetails";
import TokenCategoryMap from "../../../app/model/TokenCategoryMap";
import ExtendedTokensHistoricalData from "../../../app/model/ExtendedTokensHistoricalData";
import DevBundleDataModel from "../../../app/model/DevBundleDataModel";
import TokenStatisticsModel from "../../../app/model/TokenStatisticsModel";
import DevBundleTrackerModel from "../../../app/model/DevBundleTrackerModel";
import DevBundleTransactionsBaseModel from "../../../app/model/DevBundleTransactionsBase";
import DevWalletFundingTransactionsModel from "../../../app/model/DevWalletFundingTransactions";
import AgentGraduationAddressMapModel from "../../../app/model/AgentGraduationAddressMap";
import GenesisParticipantsAnalysisModel from "../../../app/model/GenesisParticipantsAnalysis";
import GenesisParticipantsAggregationModel from "../../../app/model/GenesisParticipantsAggregation";

export default class Postgres {
  public static sequelizeObjects: Map<string, Sequelize> = new Map();
  public readonly sequelize: Sequelize;

  /*
   * @param connectionUrl postgres connection url
   *  e.g.'postgres://user:<EMAIL>:5432/dbname'
   */
  private constructor(sequelize: Sequelize) {
    this.sequelize = sequelize;
  }

  public getDynamicModel(schema: Record<string, any>, tableName: string, indexes: ModelIndexesOptions[], schemaName: string): any {
    const initOptions: InitOptions = {
      sequelize: this.sequelize,
      underscored: true,
      timestamps: false,
      indexes: indexes,
      schema: schemaName
    };

    return this.sequelize.define(tableName, schema, initOptions);
  }

  public async authenticate(): Promise<void> {
    try {
      await this.sequelize.authenticate();
    } catch (e) {
      Logger.error(`Postgres::authenticate::Unable to connect to the database: ${e.message}`);
      throw new Error(`Postgres: Unable to connect to the database: ${e.message}`);
    }
    Logger.info("Postgres::authenticate::Connection has been established successfully.");
  }

  public static getSequelizeObject(connectionUrl: string, enableSSL: boolean = true): Sequelize {
    if (Postgres.sequelizeObjects.has(connectionUrl)) {
      return Postgres.sequelizeObjects.get(connectionUrl)!;
    }
    let isLogging = false;
    if (!Constant.isProduction) {
      isLogging = true;
    }
    const sequelize = new Sequelize(connectionUrl, Postgres.getDataBaseConnectionConfig(isLogging, enableSSL));

    Postgres.sequelizeObjects.set(connectionUrl, sequelize);
    return sequelize;
  }

  public static getDataBaseConnectionConfig(isLogging: boolean, enableSSL: boolean): any {
    let baseConf: any = {
      logging: isLogging,
      benchmark: true,
      typeValidation: true,
      pool: {
        max: 3,
        min: 0,
        acquire: 30000,
        idle: 10000
      }
    };

    if (Constant.machineIp === Constant.customMachineIP) {
      return baseConf;
    }

    if (Constant.isProduction && enableSSL) {
      baseConf.dialect = "postgres";
      baseConf.dialectOptions = {
        ssl: Postgres.getSslConfigValue()
      };
    }
    return baseConf;
  }

  public static getSslConfigValue(): any {
    let sslConfig: any = {
      rejectUnauthorized: true,
      ca: fs.readFileSync(Constant.caCertificatePermissionFilePath)
    };
    return sslConfig;
  }

  public static getClient(connectionUrl: string): Postgres {
    const sequelize = Postgres.getSequelizeObject(connectionUrl);

    return new Postgres(sequelize);
  }

  public static getDbModels(): Models {
    const restfulDBSeqlize = Postgres.getSequelizeObject(Constant.restfulDatabaseconnectionUrl);

    const dappDBSeqlize = Postgres.getSequelizeObject(Constant.dappDatabaseConnectionUrl);

    const restfulDBInitOptions: InitOptions = {
      sequelize: restfulDBSeqlize,
      underscored: true,
      timestamps: true,
      freezeTableName: true
    };

    const dappDBInitOptions: InitOptions = {
      ...restfulDBInitOptions,
      sequelize: dappDBSeqlize
    };

    const errorLog = new ErrorLogModel(restfulDBInitOptions);

    const ingestorProcessor = new IngestorProcessorModel(restfulDBInitOptions);

    const subgraphMetric = new SubgraphMetricModel(restfulDBInitOptions);

    const subgraphTracker = new SubgraphTracker(restfulDBInitOptions);

    const tokenPrices = new TokenPrices(dappDBInitOptions);

    const tokenPricesHistorical = new TokenPricesHistorical(dappDBInitOptions);

    const historicalTokenPrices = new HistoricalTokenPrices(dappDBInitOptions);

    const userDapp = new UserDappModel(restfulDBInitOptions);

    const userUsageStats = new UserUsageStatsModel(restfulDBInitOptions);

    const userOffChainProject = new UserOffchainProject(restfulDBInitOptions);

    const dappSchemaDetails = new DappSchemaDetailsModel(restfulDBInitOptions);

    const indexedNetwork = new IndexedNetworkModel(restfulDBInitOptions);

    const tokenExtendedDetails = new TokenExtendedDetails(dappDBInitOptions);

    const tokenCategoryMap = new TokenCategoryMap(dappDBInitOptions);

    const extendedTokensHistoricalData = new ExtendedTokensHistoricalData(dappDBInitOptions);

    const devBundleDataModel = new DevBundleDataModel(dappDBInitOptions);

    const tokenStatisticsModel = new TokenStatisticsModel(dappDBInitOptions);

    const devBundleTrackerModel = new DevBundleTrackerModel(dappDBInitOptions);

    const devBundleTransactionsBaseModel = new DevBundleTransactionsBaseModel(dappDBInitOptions);

    const devWalletFundingTransactionsModel = new DevWalletFundingTransactionsModel(dappDBInitOptions);

    const agentGraduationAddressMapModel = new AgentGraduationAddressMapModel(dappDBInitOptions);

    const genesisParticipantsAnalysisModel = new GenesisParticipantsAnalysisModel(dappDBInitOptions);

    const genesisParticipantsAggregationModel = new GenesisParticipantsAggregationModel(dappDBInitOptions);

    return {
      errorLog: errorLog,
      ingestorProcessor: ingestorProcessor,
      subgraphMetric: subgraphMetric,
      subgraphTracker: subgraphTracker,
      tokenPrices: tokenPrices,
      tokenPricesHistorical: tokenPricesHistorical,
      userDapp: userDapp,
      userUsageStats: userUsageStats,
      userOffChainProject: userOffChainProject,
      dappSchemaDetails: dappSchemaDetails,
      historicalTokenPrices: historicalTokenPrices,
      indexedNetwork: indexedNetwork,
      tokenExtendedDetails: tokenExtendedDetails,
      tokenCategoryMap: tokenCategoryMap,
      extendedTokensHistoricalData: extendedTokensHistoricalData,
      devBundleDataModel: devBundleDataModel,
      tokenStatisticsModel: tokenStatisticsModel,
      devBundleTrackerModel: devBundleTrackerModel,
      devBundleTransactionsBaseModel: devBundleTransactionsBaseModel,
      devWalletFundingTransactionsModel: devWalletFundingTransactionsModel,
      agentGraduationAddressMapModel: agentGraduationAddressMapModel,
      genesisParticipantsAnalysisModel: genesisParticipantsAnalysisModel,
      genesisParticipantsAggregationModel: genesisParticipantsAggregationModel
    };
  }
}
