class Constant {
    get restfulDatabaseconnectionUrl(): string {
        return `postgres://${process.env.PG_USER}:${process.env.PG_PASSWORD}@${process.env.PG_HOST}:${process.env.PG_PORT}/${process.env.PG_API_DB}`;
    }

    get dappDatabaseConnectionUrl(): string {
        return `postgres://${process.env.DAPP_PG_USER}:${process.env.DAPP_PG_PASSWORD}@${process.env.DAPP_PG_HOST}:${process.env.DAPP_PG_PORT
            }/${process.env.DAPP_PG_DAPP_DB}`;
    }

    get pricingDbSchemaName(): string {
        return process.env.PG_PRICING_SCHEMA!;
    }

    get virtualAgentInsightsSchemaName(): string {
        return process.env.PG_VIRTUAL_AGENT_INSIGHTS_SCHEMA!;
    }

    get machineIp(): string {
        return process.env.MACHINE_IP!;
    }

    // Need to change
    get customMachineIP(): string {
        return 'CustomNode';
    }

    get environment(): string {
        return process.env.ENVIRONMENT!; // 'devlopment', 'production', 'staging'
    }

    get apiInternalToken(): string {
        return process.env.API_INTERNAL_TOKEN!;
    }

    get isProduction(): boolean {
        const oThis = this;
        return oThis.environment == 'production' ? true : false;
    }

    get environmentShort(): string {
        const oThis = this;
        return oThis.environment.substring(0, 2);
    }

    get memcachedConfig(): {
        servers: string[];
        defaultTtl: number;
        consistentBehavior: '1';
    } {
        const memcacheServers = process.env.MEMCACHE_SERVERS! || '';
        return {
            servers: memcacheServers.split(','),
            defaultTtl: 36000,
            consistentBehavior: '1'
        };
    }

    get sesConfig(): {
        aws_access_key: string;
        aws_secret_key: string;
        region: string;
    } {
        return {
            aws_access_key: process.env.AWS_SES_ACCESS_KEY_Id!,
            aws_secret_key: process.env.AWS_SES_ACCESS_SECRET_KEY!,
            region: process.env.AWS_SES_REGION!
        };
    }

    get supportEmail(): string {
        return '<EMAIL>';
    }

    get noreplyEmail(): string {
        return '<EMAIL>';
    }

    get engineeringEmail(): string {
        return '<EMAIL>';
    }

    get dappDatabase(): string {
        return process.env.DAPP_PG_DAPP_DB;
    }

    get dappDatabaseReadOnlyUser(): string {
        return process.env.ANALYZER_READ_ONLY_USER;
    }

    get caCertificatePermissionFilePath(): string {
        return process.env.SSL_PEM_FILE_PATH!;
    }

    get clickhouseHost(): string {
        return process.env.CLICKHOUSE_HOST!;
    }

    get clickhouseUsername(): string {
        return process.env.CLICKHOUSE_USERNAME!;
    }

    get clickhousePassword(): string {
        return process.env.CLICKHOUSE_PASSWORD!;
    }

    get clickhouseMaxOpenConnection(): number {
        return Number(process.env.CLICKHOUSE_MAX_OPEN_CONNECTION!) || 10;
    }

    get clickhouseDatabaseCelo(): string {
        return process.env.CLICKHOUSE_DATABASE_CELO!;
    }

    get clickhouseDatabaseAstar(): string {
        return process.env.CLICKHOUSE_DATABASE_ASTAR!;
    }

    get clickhouseDatabaseManta(): string {
        return process.env.CLICKHOUSE_DATABASE_MANTA!;
    }

    get clickhouseDatabaseZeta(): string {
        return process.env.CLICKHOUSE_DATABASE_ZETA!;
    }

    get clickhouseDatabaseNordek(): string {
        return process.env.CLICKHOUSE_DATABASE_NORDEK!;
    }

    get clickhouseDatabaseBerachain(): string {
        return process.env.CLICKHOUSE_DATABASE_BERACHAIN!;
    }

    get clickhouseDatabaseStellarchain(): string {
        return process.env.CLICKHOUSE_DATABASE_STELLARCHAIN!;
    }

    get clickhouseDatabasePeaq(): string {
        return process.env.CLICKHOUSE_DATABASE_PEAQ!;
    }

    get clickhouseDatabaseVara(): string {
        return process.env.CLICKHOUSE_DATABASE_VARA!;
    }

    get clickhouseDatabaseDefault(): string {
        return process.env.CLICKHOUSE_DATABASE_DEFAULT!;
    }

    get clickhouseDatabaseHaqq(): string {
        return process.env.CLICKHOUSE_DATABASE_HAQQ!;
    }

    get clickhouseDatabaseMultiversx(): string {
        return process.env.CLICKHOUSE_DATABASE_MULTIVERSX!;
    }

    get clickhouseDatabaseCrossFi(): string {
        return process.env.CLICKHOUSE_DATABASE_CROSSFI!;
    }

    get clickhouseDatabasePriceOracle(): string {
        return process.env.CLICKHOUSE_DATABASE_PRICE_ORACLE! || 'price_oracle';
    }

    get clickhouseDatabaseOrbit(): string {
        return process.env.CLICKHOUSE_DATABASE_ORBIT!;
    }

    get clickhouseDatabaseMovementMainnet(): string {
        return process.env.CLICKHOUSE_DATABASE_MOVEMENT_MAINNET!;
    }

    get clickhouseDatabaseTonMiniApps(): string {
        return process.env.CLICKHOUSE_DATABASE_TON_MINI_APPS!;
    }

    get clickhouseDatabaseArbitrum(): string {
        return process.env.CLICKHOUSE_DATABASE_ARBITRUM!;
    }

    get clickhouseDatabaseGenesisInsights(): string {
        return process.env.CLICKHOUSE_DATABASE_GENESIS_INSIGHTS!;
    }

    get databaseChoicePostgres(): string {
        return 'pg'; // Postgres
    }

    get databaseChoiceClickhouse(): string {
        return 'ch'; // Clickhouse
    }

    get clickhouseReplacingMergeTree(): string {
        return 'ReplacingMergeTree';
    }

    get clickhouseMergeTree(): string {
        return 'MergeTree';
    }

    get protocolBatchSize(): number {
        return 250;
    }

    get protocolBatchSizeBerachain(): number {
        return 15;
    }

    get protocolBatchSizeHaqq(): number {
        return 500;
    }

    get StellarNetworkBatchSize(): number {
        return 40;
    }

    get assetsBatchSizeStellarchain(): number {
        return 1000;
    }

    get MultiversXBatchSize(): number {
        return 100;
    }

    get ArbitrumBatchSize(): number {
        return 300;
    }

    get transactionsBatchSizeMultiversX(): number {
        return 400;
    }

    get contractsBatchSizeMultiversX(): number {
        return 1000;
    }

    get subqueryIndexerAdapterBatchSize(): number {
        return 15000;
    }

    get intervalForGoodNetworkHealth(): number {
        return 3600;
    }

    get dataStorePostgres(): string {
        return 'postgres';
    }

    get dataStoreClickHouse(): string {
        return 'clickhouse';
    }

    get excludedDappIdsFromOptimization(): number[] {
        return [120519];
    }

    public get StellarDataLimit(): number {
        return 200;
    }

    public get graphAdapterMaxBatchNumber(): number {
        return 3;
    }

    get graphAdapterBatchSize(): number {
        return 1000;
    }

    get priceOracleMigrationBatchSize(): number {
        return 10000;
    }

    get historicalPricesConfig(): {
        schema: string;
        sourceTable: string;
    } {
        return {
            schema: 'price_oracle',
            sourceTable: 'historical_token_prices'
        };
    }

    get tokenPricesConfig(): {
        schema: string;
        sourceTable: string;
    } {
        return {
            schema: 'price_oracle',
            sourceTable: 'token_prices'
        };
    }

    get genesisMetadataConfig(): {
        schema: string;
        sourceTable: string;
    } {
        return {
            schema: 'genesis_insights',
            sourceTable: 'genesis_metadata'
        };
    }

    get acpAgentsMetadataConfig(): {
        schema: string;
        sourceTable: string;
    } {
        return {
            schema: 'genesis_insights',
            sourceTable: 'acp_agents_metadata'
        };
    }

    get duneApiKey(): string {
        // return 'B5iDhQOnYiwPpTusnwKwptdbVhnnTK0p'; // Abdul's Key
        return 'sre7xKK045pHOlznmfgKJYZ0Z2sScpne'; // Choubey's Key
    }

    get getQueryIds(): Record<string, number> {
        return {
            ledgerDataQueryId: 4365659,
            transactionDataQueryId: 4365888,
            operationDataQueryId: 4361328,
            operationByTypeQueryId: 4365712,
            uniqueAddressesPerDayQueryId: 4365912,
            ledgerDataByDayId: 4368443,
            transactionDataByDayId: 4371039,
            operationDataByDayId: 4371052,
            uniqueAddressesDataByDayId: 4371068,
            operationByTypeByDayId: 4371074
        };
    }

    get DevBundleSchemaIds(): Record<string, number> {
        return {
            NO_SCHEMA_REQUIRED: -1,
            VIRTUAL_AI_AGENTS: 0,
            VIRTUAL_AI_AGENTS_OLD: 1,
            VIRTUAL_AI_AGENTS_PROTOTYPE: 2,
            VIRTUAL_AI_AGENTS_MIGRATED: 3,
            VIRTUAL_TOKEN: 4,
            VIRTUALS_GENESIS_AGENTS: 5,
            VIRTUALS_ETHEREUM_AGENTS: 6
        }
    }

    get DevBundleSchemaNames(): Record<number, string> {
        return {
            0: 'virtual_ai_agents',
            1: 'virtual_ai_agents_old',
            2: 'virtual_ai_agents_prototype',
            3: 'virtual_ai_agents_migrated',
            4: 'virtual_token',
            5: 'virtuals_genesis_agents',
            6: 'virtuals_ethereum_agents'
        }
    }

    get DevBundleQueryTypes(): Record<string, number> {
        return {
            DEV_WALLET_BUNDLE_QUERY: 0, // DevBundleDataService
            SPREAD_WALLET_BUNDLE_QUERY: 1, // DevBundleDataService
            DEV_WALLET_FLOW_QUERY: 2, // DevBundleDataService
            SPREAD_WALLET_FLOW_QUERY: 3, // DevBundleDataService
            LEVEL_2_SPREAD_WALLET_FLOW_QUERY: 4, // DevBundleDataService
            WALLET_BALANCES_QUERY: 5, // DevBundleDataService
            TOKEN_BURN_AMOUNT_QUERY: 6, // TokenStatisticsService
            TOKEN_DEV_INITIAL_SUPPLY_QUERY: 7, // TokenStatisticsService
            TOKEN_FIRST_BUYERS_QUERY: 8, // FirstBuyersService
            DEV_INCOMING_TRANSACTIONS_QUERY: 9, // DevBundleDataService
            SPREAD_INCOMING_TRANSACTIONS_QUERY: 10, // DevBundleDataService
            LEVEL_2_SPREAD_INCOMING_TRANSACTIONS_QUERY: 11, // DevBundleDataService
            DEV_BUNDLE_TRANSACTIONS_QUERY: 12, // DevBundleTransactionsService
            DEV_WALLET_FUNDING_TRANSACTIONS_QUERY: 13, // DevWalletFundingTransactionsService
            AGENT_GRADUATION_ADDRESS_MAP_QUERY: 14, // AgentGraduationAddressMapService
            DEV_BUNDLE_TRANSACTIONS_PROCESSED_TOKEN_TRACKER: 15, // DevBundleTransactionsService

            GENESIS_CREATED_BLOCK_TRACKER_FOR_PARTICIPANTS_DATA: 16, // ParticipantsDataService,
            PARTICIPANT_CREATED_BLOCK_TRACKER_FOR_PARTICIPANTS_DATA: 17, // ParticipantsDataService,
            REFUND_CLAIMED_UUID_TRACKER_FOR_PARTICIPANTS_DATA: 18, // VirtualsRefundedDataService,
            INITIAL_TOKEN_ALLOCATION_UUID_TRACKER_FOR_PARTICIPANTS_DATA: 19, // InitialTokenAllocationDataService,
            USER_TOKEN_BALANCE_TRACKER_FOR_PARTICIPANTS_DATA: 20, // UserTokenBalanceDataService,
            USER_STAKING_DETAILS_TRACKER_FOR_PARTICIPANTS_DATA: 21, // UserStakingDetailsDataService
            V2_INITIAL_TOKEN_ALLOCATION_UUID_TRACKER_FOR_PARTICIPANTS_DATA: 22 // InitialTokenAllocationDataService
        };
    }

    get virtualAgentsInsightsSchema(): string {
        return `${process.env.PG_VIRTUAL_AGENT_INSIGHTS_SCHEMA!}`;
    }

    get allVirtualSchemas(): number[] {
        const oThis = this;
        return [
            oThis.DevBundleSchemaIds.VIRTUAL_AI_AGENTS,
            oThis.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD,
            oThis.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE,
            oThis.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_MIGRATED,
            oThis.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
            oThis.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS
        ]
    }

    get virtualSchemasWithoutMigrated(): number[] {
        const oThis = this;
        return [
            oThis.DevBundleSchemaIds.VIRTUAL_AI_AGENTS,
            oThis.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_OLD,
            oThis.DevBundleSchemaIds.VIRTUAL_AI_AGENTS_PROTOTYPE,
            oThis.DevBundleSchemaIds.VIRTUALS_GENESIS_AGENTS,
            oThis.DevBundleSchemaIds.VIRTUALS_ETHEREUM_AGENTS
        ]
    }
}

export default new Constant();
