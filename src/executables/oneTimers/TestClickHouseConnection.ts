import Logger from '../../lib/Logger';
import ClickhouseClient from '../../lib/dataStores/destination/ClickhouseClient';
import Constant from '../../config/Constant';
import { ClickHouseConfig } from '../../lib/Types';

interface DatabaseRow {
    name: string;
}

interface ColumnInfo {
    name: string;
    type: string;
}

interface CountResult {
    count: string;
}

interface TokenRecord {
    token_id: string;
    base_address: string | null;
}

class TestClickHouseConnection {
    private chConfig: ClickHouseConfig;
    private clickhouseClient: ClickhouseClient;
    private priceOracleSchema: string = 'price_oracle';
    private tokenPricesTable: string = 'token_prices';

    constructor() {
        this.chConfig = {
            host: Constant.clickhouseHost,
            username: Constant.clickhouseUsername,
            password: Constant.clickhousePassword,
            database: Constant.clickhouseDatabasePriceOracle,
            max_open_connections: Constant.clickhouseMaxOpenConnection,
        };
        this.clickhouseClient = new ClickhouseClient(this.chConfig);
    }

    public async perform(): Promise<void> {
        try {
            Logger.info(`TestClickHouseConnection::perform::Starting ClickHouse connection test`);
            Logger.info(`TestClickHouseConnection::perform::Host: ${this.chConfig.host}`);
            Logger.info(`TestClickHouseConnection::perform::Database: ${this.chConfig.database}`);
            Logger.info(`TestClickHouseConnection::perform::Username: ${this.chConfig.username}`);

            const client = this.clickhouseClient.getClient();

            // Test 1: Basic connection
            Logger.info(`\n=== TEST 1: Basic Connection ===`);
            const pingResult = await client.ping();
            Logger.info(`Ping result: ${pingResult}`);

            // Test 2: Check database
            Logger.info(`\n=== TEST 2: Database Check ===`);
            const dbQuery = `SELECT name FROM system.databases WHERE name = '${this.chConfig.database}'`;
            Logger.info(`Query: ${dbQuery}`);
            const dbResult = await client.query({
                query: dbQuery,
                format: 'JSONEachRow'
            });
            const dbRows: DatabaseRow[] = await dbResult.json();
            Logger.info(`Database exists: ${dbRows.length > 0}`);

            // Test 3: Check table
            Logger.info(`\n=== TEST 3: Table Check ===`);
            const tableQuery = `SELECT name FROM system.tables WHERE database = '${this.chConfig.database}' AND name = '${this.tokenPricesTable}'`;
            Logger.info(`Query: ${tableQuery}`);
            const tableResult = await client.query({
                query: tableQuery,
                format: 'JSONEachRow'
            });
            const tableRows: DatabaseRow[] = await tableResult.json();
            Logger.info(`Table exists: ${tableRows.length > 0}`);

            if (tableRows.length === 0) {
                Logger.warn(`Table '${this.chConfig.database}.${this.tokenPricesTable}' does not exist!`);
                return;
            }

            // Test 4: Check columns
            Logger.info(`\n=== TEST 4: Column Check ===`);
            const columnsQuery = `
                SELECT name, type 
                FROM system.columns 
                WHERE database = '${this.chConfig.database}' 
                AND table = '${this.tokenPricesTable}'
                ORDER BY name
            `;
            Logger.info(`Query: ${columnsQuery}`);
            const columnsResult = await client.query({
                query: columnsQuery,
                format: 'JSONEachRow'
            });
            const columns: ColumnInfo[] = await columnsResult.json();
            Logger.info(`Found ${columns.length} columns:`);
            columns.forEach((col: ColumnInfo) => {
                Logger.info(`  - ${col.name}: ${col.type}`);
            });

            const hasBaseAddress = columns.some((col: ColumnInfo) => col.name === 'base_address');
            const hasTokenId = columns.some((col: ColumnInfo) => col.name === 'token_id');
            Logger.info(`Has token_id column: ${hasTokenId}`);
            Logger.info(`Has base_address column: ${hasBaseAddress}`);

            // Test 5: Count records
            Logger.info(`\n=== TEST 5: Record Count ===`);
            const countQuery = `SELECT COUNT(*) as count FROM ${this.chConfig.database}.${this.tokenPricesTable}`;
            Logger.info(`Query: ${countQuery}`);
            const countResult = await client.query({
                query: countQuery,
                format: 'JSONEachRow'
            });
            const countRows: CountResult[] = await countResult.json();
            Logger.info(`Total records: ${countRows[0]?.count || 0}`);

            // Test 6: Sample data
            Logger.info(`\n=== TEST 6: Sample Data ===`);
            const sampleQuery = `
                SELECT token_id, ${hasBaseAddress ? 'base_address' : 'NULL as base_address'}
                FROM ${this.chConfig.database}.${this.tokenPricesTable} 
                LIMIT 5
            `;
            Logger.info(`Query: ${sampleQuery}`);
            const sampleResult = await client.query({
                query: sampleQuery,
                format: 'JSONEachRow'
            });
            const sampleRows: TokenRecord[] = await sampleResult.json();
            Logger.info(`Sample records:`);
            sampleRows.forEach((row: TokenRecord, index: number) => {
                Logger.info(`  ${index + 1}. token_id: ${row.token_id}, base_address: ${row.base_address || 'NULL'}`);
            });

            // Test 7: Test simple IN query
            if (sampleRows.length > 0) {
                Logger.info(`\n=== TEST 7: Simple IN Query ===`);
                const firstTokenId = sampleRows[0].token_id;
                const escapedTokenId = firstTokenId.replace(/\\/g, '\\\\').replace(/'/g, "\\'");
                const inQuery = `
                    SELECT token_id, ${hasBaseAddress ? 'base_address' : 'NULL as base_address'}
                    FROM ${this.chConfig.database}.${this.tokenPricesTable} 
                    WHERE token_id IN ('${escapedTokenId}')
                `;
                Logger.info(`Query: ${inQuery}`);
                const inResult = await client.query({
                    query: inQuery,
                    format: 'JSONEachRow'
                });
                const inRows: TokenRecord[] = await inResult.json();
                Logger.info(`IN query result: ${inRows.length} records found`);
            }

            Logger.info(`\n✅ All tests completed successfully!`);

        } catch (error) {
            Logger.error(`TestClickHouseConnection::perform::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }
}

// Execute the test
async function main() {
    try {
        const test = new TestClickHouseConnection();
        await test.perform();
        process.exit(0);
    } catch (error) {
        Logger.error(`Main::Error: ${JSON.stringify(error)}`);
        process.exit(1);
    }
}

main();
