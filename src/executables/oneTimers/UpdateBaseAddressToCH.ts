import Logger from '../../lib/Logger';
import Postgres from '../../lib/dataStores/destination/Postgres';
import ClickhouseClient from '../../lib/dataStores/destination/ClickhouseClient';
import Constant from '../../config/Constant';
import { ClickHouseConfig } from '../../lib/Types';
import { QueryTypes } from 'sequelize';

// Simple command line argument parsing
const args = process.argv.slice(2);
const getBatchSize = (): number => {
    const batchIndex = args.findIndex(arg => arg === '--batchSize' || arg === '-b');
    if (batchIndex !== -1 && args[batchIndex + 1]) {
        return parseInt(args[batchIndex + 1]) || 1000;
    }
    return 1000;
};

const isDryRun = (): boolean => {
    return args.includes('--dryRun') || args.includes('-d');
};

const options = {
    batchSize: getBatchSize(),
    dryRun: isDryRun()
};

interface TokenRecord {
    token_id: string;
    base_address: string | null;
}

class UpdateBaseAddressToCH {
    private batchSize: number;
    private dryRun: boolean;
    private postgres: Postgres;
    private chConfig: ClickHouseConfig;
    private clickhouseClient: ClickhouseClient;
    private priceOracleSchema: string = 'price_oracle';
    private tokenPricesTable: string = 'token_prices';

    constructor() {
        this.batchSize = options.batchSize;
        this.dryRun = options.dryRun;
        this.postgres = Postgres.getClient(Constant.dappDatabaseConnectionUrl);
        this.chConfig = {
            host: Constant.clickhouseHost,
            username: Constant.clickhouseUsername,
            password: Constant.clickhousePassword,
            database: Constant.clickhouseDatabasePriceOracle,
            max_open_connections: Constant.clickhouseMaxOpenConnection,
        };
        this.clickhouseClient = new ClickhouseClient(this.chConfig);
    }

    public async perform(): Promise<void> {
        try {
            Logger.info(`UpdateBaseAddressToCH::perform::Starting base address migration ${this.dryRun ? '(DRY RUN)' : ''}`);
            Logger.info(`UpdateBaseAddressToCH::perform::Batch size: ${this.batchSize}`);

            // First, ensure the ClickHouse table has the base_address column
            await this.ensureBaseAddressColumn();

            // Get total count for progress tracking
            const totalCount = await this.getTotalTokensWithBaseAddress();
            Logger.info(`UpdateBaseAddressToCH::perform::Total tokens with base address: ${totalCount}`);

            if (totalCount === 0) {
                Logger.info(`UpdateBaseAddressToCH::perform::No tokens with base address found. Exiting.`);
                return;
            }

            let offset = 0;
            let processedCount = 0;
            let updatedCount = 0;

            while (offset < totalCount) {
                const tokens = await this.getTokensWithBaseAddress(offset, this.batchSize);
                
                if (tokens.length === 0) {
                    break;
                }

                Logger.info(`UpdateBaseAddressToCH::perform::Processing batch ${Math.floor(offset / this.batchSize) + 1}, records ${offset + 1}-${offset + tokens.length} of ${totalCount}`);

                const batchUpdatedCount = await this.updateTokensInClickHouse(tokens);
                
                processedCount += tokens.length;
                updatedCount += batchUpdatedCount;
                offset += this.batchSize;

                Logger.info(`UpdateBaseAddressToCH::perform::Batch completed. Updated ${batchUpdatedCount} records. Total processed: ${processedCount}, Total updated: ${updatedCount}`);

                // Add a small delay to avoid overwhelming the databases
                await this.sleep(100);
            }

            Logger.info(`UpdateBaseAddressToCH::perform::Migration completed successfully!`);
            Logger.info(`UpdateBaseAddressToCH::perform::Total processed: ${processedCount}, Total updated: ${updatedCount}`);

        } catch (error) {
            Logger.error(`UpdateBaseAddressToCH::perform::Error during migration: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async ensureBaseAddressColumn(): Promise<void> {
        try {
            const client = this.clickhouseClient.getClient();

            // Check if base_address column exists
            const checkColumnQuery = `
                SELECT name
                FROM system.columns
                WHERE database = '${this.chConfig.database}'
                AND table = '${this.tokenPricesTable}'
                AND name IN ('base_address', 'solana_address')
            `;

            const result = await client.query({
                query: checkColumnQuery,
                format: 'JSONEachRow'
            });

            const rows = await result.json();
            const existingColumns = rows.map((row: any) => row.name);

            // Add base_address column if it doesn't exist
            if (!existingColumns.includes('base_address')) {
                Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::Adding base_address column to ClickHouse table`);

                if (!this.dryRun) {
                    const addColumnQuery = `
                        ALTER TABLE ${this.chConfig.database}.${this.tokenPricesTable}
                        ADD COLUMN IF NOT EXISTS base_address Nullable(String)
                    `;

                    await client.command({ query: addColumnQuery });
                    Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::base_address column added successfully`);
                } else {
                    Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::DRY RUN - Would add base_address column`);
                }
            } else {
                Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::base_address column already exists`);
            }

            // Add solana_address column if it doesn't exist
            if (!existingColumns.includes('solana_address')) {
                Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::Adding solana_address column to ClickHouse table`);

                if (!this.dryRun) {
                    const addColumnQuery = `
                        ALTER TABLE ${this.chConfig.database}.${this.tokenPricesTable}
                        ADD COLUMN IF NOT EXISTS solana_address Nullable(String)
                    `;

                    await client.command({ query: addColumnQuery });
                    Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::solana_address column added successfully`);
                } else {
                    Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::DRY RUN - Would add solana_address column`);
                }
            } else {
                Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::solana_address column already exists`);
            }

        } catch (error) {
            Logger.error(`UpdateBaseAddressToCH::ensureBaseAddressColumn::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getTotalTokensWithBaseAddress(): Promise<number> {
        try {
            const query = `
                SELECT COUNT(*) as count
                FROM ${this.priceOracleSchema}.${this.tokenPricesTable}
                WHERE base_address IS NOT NULL AND base_address != ''
            `;

            const result = await this.postgres.sequelize.query(query, { type: QueryTypes.SELECT });
            return parseInt((result[0] as any).count);

        } catch (error) {
            Logger.error(`UpdateBaseAddressToCH::getTotalTokensWithBaseAddress::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getTokensWithBaseAddress(offset: number, limit: number): Promise<TokenRecord[]> {
        try {
            const query = `
                SELECT token_id, base_address
                FROM ${this.priceOracleSchema}.${this.tokenPricesTable}
                WHERE base_address IS NOT NULL AND base_address != ''
                ORDER BY token_id
                LIMIT :limit OFFSET :offset
            `;

            const result = await this.postgres.sequelize.query(query, {
                type: QueryTypes.SELECT,
                replacements: { limit, offset }
            });
            return (result as any[]).map((row: any) => ({
                token_id: row.token_id,
                base_address: row.base_address
            }));

        } catch (error) {
            Logger.error(`UpdateBaseAddressToCH::getTokensWithBaseAddress::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async updateTokensInClickHouse(tokens: TokenRecord[]): Promise<number> {
        try {
            if (this.dryRun) {
                Logger.info(`UpdateBaseAddressToCH::updateTokensInClickHouse::DRY RUN - Would update ${tokens.length} tokens`);
                return tokens.length;
            }

            const client = this.clickhouseClient.getClient();
            let updatedCount = 0;

            // ClickHouse doesn't support traditional UPDATE statements
            // We need to use ALTER TABLE UPDATE for mutations
            for (const token of tokens) {
                // Escape single quotes in the base_address value
                const escapedBaseAddress = token.base_address?.replace(/'/g, "\\'") || '';

                const updateQuery = `
                    ALTER TABLE ${this.chConfig.database}.${this.tokenPricesTable}
                    UPDATE base_address = '${escapedBaseAddress}'
                    WHERE token_id = '${token.token_id}'
                `;

                try {
                    await client.command({ query: updateQuery });
                    updatedCount++;
                    Logger.debug(`UpdateBaseAddressToCH::updateTokensInClickHouse::Updated token ${token.token_id} with base_address: ${token.base_address}`);
                } catch (error) {
                    Logger.error(`UpdateBaseAddressToCH::updateTokensInClickHouse::Error updating token ${token.token_id}: ${JSON.stringify(error)}`);
                    // Continue with other tokens even if one fails
                }
            }

            return updatedCount;

        } catch (error) {
            Logger.error(`UpdateBaseAddressToCH::updateTokensInClickHouse::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Execute the migration
async function main() {
    try {
        const migration = new UpdateBaseAddressToCH();
        await migration.perform();
        process.exit(0);
    } catch (error) {
        Logger.error(`Main::Error: ${JSON.stringify(error)}`);
        process.exit(1);
    }
}

main();
