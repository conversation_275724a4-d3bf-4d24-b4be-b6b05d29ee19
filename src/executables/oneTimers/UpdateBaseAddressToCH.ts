import Logger from '../../lib/Logger';
import Postgres from '../../lib/dataStores/destination/Postgres';
import ClickhouseClient from '../../lib/dataStores/destination/ClickhouseClient';
import Constant from '../../config/Constant';
import { ClickHouseConfig } from '../../lib/Types';
import { QueryTypes } from 'sequelize';

// Simple command line argument parsing
const args = process.argv.slice(2);
const getBatchSize = (): number => {
    const batchIndex = args.findIndex(arg => arg === '--batchSize' || arg === '-b');
    if (batchIndex !== -1 && args[batchIndex + 1]) {
        return parseInt(args[batchIndex + 1]) || 1000;
    }
    return 1000;
};

const isDryRun = (): boolean => {
    return args.includes('--dryRun') || args.includes('-d');
};

const getStartOffset = (): number => {
    const offsetIndex = args.findIndex(arg => arg === '--startOffset' || arg === '-s');
    if (offsetIndex !== -1 && args[offsetIndex + 1]) {
        return parseInt(args[offsetIndex + 1]) || 0;
    }
    return 0;
};

const getMaxRecords = (): number => {
    const maxIndex = args.findIndex(arg => arg === '--maxRecords' || arg === '-m');
    if (maxIndex !== -1 && args[maxIndex + 1]) {
        return parseInt(args[maxIndex + 1]) || 0;
    }
    return 0; // 0 means no limit
};

const options = {
    batchSize: getBatchSize(),
    dryRun: isDryRun(),
    startOffset: getStartOffset(),
    maxRecords: getMaxRecords()
};

interface TokenRecord {
    token_id: string;
    base_address: string | null;
}

class UpdateBaseAddressToCH {
    private batchSize: number;
    private dryRun: boolean;
    private startOffset: number;
    private maxRecords: number;
    private postgres: Postgres;
    private chConfig: ClickHouseConfig;
    private clickhouseClient: ClickhouseClient;
    private priceOracleSchema: string = 'price_oracle';
    private tokenPricesTable: string = 'token_prices';

    constructor() {
        this.batchSize = options.batchSize;
        this.dryRun = options.dryRun;
        this.startOffset = options.startOffset;
        this.maxRecords = options.maxRecords;
        this.postgres = Postgres.getClient(Constant.dappDatabaseConnectionUrl);
        this.chConfig = {
            host: Constant.clickhouseHost,
            username: Constant.clickhouseUsername,
            password: Constant.clickhousePassword,
            database: Constant.clickhouseDatabasePriceOracle,
            max_open_connections: Constant.clickhouseMaxOpenConnection,
        };
        this.clickhouseClient = new ClickhouseClient(this.chConfig);
    }

    public async perform(): Promise<void> {
        try {
            Logger.info(`UpdateBaseAddressToCH::perform::Starting base address migration ${this.dryRun ? '(DRY RUN)' : ''}`);
            Logger.info(`UpdateBaseAddressToCH::perform::Batch size: ${this.batchSize}`);
            Logger.info(`UpdateBaseAddressToCH::perform::Start offset: ${this.startOffset}`);
            if (this.maxRecords > 0) {
                Logger.info(`UpdateBaseAddressToCH::perform::Max records to process: ${this.maxRecords}`);
            }

            // First, ensure the ClickHouse table has the base_address column
            await this.ensureBaseAddressColumn();

            // Get total count for progress tracking
            const totalCount = await this.getTotalTokensWithBaseAddress();
            Logger.info(`UpdateBaseAddressToCH::perform::Total tokens with base address: ${totalCount}`);

            if (totalCount === 0) {
                Logger.info(`UpdateBaseAddressToCH::perform::No tokens with base address found. Exiting.`);
                return;
            }

            // Calculate effective range
            let offset = this.startOffset;
            let processedCount = 0;
            let updatedCount = 0;
            let effectiveTotal = totalCount;

            if (this.maxRecords > 0) {
                effectiveTotal = Math.min(totalCount - this.startOffset, this.maxRecords);
                Logger.info(`UpdateBaseAddressToCH::perform::Will process ${effectiveTotal} records starting from offset ${this.startOffset}`);
            }

            const startTime = Date.now();

            while (offset < totalCount && (this.maxRecords === 0 || processedCount < this.maxRecords)) {
                const remainingRecords = this.maxRecords > 0 ? this.maxRecords - processedCount : this.batchSize;
                const currentBatchSize = Math.min(this.batchSize, remainingRecords);

                const tokens = await this.getTokensWithBaseAddress(offset, currentBatchSize);

                if (tokens.length === 0) {
                    break;
                }

                const batchNumber = Math.floor((offset - this.startOffset) / this.batchSize) + 1;
                Logger.info(`UpdateBaseAddressToCH::perform::Processing batch ${batchNumber}, records ${offset + 1}-${offset + tokens.length} of ${totalCount}`);

                const batchUpdatedCount = await this.updateTokensInClickHouse(tokens);

                processedCount += tokens.length;
                updatedCount += batchUpdatedCount;
                offset += tokens.length;

                // Calculate progress and ETA
                const elapsed = Date.now() - startTime;
                const rate = processedCount / (elapsed / 1000); // records per second
                const remaining = (this.maxRecords > 0 ? this.maxRecords : effectiveTotal) - processedCount;
                const eta = remaining > 0 ? Math.round(remaining / rate) : 0;

                Logger.info(`UpdateBaseAddressToCH::perform::Batch completed. Updated ${batchUpdatedCount} records. Total processed: ${processedCount}, Total updated: ${updatedCount}`);
                Logger.info(`UpdateBaseAddressToCH::perform::Progress: ${((processedCount / (this.maxRecords > 0 ? this.maxRecords : effectiveTotal)) * 100).toFixed(2)}%, Rate: ${rate.toFixed(2)} records/sec, ETA: ${eta}s`);

                // Add a small delay to avoid overwhelming the databases
                await this.sleep(100);
            }

            const totalElapsed = (Date.now() - startTime) / 1000;
            Logger.info(`UpdateBaseAddressToCH::perform::Migration completed successfully!`);
            Logger.info(`UpdateBaseAddressToCH::perform::Total processed: ${processedCount}, Total updated: ${updatedCount}, Time elapsed: ${totalElapsed.toFixed(2)}s`);

        } catch (error) {
            Logger.error(`UpdateBaseAddressToCH::perform::Error during migration: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async ensureBaseAddressColumn(): Promise<void> {
        try {
            const client = this.clickhouseClient.getClient();

            // Check if base_address column exists
            const checkColumnQuery = `
                SELECT name
                FROM system.columns
                WHERE database = '${this.chConfig.database}'
                  AND table = '${this.tokenPricesTable}'
                  AND name IN ('base_address'
                    , 'solana_address')
            `;

            const result: any = await client.query({
                query: checkColumnQuery,
                format: 'JSONEachRow'
            });

            const rows: any = await result.json();
            const existingColumns = rows.map((row: any) => row.name);

            // Add base_address column if it doesn't exist
            if (!existingColumns.includes('base_address')) {
                Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::Adding base_address column to ClickHouse table`);

                if (!this.dryRun) {
                    const addColumnQuery = `
                        ALTER TABLE ${this.chConfig.database}.${this.tokenPricesTable}
                            ADD COLUMN IF NOT EXISTS base_address Nullable (String)
                    `;

                    await client.command({ query: addColumnQuery });
                    Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::base_address column added successfully`);
                } else {
                    Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::DRY RUN - Would add base_address column`);
                }
            } else {
                Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::base_address column already exists`);
            }

            // Add solana_address column if it doesn't exist
            if (!existingColumns.includes('solana_address')) {
                Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::Adding solana_address column to ClickHouse table`);

                if (!this.dryRun) {
                    const addColumnQuery = `
                        ALTER TABLE ${this.chConfig.database}.${this.tokenPricesTable}
                            ADD COLUMN IF NOT EXISTS solana_address Nullable (String)
                    `;

                    await client.command({ query: addColumnQuery });
                    Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::solana_address column added successfully`);
                } else {
                    Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::DRY RUN - Would add solana_address column`);
                }
            } else {
                Logger.info(`UpdateBaseAddressToCH::ensureBaseAddressColumn::solana_address column already exists`);
            }

        } catch (error) {
            Logger.error(`UpdateBaseAddressToCH::ensureBaseAddressColumn::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getTotalTokensWithBaseAddress(): Promise<number> {
        try {
            const query = `
                SELECT COUNT(*) as count
                FROM ${this.priceOracleSchema}.${this.tokenPricesTable}
                WHERE base_address IS NOT NULL AND base_address != ''
            `;

            const result = await this.postgres.sequelize.query(query, { type: QueryTypes.SELECT });
            return parseInt((result[0] as any).count);

        } catch (error) {
            Logger.error(`UpdateBaseAddressToCH::getTotalTokensWithBaseAddress::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getTokensWithBaseAddress(offset: number, limit: number): Promise<TokenRecord[]> {
        try {
            const query = `
                SELECT token_id, base_address
                FROM ${this.priceOracleSchema}.${this.tokenPricesTable}
                WHERE base_address IS NOT NULL
                  AND base_address != ''
                ORDER BY token_id
                    LIMIT :limit
                OFFSET :offset
            `;

            const result = await this.postgres.sequelize.query(query, {
                type: QueryTypes.SELECT,
                replacements: { limit, offset }
            });
            return (result as any[]).map((row: any) => ({
                token_id: row.token_id,
                base_address: row.base_address
            }));

        } catch (error) {
            Logger.error(`UpdateBaseAddressToCH::getTokensWithBaseAddress::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async updateTokensInClickHouse(tokens: TokenRecord[]): Promise<number> {
        try {
            if (this.dryRun) {
                Logger.info(`UpdateBaseAddressToCH::updateTokensInClickHouse::DRY RUN - Would update ${tokens.length} tokens`);
                return tokens.length;
            }

            const client = this.clickhouseClient.getClient();
            let updatedCount = 0;
            let failedCount = 0;

            // Process tokens in smaller sub-batches to avoid overwhelming ClickHouse
            const subBatchSize = 50;
            for (let i = 0; i < tokens.length; i += subBatchSize) {
                const subBatch = tokens.slice(i, i + subBatchSize);
                Logger.debug(`UpdateBaseAddressToCH::updateTokensInClickHouse::Processing sub-batch ${Math.floor(i / subBatchSize) + 1}, tokens ${i + 1}-${Math.min(i + subBatchSize, tokens.length)}`);

                for (const token of subBatch) {
                    try {
                        await this.updateSingleToken(client, token);
                        updatedCount++;
                    } catch (error) {
                        failedCount++;
                        Logger.error(`UpdateBaseAddressToCH::updateTokensInClickHouse::Error updating token ${token.token_id}: ${JSON.stringify(error)}`);
                        // Continue with other tokens even if one fails
                    }
                }

                // Small delay between sub-batches to avoid overwhelming ClickHouse
                if (i + subBatchSize < tokens.length) {
                    await this.sleep(50);
                }
            }

            if (failedCount > 0) {
                Logger.warn(`UpdateBaseAddressToCH::updateTokensInClickHouse::Completed with ${failedCount} failures out of ${tokens.length} tokens`);
            }

            return updatedCount;

        } catch (error) {
            Logger.error(`UpdateBaseAddressToCH::updateTokensInClickHouse::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async updateSingleToken(client: any, token: TokenRecord): Promise<void> {
        // Escape single quotes and backslashes in the base_address value
        const escapedBaseAddress = token.base_address
            ?.replace(/\\/g, '\\\\')  // Escape backslashes first
            ?.replace(/'/g, "\\'")    // Then escape single quotes
            || '';

        // Also escape the token_id to prevent SQL injection
        const escapedTokenId = token.token_id.replace(/'/g, "\\'");

        const updateQuery = `
            ALTER TABLE ${this.chConfig.database}.${this.tokenPricesTable}
            UPDATE base_address = '${escapedBaseAddress}'
            WHERE token_id = '${escapedTokenId}'
        `;

        await client.command({ query: updateQuery });
        Logger.debug(`UpdateBaseAddressToCH::updateSingleToken::Updated token ${token.token_id} with base_address: ${token.base_address}`);
    }

    private async sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Execute the migration
async function main() {
    try {
        const migration = new UpdateBaseAddressToCH();
        await migration.perform();
        process.exit(0);
    } catch (error) {
        Logger.error(`Main::Error: ${JSON.stringify(error)}`);
        process.exit(1);
    }
}

main();
