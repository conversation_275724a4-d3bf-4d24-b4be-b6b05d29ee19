import Logger from '../../lib/Logger';
import Postgres from '../../lib/dataStores/destination/Postgres';
import ClickhouseClient from '../../lib/dataStores/destination/ClickhouseClient';
import Constant from '../../config/Constant';
import { ClickHouseConfig } from '../../lib/Types';
import { QueryTypes } from 'sequelize';

// Simple command line argument parsing
const args = process.argv.slice(2);
const getLimit = (): number => {
    const limitIndex = args.findIndex(arg => arg === '--limit' || arg === '-l');
    if (limitIndex !== -1 && args[limitIndex + 1]) {
        return parseInt(args[limitIndex + 1]) || 1000;
    }
    return 1000;
};

const getAllTokens = (): boolean => {
    return args.includes('--all') || args.includes('-a');
};

const options = {
    limit: getLimit(),
    all: getAllTokens()
};

interface ValidationResult {
    token_id: string;
    pg_base_address: string | null;
    ch_base_address: string | null;
    matches: boolean;
}

class ValidateBaseAddressMigration {
    private limit: number;
    private validateAll: boolean;
    private postgres: Postgres;
    private chConfig: ClickHouseConfig;
    private clickhouseClient: ClickhouseClient;
    private priceOracleSchema: string = 'price_oracle';
    private tokenPricesTable: string = 'token_prices';

    constructor() {
        this.limit = options.limit;
        this.validateAll = options.all;
        this.postgres = Postgres.getClient(Constant.dappDatabaseConnectionUrl);
        this.chConfig = {
            host: Constant.clickhouseHost,
            username: Constant.clickhouseUsername,
            password: Constant.clickhousePassword,
            database: Constant.clickhouseDatabasePriceOracle,
            max_open_connections: Constant.clickhouseMaxOpenConnection,
        };
        this.clickhouseClient = new ClickhouseClient(this.chConfig);
    }

    public async perform(): Promise<void> {
        try {
            const validationMode = this.validateAll ? 'ALL tokens' : `${this.limit} tokens`;
            Logger.info(`ValidateBaseAddressMigration::perform::Starting validation for ${validationMode}`);

            // Test ClickHouse connection and table structure first
            await this.testClickHouseConnection();

            if (this.validateAll) {
                await this.performBatchValidation();
            } else {
                await this.performSampleValidation();
            }

        } catch (error) {
            Logger.error(`ValidateBaseAddressMigration::perform::Error during validation: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async testClickHouseConnection(): Promise<void> {
        try {
            const client = this.clickhouseClient.getClient();

            // Test basic connection
            Logger.info(`ValidateBaseAddressMigration::testClickHouseConnection::Testing ClickHouse connection...`);

            // Check if database exists
            const dbQuery = `SELECT name FROM system.databases WHERE name = '${this.chConfig.database}'`;
            const dbResult = await client.query({
                query: dbQuery,
                format: 'JSONEachRow'
            });
            const dbRows = await dbResult.json();

            if (dbRows.length === 0) {
                throw new Error(`Database '${this.chConfig.database}' does not exist`);
            }

            // Check if table exists
            const tableQuery = `SELECT name FROM system.tables WHERE database = '${this.chConfig.database}' AND name = '${this.tokenPricesTable}'`;
            const tableResult = await client.query({
                query: tableQuery,
                format: 'JSONEachRow'
            });
            const tableRows = await tableResult.json();

            if (tableRows.length === 0) {
                throw new Error(`Table '${this.chConfig.database}.${this.tokenPricesTable}' does not exist`);
            }

            // Check table structure
            const columnsQuery = `
                SELECT name, type
                FROM system.columns
                WHERE database = '${this.chConfig.database}'
                AND table = '${this.tokenPricesTable}'
                ORDER BY name
            `;
            const columnsResult = await client.query({
                query: columnsQuery,
                format: 'JSONEachRow'
            });
            const columns = await columnsResult.json();

            Logger.info(`ValidateBaseAddressMigration::testClickHouseConnection::Found ${columns.length} columns in table`);
            const hasBaseAddress = columns.some((col: any) => col.name === 'base_address');
            const hasTokenId = columns.some((col: any) => col.name === 'token_id');

            if (!hasTokenId) {
                throw new Error(`Table '${this.chConfig.database}.${this.tokenPricesTable}' does not have 'token_id' column`);
            }

            if (!hasBaseAddress) {
                Logger.warn(`ValidateBaseAddressMigration::testClickHouseConnection::Table does not have 'base_address' column yet`);
            } else {
                Logger.info(`ValidateBaseAddressMigration::testClickHouseConnection::Table has 'base_address' column`);
            }

            Logger.info(`ValidateBaseAddressMigration::testClickHouseConnection::ClickHouse connection test passed`);

        } catch (error) {
            Logger.error(`ValidateBaseAddressMigration::testClickHouseConnection::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async performSampleValidation(): Promise<void> {
        // Get sample tokens with base addresses from PostgreSQL
        const pgTokens = await this.getTokensFromPostgreSQL();
        Logger.info(`ValidateBaseAddressMigration::performSampleValidation::Found ${pgTokens.length} tokens in PostgreSQL`);

        if (pgTokens.length === 0) {
            Logger.info(`ValidateBaseAddressMigration::performSampleValidation::No tokens found in PostgreSQL. Exiting.`);
            return;
        }

        // Get corresponding tokens from ClickHouse
        const chTokens = await this.getTokensFromClickHouse(pgTokens.map(t => t.token_id));
        Logger.info(`ValidateBaseAddressMigration::performSampleValidation::Found ${chTokens.length} tokens in ClickHouse`);

        // Compare the data
        const validationResults = this.compareTokenData(pgTokens, chTokens);

        // Report results
        this.reportValidationResults(validationResults);
    }

    private async performBatchValidation(): Promise<void> {
        const batchSize = 1000;
        let offset = 0;
        let totalProcessed = 0;
        let totalMatching = 0;
        let totalMismatched = 0;
        let allMismatches: ValidationResult[] = [];

        // Get total count first
        const totalCount = await this.getTotalTokensWithBaseAddress();
        Logger.info(`ValidateBaseAddressMigration::performBatchValidation::Total tokens with base address: ${totalCount}`);

        if (totalCount === 0) {
            Logger.info(`ValidateBaseAddressMigration::performBatchValidation::No tokens found in PostgreSQL. Exiting.`);
            return;
        }

        while (offset < totalCount) {
            Logger.info(`ValidateBaseAddressMigration::performBatchValidation::Processing batch ${Math.floor(offset / batchSize) + 1}, records ${offset + 1}-${Math.min(offset + batchSize, totalCount)} of ${totalCount}`);

            // Get batch of tokens from PostgreSQL
            const pgTokens = await this.getTokensFromPostgreSQLBatch(offset, batchSize);

            if (pgTokens.length === 0) {
                break;
            }

            // Get corresponding tokens from ClickHouse
            const chTokens = await this.getTokensFromClickHouse(pgTokens.map(t => t.token_id));

            // Compare the data
            const validationResults = this.compareTokenData(pgTokens, chTokens);

            // Accumulate results
            const batchMatching = validationResults.filter(r => r.matches).length;
            const batchMismatched = validationResults.filter(r => !r.matches);

            totalProcessed += validationResults.length;
            totalMatching += batchMatching;
            totalMismatched += batchMismatched.length;
            allMismatches.push(...batchMismatched);

            Logger.info(`ValidateBaseAddressMigration::performBatchValidation::Batch completed. Matching: ${batchMatching}, Mismatched: ${batchMismatched.length}`);

            offset += batchSize;

            // Add a small delay to avoid overwhelming the databases
            await this.sleep(100);
        }

        // Report final results
        this.reportBatchValidationResults(totalProcessed, totalMatching, totalMismatched, allMismatches);
    }

    private async getTotalTokensWithBaseAddress(): Promise<number> {
        try {
            const query = `
                SELECT COUNT(*) as count
                FROM ${this.priceOracleSchema}.${this.tokenPricesTable}
                WHERE base_address IS NOT NULL AND base_address != ''
            `;

            const result = await this.postgres.sequelize.query(query, { type: QueryTypes.SELECT });
            return parseInt((result[0] as any).count);

        } catch (error) {
            Logger.error(`ValidateBaseAddressMigration::getTotalTokensWithBaseAddress::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getTokensFromPostgreSQL(): Promise<any[]> {
        try {
            const query = `
                SELECT token_id, base_address
                FROM ${this.priceOracleSchema}.${this.tokenPricesTable}
                WHERE base_address IS NOT NULL AND base_address != ''
                ORDER BY token_id
                LIMIT :limit
            `;

            const result = await this.postgres.sequelize.query(query, {
                type: QueryTypes.SELECT,
                replacements: { limit: this.limit }
            });

            return result as any[];

        } catch (error) {
            Logger.error(`ValidateBaseAddressMigration::getTokensFromPostgreSQL::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getTokensFromPostgreSQLBatch(offset: number, limit: number): Promise<any[]> {
        try {
            const query = `
                SELECT token_id, base_address
                FROM ${this.priceOracleSchema}.${this.tokenPricesTable}
                WHERE base_address IS NOT NULL AND base_address != ''
                ORDER BY token_id
                LIMIT :limit OFFSET :offset
            `;

            const result = await this.postgres.sequelize.query(query, {
                type: QueryTypes.SELECT,
                replacements: { limit, offset }
            });

            return result as any[];

        } catch (error) {
            Logger.error(`ValidateBaseAddressMigration::getTokensFromPostgreSQLBatch::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getTokensFromClickHouse(tokenIds: string[]): Promise<any[]> {
        try {
            const client = this.clickhouseClient.getClient();

            // Check if base_address column exists first
            const checkColumnQuery = `
                SELECT name
                FROM system.columns
                WHERE database = '${this.chConfig.database}'
                AND table = '${this.tokenPricesTable}'
                AND name = 'base_address'
            `;

            const columnResult = await client.query({
                query: checkColumnQuery,
                format: 'JSONEachRow'
            });

            const columnRows: any = await columnResult.json();

            if (columnRows.length === 0) {
                Logger.warn(`ValidateBaseAddressMigration::getTokensFromClickHouse::base_address column does not exist in ClickHouse`);
                return [];
            }

            // Handle empty token list
            if (tokenIds.length === 0) {
                return [];
            }

            // Process in smaller chunks to avoid query length limits
            const chunkSize = 100;
            let allResults: any[] = [];

            for (let i = 0; i < tokenIds.length; i += chunkSize) {
                const chunk = tokenIds.slice(i, i + chunkSize);

                // Properly escape token IDs and create IN clause
                const escapedTokenIds = chunk.map(id => {
                    // Escape single quotes and backslashes
                    const escaped = id.replace(/\\/g, '\\\\').replace(/'/g, "\\'");
                    return `'${escaped}'`;
                });

                const tokenIdList = escapedTokenIds.join(',');

                const query = `
                    SELECT token_id, base_address
                    FROM ${this.chConfig.database}.${this.tokenPricesTable}
                    WHERE token_id IN (${tokenIdList})
                    ORDER BY token_id
                `;

                Logger.debug(`ValidateBaseAddressMigration::getTokensFromClickHouse::Querying chunk ${Math.floor(i / chunkSize) + 1} with ${chunk.length} tokens`);

                const result = await client.query({
                    query: query,
                    format: 'JSONEachRow'
                });

                const chunkResults = await result.json();
                allResults = allResults.concat(chunkResults);

                // Small delay between chunks to avoid overwhelming ClickHouse
                if (i + chunkSize < tokenIds.length) {
                    await this.sleep(50);
                }
            }

            return allResults;

        } catch (error) {
            Logger.error(`ValidateBaseAddressMigration::getTokensFromClickHouse::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private compareTokenData(pgTokens: any[], chTokens: any[]): ValidationResult[] {
        const results: ValidationResult[] = [];

        // Create a map of ClickHouse tokens for quick lookup
        const chTokenMap = new Map();
        chTokens.forEach(token => {
            chTokenMap.set(token.token_id, token.base_address);
        });

        // Compare each PostgreSQL token with its ClickHouse counterpart
        pgTokens.forEach(pgToken => {
            const chBaseAddress = chTokenMap.get(pgToken.token_id) || null;
            const matches = pgToken.base_address === chBaseAddress;

            results.push({
                token_id: pgToken.token_id,
                pg_base_address: pgToken.base_address,
                ch_base_address: chBaseAddress,
                matches: matches
            });
        });

        return results;
    }

    private reportValidationResults(results: ValidationResult[]): void {
        const totalTokens = results.length;
        const matchingTokens = results.filter(r => r.matches).length;
        const mismatchedTokens = results.filter(r => !r.matches);

        Logger.info(`\n=== VALIDATION RESULTS ===`);
        Logger.info(`Total tokens validated: ${totalTokens}`);
        Logger.info(`Matching tokens: ${matchingTokens}`);
        Logger.info(`Mismatched tokens: ${mismatchedTokens.length}`);
        Logger.info(`Success rate: ${((matchingTokens / totalTokens) * 100).toFixed(2)}%`);

        if (mismatchedTokens.length > 0) {
            Logger.warn(`\n=== MISMATCHED TOKENS ===`);
            mismatchedTokens.slice(0, 10).forEach(token => { // Show first 10 mismatches
                Logger.warn(`Token ID: ${token.token_id}`);
                Logger.warn(`  PostgreSQL base_address: ${token.pg_base_address}`);
                Logger.warn(`  ClickHouse base_address: ${token.ch_base_address}`);
                Logger.warn(`---`);
            });

            if (mismatchedTokens.length > 10) {
                Logger.warn(`... and ${mismatchedTokens.length - 10} more mismatched tokens`);
            }
        }

        // Summary statistics
        const missingInClickHouse = results.filter(r => r.pg_base_address && !r.ch_base_address).length;
        const extraInClickHouse = results.filter(r => !r.pg_base_address && r.ch_base_address).length;
        const differentValues = results.filter(r => r.pg_base_address && r.ch_base_address && r.pg_base_address !== r.ch_base_address).length;

        Logger.info(`\n=== DETAILED STATISTICS ===`);
        Logger.info(`Missing in ClickHouse: ${missingInClickHouse}`);
        Logger.info(`Extra in ClickHouse: ${extraInClickHouse}`);
        Logger.info(`Different values: ${differentValues}`);

        if (matchingTokens === totalTokens) {
            Logger.info(`\n✅ VALIDATION PASSED: All tokens have matching base addresses!`);
        } else {
            Logger.warn(`\n❌ VALIDATION FAILED: ${mismatchedTokens.length} tokens have mismatched base addresses.`);
        }
    }

    private reportBatchValidationResults(totalProcessed: number, totalMatching: number, totalMismatched: number, allMismatches: ValidationResult[]): void {
        Logger.info(`\n=== BATCH VALIDATION RESULTS ===`);
        Logger.info(`Total tokens validated: ${totalProcessed}`);
        Logger.info(`Matching tokens: ${totalMatching}`);
        Logger.info(`Mismatched tokens: ${totalMismatched}`);
        Logger.info(`Success rate: ${((totalMatching / totalProcessed) * 100).toFixed(2)}%`);

        if (totalMismatched > 0) {
            Logger.warn(`\n=== SAMPLE MISMATCHED TOKENS ===`);
            allMismatches.slice(0, 20).forEach(token => { // Show first 20 mismatches
                Logger.warn(`Token ID: ${token.token_id}`);
                Logger.warn(`  PostgreSQL base_address: ${token.pg_base_address}`);
                Logger.warn(`  ClickHouse base_address: ${token.ch_base_address}`);
                Logger.warn(`---`);
            });

            if (totalMismatched > 20) {
                Logger.warn(`... and ${totalMismatched - 20} more mismatched tokens`);
            }
        }

        // Summary statistics
        const missingInClickHouse = allMismatches.filter(r => r.pg_base_address && !r.ch_base_address).length;
        const extraInClickHouse = allMismatches.filter(r => !r.pg_base_address && r.ch_base_address).length;
        const differentValues = allMismatches.filter(r => r.pg_base_address && r.ch_base_address && r.pg_base_address !== r.ch_base_address).length;

        Logger.info(`\n=== DETAILED STATISTICS ===`);
        Logger.info(`Missing in ClickHouse: ${missingInClickHouse}`);
        Logger.info(`Extra in ClickHouse: ${extraInClickHouse}`);
        Logger.info(`Different values: ${differentValues}`);

        if (totalMatching === totalProcessed) {
            Logger.info(`\n✅ BATCH VALIDATION PASSED: All tokens have matching base addresses!`);
        } else {
            Logger.warn(`\n❌ BATCH VALIDATION FAILED: ${totalMismatched} tokens have mismatched base addresses.`);
        }
    }

    private async sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Execute the validation
async function main() {
    try {
        const validation = new ValidateBaseAddressMigration();
        await validation.perform();
        process.exit(0);
    } catch (error) {
        Logger.error(`Main::Error: ${JSON.stringify(error)}`);
        process.exit(1);
    }
}

main();
