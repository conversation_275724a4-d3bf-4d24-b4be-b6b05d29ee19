import Logger from '../../lib/Logger';
import Postgres from '../../lib/dataStores/destination/Postgres';
import ClickhouseClient from '../../lib/dataStores/destination/ClickhouseClient';
import Constant from '../../config/Constant';
import { ClickHouseConfig } from '../../lib/Types';
import { QueryTypes } from 'sequelize';

// Simple command line argument parsing
const args = process.argv.slice(2);
const getLimit = (): number => {
    const limitIndex = args.findIndex(arg => arg === '--limit' || arg === '-l');
    if (limitIndex !== -1 && args[limitIndex + 1]) {
        return parseInt(args[limitIndex + 1]) || 100;
    }
    return 100;
};

const options = {
    limit: getLimit()
};

interface ValidationResult {
    token_id: string;
    pg_base_address: string | null;
    ch_base_address: string | null;
    matches: boolean;
}

class ValidateBaseAddressMigration {
    private limit: number;
    private postgres: Postgres;
    private chConfig: ClickHouseConfig;
    private clickhouseClient: ClickhouseClient;
    private priceOracleSchema: string = 'price_oracle';
    private tokenPricesTable: string = 'token_prices';

    constructor() {
        this.limit = options.limit;
        this.postgres = Postgres.getClient(Constant.dappDatabaseConnectionUrl);
        this.chConfig = {
            host: Constant.clickhouseHost,
            username: Constant.clickhouseUsername,
            password: Constant.clickhousePassword,
            database: Constant.clickhouseDatabasePriceOracle,
            max_open_connections: Constant.clickhouseMaxOpenConnection,
        };
        this.clickhouseClient = new ClickhouseClient(this.chConfig);
    }

    public async perform(): Promise<void> {
        try {
            Logger.info(`ValidateBaseAddressMigration::perform::Starting validation with limit: ${this.limit}`);

            // Get sample tokens with base addresses from PostgreSQL
            const pgTokens = await this.getTokensFromPostgreSQL();
            Logger.info(`ValidateBaseAddressMigration::perform::Found ${pgTokens.length} tokens in PostgreSQL`);

            if (pgTokens.length === 0) {
                Logger.info(`ValidateBaseAddressMigration::perform::No tokens found in PostgreSQL. Exiting.`);
                return;
            }

            // Get corresponding tokens from ClickHouse
            const chTokens = await this.getTokensFromClickHouse(pgTokens.map(t => t.token_id));
            Logger.info(`ValidateBaseAddressMigration::perform::Found ${chTokens.length} tokens in ClickHouse`);

            // Compare the data
            const validationResults = this.compareTokenData(pgTokens, chTokens);
            
            // Report results
            this.reportValidationResults(validationResults);

        } catch (error) {
            Logger.error(`ValidateBaseAddressMigration::perform::Error during validation: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getTokensFromPostgreSQL(): Promise<any[]> {
        try {
            const query = `
                SELECT token_id, base_address 
                FROM ${this.priceOracleSchema}.${this.tokenPricesTable} 
                WHERE base_address IS NOT NULL AND base_address != ''
                ORDER BY token_id
                LIMIT :limit
            `;

            const result = await this.postgres.sequelize.query(query, { 
                type: QueryTypes.SELECT,
                replacements: { limit: this.limit }
            });

            return result as any[];

        } catch (error) {
            Logger.error(`ValidateBaseAddressMigration::getTokensFromPostgreSQL::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private async getTokensFromClickHouse(tokenIds: string[]): Promise<any[]> {
        try {
            const client = this.clickhouseClient.getClient();
            
            // Check if base_address column exists first
            const checkColumnQuery = `
                SELECT name 
                FROM system.columns 
                WHERE database = '${this.chConfig.database}' 
                AND table = '${this.tokenPricesTable}' 
                AND name = 'base_address'
            `;

            const columnResult = await client.query({
                query: checkColumnQuery,
                format: 'JSONEachRow'
            });

            const columnRows = await columnResult.json();
            
            if (columnRows.length === 0) {
                Logger.warn(`ValidateBaseAddressMigration::getTokensFromClickHouse::base_address column does not exist in ClickHouse`);
                return [];
            }

            // Create a comma-separated list of token IDs for the IN clause
            const tokenIdList = tokenIds.map(id => `'${id}'`).join(',');
            
            const query = `
                SELECT token_id, base_address 
                FROM ${this.chConfig.database}.${this.tokenPricesTable} 
                WHERE token_id IN (${tokenIdList})
                ORDER BY token_id
            `;

            const result = await client.query({
                query: query,
                format: 'JSONEachRow'
            });

            return await result.json();

        } catch (error) {
            Logger.error(`ValidateBaseAddressMigration::getTokensFromClickHouse::Error: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    private compareTokenData(pgTokens: any[], chTokens: any[]): ValidationResult[] {
        const results: ValidationResult[] = [];
        
        // Create a map of ClickHouse tokens for quick lookup
        const chTokenMap = new Map();
        chTokens.forEach(token => {
            chTokenMap.set(token.token_id, token.base_address);
        });

        // Compare each PostgreSQL token with its ClickHouse counterpart
        pgTokens.forEach(pgToken => {
            const chBaseAddress = chTokenMap.get(pgToken.token_id) || null;
            const matches = pgToken.base_address === chBaseAddress;

            results.push({
                token_id: pgToken.token_id,
                pg_base_address: pgToken.base_address,
                ch_base_address: chBaseAddress,
                matches: matches
            });
        });

        return results;
    }

    private reportValidationResults(results: ValidationResult[]): void {
        const totalTokens = results.length;
        const matchingTokens = results.filter(r => r.matches).length;
        const mismatchedTokens = results.filter(r => !r.matches);

        Logger.info(`\n=== VALIDATION RESULTS ===`);
        Logger.info(`Total tokens validated: ${totalTokens}`);
        Logger.info(`Matching tokens: ${matchingTokens}`);
        Logger.info(`Mismatched tokens: ${mismatchedTokens.length}`);
        Logger.info(`Success rate: ${((matchingTokens / totalTokens) * 100).toFixed(2)}%`);

        if (mismatchedTokens.length > 0) {
            Logger.warn(`\n=== MISMATCHED TOKENS ===`);
            mismatchedTokens.slice(0, 10).forEach(token => { // Show first 10 mismatches
                Logger.warn(`Token ID: ${token.token_id}`);
                Logger.warn(`  PostgreSQL base_address: ${token.pg_base_address}`);
                Logger.warn(`  ClickHouse base_address: ${token.ch_base_address}`);
                Logger.warn(`---`);
            });

            if (mismatchedTokens.length > 10) {
                Logger.warn(`... and ${mismatchedTokens.length - 10} more mismatched tokens`);
            }
        }

        // Summary statistics
        const missingInClickHouse = results.filter(r => r.pg_base_address && !r.ch_base_address).length;
        const extraInClickHouse = results.filter(r => !r.pg_base_address && r.ch_base_address).length;
        const differentValues = results.filter(r => r.pg_base_address && r.ch_base_address && r.pg_base_address !== r.ch_base_address).length;

        Logger.info(`\n=== DETAILED STATISTICS ===`);
        Logger.info(`Missing in ClickHouse: ${missingInClickHouse}`);
        Logger.info(`Extra in ClickHouse: ${extraInClickHouse}`);
        Logger.info(`Different values: ${differentValues}`);

        if (matchingTokens === totalTokens) {
            Logger.info(`\n✅ VALIDATION PASSED: All tokens have matching base addresses!`);
        } else {
            Logger.warn(`\n❌ VALIDATION FAILED: ${mismatchedTokens.length} tokens have mismatched base addresses.`);
        }
    }
}

// Execute the validation
async function main() {
    try {
        const validation = new ValidateBaseAddressMigration();
        await validation.perform();
        process.exit(0);
    } catch (error) {
        Logger.error(`Main::Error: ${JSON.stringify(error)}`);
        process.exit(1);
    }
}

main();
