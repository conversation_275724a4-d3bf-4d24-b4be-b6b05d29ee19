# Update Base Address to ClickHouse - One Timer Script

## Overview

This one-timer script updates base addresses of tokens from PostgreSQL to ClickHouse in the price_oracle schema. It migrates the `base_address` field from the PostgreSQL `price_oracle.token_prices` table to the corresponding ClickHouse table.

## Features

- **Automatic Column Creation**: Adds `base_address` and `solana_address` columns to ClickHouse if they don't exist
- **Batch Processing**: Processes records in configurable batches for efficiency
- **Dry Run Mode**: Test the migration without making actual changes
- **Progress Tracking**: Shows detailed progress with batch information
- **Error Handling**: Continues processing even if individual records fail
- **Logging**: Comprehensive logging for monitoring and debugging

## Prerequisites

1. PostgreSQL database with `price_oracle.token_prices` table containing `base_address` field
2. ClickHouse database with `price_oracle.token_prices` table
3. Proper database connection configurations in environment variables
4. Node.js and TypeScript environment set up

## Usage

### Basic Usage

```bash
# Run with default settings (batch size: 1000)
npx ts-node src/executables/oneTimers/UpdateBaseAddressToCH.ts

# Run with custom batch size
npx ts-node src/executables/oneTimers/UpdateBaseAddressToCH.ts --batchSize 500

# Run in dry-run mode (no actual updates)
npx ts-node src/executables/oneTimers/UpdateBaseAddressToCH.ts --dryRun

# Combine options
npx ts-node src/executables/oneTimers/UpdateBaseAddressToCH.ts --batchSize 2000 --dryRun
```

### Command Line Options

- `--batchSize <number>`: Number of records to process in each batch (default: 1000)
- `--dryRun`: Run in dry-run mode without making actual updates (default: false)

## What the Script Does

1. **Schema Validation**: Checks and adds missing columns (`base_address`, `solana_address`) to ClickHouse table
2. **Data Discovery**: Counts total tokens with base addresses in PostgreSQL
3. **Batch Processing**: Processes tokens in configurable batches
4. **Data Migration**: Updates ClickHouse records with base addresses from PostgreSQL
5. **Progress Reporting**: Provides detailed progress information

## Database Schema Updates

The script automatically updates the ClickHouse schema to include:

- `base_address` column (Nullable String)
- `solana_address` column (Nullable String)

These columns are also added to the TypeScript schema definitions in:
- `src/app/services/coingecko/PriceOracleMigration.ts`
- `src/app/model/clickhouse/TokenPricesCH.ts`

## Environment Variables Required

The script uses the following environment variables (configured in Constant.ts):

- `DAPP_PG_USER`: PostgreSQL username
- `DAPP_PG_PASSWORD`: PostgreSQL password
- `DAPP_PG_HOST`: PostgreSQL host
- `DAPP_PG_PORT`: PostgreSQL port
- `DAPP_PG_DAPP_DB`: PostgreSQL database name
- `CLICKHOUSE_HOST`: ClickHouse host
- `CLICKHOUSE_USERNAME`: ClickHouse username
- `CLICKHOUSE_PASSWORD`: ClickHouse password
- `CLICKHOUSE_DATABASE_PRICE_ORACLE`: ClickHouse price oracle database name

## Example Output

```
UpdateBaseAddressToCH::perform::Starting base address migration
UpdateBaseAddressToCH::perform::Batch size: 1000
UpdateBaseAddressToCH::ensureBaseAddressColumn::base_address column already exists
UpdateBaseAddressToCH::ensureBaseAddressColumn::solana_address column already exists
UpdateBaseAddressToCH::perform::Total tokens with base address: 5432
UpdateBaseAddressToCH::perform::Processing batch 1, records 1-1000 of 5432
UpdateBaseAddressToCH::perform::Batch completed. Updated 1000 records. Total processed: 1000, Total updated: 1000
UpdateBaseAddressToCH::perform::Processing batch 2, records 1001-2000 of 5432
...
UpdateBaseAddressToCH::perform::Migration completed successfully!
UpdateBaseAddressToCH::perform::Total processed: 5432, Total updated: 5432
```

## Error Handling

- Individual record failures don't stop the entire migration
- Detailed error logging for troubleshooting
- Graceful handling of database connection issues
- Validation of required columns before processing

## Performance Considerations

- Default batch size of 1000 records balances performance and memory usage
- Small delay (100ms) between batches to avoid overwhelming databases
- Uses efficient ClickHouse ALTER TABLE UPDATE mutations
- Proper SQL escaping to prevent injection issues

## Safety Features

- Dry-run mode for testing
- Non-destructive operations (only adds/updates data)
- Comprehensive logging for audit trails
- Batch processing to handle large datasets

## Troubleshooting

1. **Connection Issues**: Verify environment variables and database connectivity
2. **Permission Issues**: Ensure database users have required permissions
3. **Column Missing**: Script automatically adds missing columns
4. **Large Datasets**: Adjust batch size for better performance
5. **Memory Issues**: Reduce batch size if encountering memory problems

## Notes

- This is a one-time migration script
- Safe to run multiple times (idempotent)
- Only processes tokens that have non-null, non-empty base addresses
- Uses ClickHouse mutations which may take time to complete on large tables
