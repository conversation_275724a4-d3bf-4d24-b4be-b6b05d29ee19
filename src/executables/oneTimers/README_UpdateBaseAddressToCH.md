# Update Base Address to ClickHouse - One Timer Script

## Overview

This one-timer script updates base addresses of tokens from PostgreSQL to ClickHouse in the price_oracle schema. It migrates the `base_address` field from the PostgreSQL `price_oracle.token_prices` table to the corresponding ClickHouse table.

## Features

- **Automatic Column Creation**: Adds `base_address` and `solana_address` columns to ClickHouse if they don't exist
- **Batch Processing**: Processes records in configurable batches for efficiency
- **Dry Run Mode**: Test the migration without making actual changes
- **Progress Tracking**: Shows detailed progress with batch information
- **Error Handling**: Continues processing even if individual records fail
- **Logging**: Comprehensive logging for monitoring and debugging

## Prerequisites

1. PostgreSQL database with `price_oracle.token_prices` table containing `base_address` field
2. ClickHouse database with `price_oracle.token_prices` table
3. Proper database connection configurations in environment variables
4. Node.js and TypeScript environment set up

## Usage

### Using NPM Scripts (Recommended)

```bash
# Build the project first
npm run build

# Run with default settings (batch size: 1000)
npm run onetimer:UpdateBaseAddressToCH

# Run with custom batch size
npm run onetimer:UpdateBaseAddressToCH -- --batchSize 500

# Run in dry-run mode (no actual updates)
npm run onetimer:UpdateBaseAddressToCH -- --dryRun

# Combine options
npm run onetimer:UpdateBaseAddressToCH -- --batchSize 2000 --dryRun

# Validate the migration
npm run onetimer:ValidateBaseAddressMigration

# Validate with custom limit
npm run onetimer:ValidateBaseAddressMigration -- --limit 200

# Validate ALL tokens (processes in batches)
npm run onetimer:ValidateBaseAddressMigration -- --all

# Resume migration from a specific offset (useful for large datasets)
npm run onetimer:UpdateBaseAddressToCH -- --startOffset 50000 --batchSize 500

# Process only a specific number of records
npm run onetimer:UpdateBaseAddressToCH -- --maxRecords 10000 --batchSize 500
```

### Using TypeScript Directly

```bash
# Run with default settings (batch size: 1000)
npx ts-node src/executables/oneTimers/UpdateBaseAddressToCH.ts

# Run with custom batch size
npx ts-node src/executables/oneTimers/UpdateBaseAddressToCH.ts --batchSize 500

# Run in dry-run mode (no actual updates)
npx ts-node src/executables/oneTimers/UpdateBaseAddressToCH.ts --dryRun

# Combine options
npx ts-node src/executables/oneTimers/UpdateBaseAddressToCH.ts --batchSize 2000 --dryRun

# Resume from specific offset (useful for large datasets)
npx ts-node src/executables/oneTimers/UpdateBaseAddressToCH.ts --startOffset 50000

# Process limited number of records
npx ts-node src/executables/oneTimers/UpdateBaseAddressToCH.ts --maxRecords 10000

# Validate the migration (sample)
npx ts-node src/executables/oneTimers/ValidateBaseAddressMigration.ts --limit 1000

# Validate ALL tokens
npx ts-node src/executables/oneTimers/ValidateBaseAddressMigration.ts --all
```

### Command Line Options

**Migration Script Options:**
- `--batchSize <number>`: Number of records to process in each batch (default: 1000)
- `--dryRun`: Run in dry-run mode without making actual updates (default: false)
- `--startOffset <number>`: Start processing from this record offset (default: 0)
- `--maxRecords <number>`: Maximum number of records to process (default: 0 = no limit)

**Validation Script Options:**
- `--limit <number>`: Number of records to validate in sample mode (default: 1000)
- `--all`: Validate all tokens with base addresses (processes in batches)

## What the Script Does

1. **Schema Validation**: Checks and adds missing columns (`base_address`, `solana_address`) to ClickHouse table
2. **Data Discovery**: Counts total tokens with base addresses in PostgreSQL
3. **Batch Processing**: Processes tokens in configurable batches
4. **Data Migration**: Updates ClickHouse records with base addresses from PostgreSQL
5. **Progress Reporting**: Provides detailed progress information

## Database Schema Updates

The script automatically updates the ClickHouse schema to include:

- `base_address` column (Nullable String)
- `solana_address` column (Nullable String)

These columns are also added to the TypeScript schema definitions in:
- `src/app/services/coingecko/PriceOracleMigration.ts`
- `src/app/model/clickhouse/TokenPricesCH.ts`

## Environment Variables Required

The script uses the following environment variables (configured in Constant.ts):

- `DAPP_PG_USER`: PostgreSQL username
- `DAPP_PG_PASSWORD`: PostgreSQL password
- `DAPP_PG_HOST`: PostgreSQL host
- `DAPP_PG_PORT`: PostgreSQL port
- `DAPP_PG_DAPP_DB`: PostgreSQL database name
- `CLICKHOUSE_HOST`: ClickHouse host
- `CLICKHOUSE_USERNAME`: ClickHouse username
- `CLICKHOUSE_PASSWORD`: ClickHouse password
- `CLICKHOUSE_DATABASE_PRICE_ORACLE`: ClickHouse price oracle database name

## Example Output

```
UpdateBaseAddressToCH::perform::Starting base address migration
UpdateBaseAddressToCH::perform::Batch size: 1000
UpdateBaseAddressToCH::ensureBaseAddressColumn::base_address column already exists
UpdateBaseAddressToCH::ensureBaseAddressColumn::solana_address column already exists
UpdateBaseAddressToCH::perform::Total tokens with base address: 5432
UpdateBaseAddressToCH::perform::Processing batch 1, records 1-1000 of 5432
UpdateBaseAddressToCH::perform::Batch completed. Updated 1000 records. Total processed: 1000, Total updated: 1000
UpdateBaseAddressToCH::perform::Processing batch 2, records 1001-2000 of 5432
...
UpdateBaseAddressToCH::perform::Migration completed successfully!
UpdateBaseAddressToCH::perform::Total processed: 5432, Total updated: 5432
```

## Error Handling

- Individual record failures don't stop the entire migration
- Detailed error logging for troubleshooting
- Graceful handling of database connection issues
- Validation of required columns before processing

## Performance Considerations

- Default batch size of 1000 records balances performance and memory usage
- Sub-batching (50 records per sub-batch) for ClickHouse updates to avoid timeouts
- Small delays between batches to avoid overwhelming databases
- Uses efficient ClickHouse ALTER TABLE UPDATE mutations
- Proper SQL escaping to prevent injection issues
- Progress tracking with ETA calculations
- Resume capability for handling very large datasets

## Handling Large Datasets (100K+ tokens)

For very large datasets, use these strategies:

### 1. Process in Chunks
```bash
# Process first 50,000 records
npm run onetimer:UpdateBaseAddressToCH -- --maxRecords 50000 --batchSize 500

# Resume from record 50,000 for next 50,000
npm run onetimer:UpdateBaseAddressToCH -- --startOffset 50000 --maxRecords 50000 --batchSize 500

# Continue until complete...
```

### 2. Reduce Batch Size for Stability
```bash
# Use smaller batches for better stability
npm run onetimer:UpdateBaseAddressToCH -- --batchSize 250
```

### 3. Monitor Progress
The script provides real-time progress updates including:
- Records processed per second
- Estimated time to completion
- Success/failure rates
- Current batch information

### 4. Validate in Batches
```bash
# Validate all tokens (processes in 1000-record batches)
npm run onetimer:ValidateBaseAddressMigration -- --all
```

## Safety Features

- Dry-run mode for testing
- Non-destructive operations (only adds/updates data)
- Comprehensive logging for audit trails
- Batch processing to handle large datasets

## Troubleshooting

### Quick Diagnosis
First, run the connection test to identify issues:
```bash
npm run build
npm run onetimer:TestClickHouseConnection
```

### Common Issues

1. **SYNTAX_ERROR (Code 62)**
   - Usually caused by special characters in token IDs
   - Fixed in latest version with proper escaping
   - Try running with smaller batch sizes: `--batchSize 100`

2. **Connection Issues**
   - Verify environment variables and database connectivity
   - Check if ClickHouse service is running
   - Verify database and table exist

3. **Permission Issues**
   - Ensure database users have required permissions
   - ClickHouse user needs ALTER TABLE permissions for mutations

4. **Column Missing**
   - Script automatically adds missing columns
   - If manual addition needed: `ALTER TABLE price_oracle.token_prices ADD COLUMN base_address Nullable(String)`

5. **Large Datasets**
   - Use `--startOffset` and `--maxRecords` for chunking
   - Reduce batch size: `--batchSize 250`
   - Monitor ClickHouse server resources

6. **Memory Issues**
   - Reduce batch size if encountering memory problems
   - Use sub-batching (automatically handled)

7. **Query Timeout**
   - Reduce batch size to avoid long-running queries
   - Check ClickHouse server configuration

### Debug Mode
Enable debug logging by setting environment variable:
```bash
export LOG_LEVEL=debug
npm run onetimer:UpdateBaseAddressToCH -- --dryRun --batchSize 10
```

## Notes

- This is a one-time migration script
- Safe to run multiple times (idempotent)
- Only processes tokens that have non-null, non-empty base addresses
- Uses ClickHouse mutations which may take time to complete on large tables
