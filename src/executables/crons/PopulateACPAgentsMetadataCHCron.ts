import PopulateACPAgentsMetadataCH from '../../app/services/virtuals/PopulateACPAgentsMetadataCH';
import Logger from '../../lib/Logger';

/**
 * <PERSON><PERSON> to populate ACP agents metadata from virtuals
 */

class PopulateACPAgentsMetadataCHCron {
    public async perform() {
        await new PopulateACPAgentsMetadataCH({}).perform();
        process.exit(0);
    }
}

const populateACPAgentsMetadataCHCron = new PopulateACPAgentsMetadataCHCron();
populateACPAgentsMetadataCHCron
    .perform()
    .then((rsp) => { })
    .catch((err) => {
        Logger.info(`ExceptionAlerts::PopulateACPAgentsMetadataCHCron::Error: ${err}, Stacktrace: ${err.stack}`);
    });
