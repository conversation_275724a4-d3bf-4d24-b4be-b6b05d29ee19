import Logger from "../../lib/Logger";
import GenesisParticipantsAggregationService from "../../app/services/virtuals/genesis/GenesisParticipantsAggregationService";

class PopulateGenesisParticipantsAggregation {
    public async perform() {
        await new GenesisParticipantsAggregationService({}).run();
        process.exit(0);
    }
}
const populateGenesisParticipantsAggregation = new PopulateGenesisParticipantsAggregation();

populateGenesisParticipantsAggregation
    .perform()
    .then((rsp) => { })
    .catch((err) => {
        Logger.info(`ExceptionAlerts::error::Error occured during populate genesis participants analysis : ${err.message}, Stacktrace: ${err.stack}`);
    });
