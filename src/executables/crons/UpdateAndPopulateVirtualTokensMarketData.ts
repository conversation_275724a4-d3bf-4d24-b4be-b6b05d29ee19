import Logger from '../../lib/Logger';
import SentientTokensMarketDataService from '../../app/services/nonCoingecko/SentientTokensMarketDataService';
import PrototypeTokensMarketDataService from '../../app/services/nonCoingecko/PrototypeTokensMarketDataService';

/**
 * <PERSON><PERSON> to Populate and update Virtuals tokens (Genesis, Sentient and Prototype) historical, extended historical and token prices table data.
 */
class UpdateAndPopulateVirtualTokensMarketData {
    public async perform() {

        Logger.info(`\x1b[35m\x1b[1m+------------------------ START SENTIENT / GENESIS TOKENS MARKET DATA SERVICE ------------------------+\x1b[0m`);
        await new SentientTokensMarketDataService({}).perform();
        Logger.info(`\x1b[35m\x1b[1m+------------------------ END SENTIENT / GENESIS TOKENS MARKET DATA SERVICE ------------------------+\x1b[0m`);

        Logger.info(`\x1b[35m\x1b[1m+------------------------ START PROTOTYPE TOKENS MARKET DATA SERVICE ------------------------+\x1b[0m`);
        await new PrototypeTokensMarketDataService({}).perform();
        Logger.info(`\x1b[35m\x1b[1m+------------------------ END PROTOTYPE TOKENS MARKET DATA SERVICE ------------------------+\x1b[0m`);

        process.exit(0);
    }
}

const populatePrototypeTokenDailyData = new UpdateAndPopulateVirtualTokensMarketData();
populatePrototypeTokenDailyData
    .perform()
    .then((rsp) => { })
    .catch((err) => {
        Logger.info(`ExceptionAlerts::PopulatePrototypeTokenDailyDataService::Error: ${err}, Stacktrace: ${err.stack}`);
    });
