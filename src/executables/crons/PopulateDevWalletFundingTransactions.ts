import Logger from "../../lib/Logger";
import DevWalletFundingTransactionsService from "../../app/services/virtuals/DevWalletFundingTransactionsService";
import { Command } from "commander";

const command = new Command();
command
    .option('-s, --schema <string>', 'Schema name', String) // Possible values: virtual_ai_agents_prototype, virtual_ai_agents_old, virtual_ai_agents_migrated, virtuals_genesis_agents, virtuals_ethereum_agents
command.parse(process.argv);

const params = {
    schema: command.schema
}

class PopulateDevWalletFundingTransactions {
    public async perform() {
        await new DevWalletFundingTransactionsService({ schema: params.schema }).run();
        process.exit(0);
    }
}

const populateDevWalletFundingTransactions = new PopulateDevWalletFundingTransactions();

populateDevWalletFundingTransactions
    .perform()
    .then((rsp) => { })
    .catch((err) => {
        Logger.info(
            `ExceptionAlerts::error::Error occured during populate dev wallet funding transactions : ${err.message}, Stacktrace: ${err.stack}`
        );
    });
