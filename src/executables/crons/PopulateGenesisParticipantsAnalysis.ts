import Logger from "../../lib/Logger";
import GenesisParticipantsAnalysisService from "../../app/services/virtuals/genesis/GenesisParticipantsAnalysisService";

class PopulateGenesisParticipantsAnalysis {
    public async perform() {
        await new GenesisParticipantsAnalysisService({}).run();
        process.exit(0);
    }
}
const populateGenesisParticipantsAnalysis = new PopulateGenesisParticipantsAnalysis();

populateGenesisParticipantsAnalysis
    .perform()
    .then((rsp) => { })
    .catch((err) => {
        Logger.info(`ExceptionAlerts::error::Error occured during populate genesis participants analysis : ${err.message}, Stacktrace: ${err.stack}`);
    });
